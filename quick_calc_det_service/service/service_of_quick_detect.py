# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/29 9:02 
# @Description  : QuickCalculationService.py
import time
import traceback
import json

import cv2
import numpy as np

from base_common.service.service_of_base import BaseModelService
from quick_calc_det_service.model import dbnet_interface
from base_common import LoggerFactory, ImageUtil, Constants, MissionMode

log = LoggerFactory.get_logger('QuickCalculationService')

def calc_iou(bbox1, bbox2):
    if not isinstance(bbox1, np.ndarray):
        bbox1 = np.array(bbox1)
    if not isinstance(bbox2, np.ndarray):
        bbox2 = np.array(bbox2)
        
    xmin1, ymin1, xmax1, ymax1, = np.split(bbox1, 4, axis=-1)
    xmin2, ymin2, xmax2, ymax2, = np.split(bbox2, 4, axis=-1)

    area1 = (xmax1 - xmin1) * (ymax1 - ymin1)
    area2 = (xmax2 - xmin2) * (ymax2 - ymin2)

    ymin = np.maximum(ymin1, np.squeeze(ymin2, axis=-1))
    xmin = np.maximum(xmin1, np.squeeze(xmin2, axis=-1))
    ymax = np.minimum(ymax1, np.squeeze(ymax2, axis=-1))
    xmax = np.minimum(xmax1, np.squeeze(xmax2, axis=-1))

    h = np.maximum(ymax - ymin, 0)
    w = np.maximum(xmax - xmin, 0)
    intersect = h * w

    iou_1 = intersect / area1
    iou_2 = intersect / area2.reshape(1, -1)
    ious_ = np.stack((iou_1, iou_2), 0)
    ious = ious_.max(0)
    return ious

class QuickDetectService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.QUICK_CALC_DET_MISSION
        model_path = f"{Constants.MODEL_WEIGHT_PATH}/quick_calc_det_service/quickcal_dbnet.pth"
        self.dbnet_detector = dbnet_interface.Dbnet_Interface(model_path)

    def do_post(self, data_json):
        try:
            img_photo_cv2 = self.get_image(data_json)
            
            ho,wo,_ = img_photo_cv2.shape
            data_json.pop('img_key')
            data_json.pop('img_type')
            resp = {}
            for item_id in data_json:
                item = data_json[item_id]
                if isinstance(item, str):
                    item_json = json.loads(item)
                else:
                    item_json = item
                item_box = item_json['box']
                log.info(f'{item_box}')
                b0 = item_box[0]
                b1 = item_box[1]
                b2 = item_box[2]
                b3 = item_box[3]
                
                crop_b0 = max(0,b0-100)
                crop_b1 = max(0,b1-100)
                crop_b2 = min(wo,b2+100)
                crop_b3 = min(ho,b3+100)
                
                new_b0 = b0 - crop_b0
                new_b1 = b1 - crop_b1
                new_b2 = b2 - crop_b0
                new_b3 = b3 - crop_b1
                
                item_img_photo = img_photo_cv2[crop_b1:crop_b3, crop_b0:crop_b2, :]
                boxes_list = self.dbnet_detector.detect_img(item_img_photo)
                if len(boxes_list) == 0:
                    log.info(f"{item_id}检测不到....")
                    continue
                    
                boxes_list_rec = []
                widths = []
                heights = []
                for box in boxes_list:
                    x1 = np.array(box)[:,0].min()
                    y1 = np.array(box)[:,1].min()
                    x2 = np.array(box)[:,0].max()
                    y2 = np.array(box)[:,1].max()
                    boxes_list_rec.append([x1,y1,x2,y2])
                    widths.append(box[1][0]-box[0][0])
                    heights.append(box[3][1]-box[0][1])
                ious = calc_iou([[new_b0,new_b1,new_b2,new_b3]],boxes_list_rec)[0]
                width_mean = np.array(widths).mean()
                height_mean = np.array(heights).mean()
                
                boxes_list_rest = []
                for ii,iou in enumerate(ious):
                    if iou > 0.2:
                        ww = boxes_list[ii][1][0] - boxes_list[ii][0][0]
                        hh = boxes_list[ii][3][1] - boxes_list[ii][0][1]
                        
                        if ww < width_mean * 0.2 or hh < height_mean * 0.2:
                            continue
                        
                        new_b = []
                        for point in boxes_list[ii]:
                            new_b.append([point[0]-(b0 - crop_b0),point[1]-(b1 - crop_b1)])
                        boxes_list_rest.append(new_b)

                #item_img_photo = img_photo_cv2[b1:b3, b0:b2, :]
                #boxes_list = self.dbnet_detector.detect_img(item_img_photo)
                
                h, w, c = item_img_photo.shape
                if len(boxes_list_rest) == 0:
                    log.info(f"{item_id}检测不到....")
                    continue
                # 对分割的框进行左右适当扩充，提高识别的精度，系数0.95和0.05可以调整
                new_boxes_list = []
                for bb in boxes_list_rest:
                    xmin = 100000
                    xmax = 0
                    for p in bb:
                        if p[0] < xmin:
                            xmin = p[0]
                        if p[0] > xmax:
                            xmax = p[0]
                    width = xmax - xmin
                    new_bb = []
                    for p in bb:
                        if p[0] - xmin > 0.97 * width:
                            new_bb.append([min(w, int(p[0] + p[0] - xmin - 0.97 * width)), int(p[1])])
                        elif p[0] - xmin < 0.03 * width:
                            new_bb.append([max(0, int(p[0] - (0.03 * width - p[0] + xmin))), int(p[1])])
                        else:
                            new_bb.append([int(p_p) for p_p in p])
                    new_boxes_list.append(new_bb)
                if len(new_boxes_list) > 0:
                    resp[item_id] = new_boxes_list
            return resp
        except:
            log.error(f"{traceback.format_exc()}")
            return {}