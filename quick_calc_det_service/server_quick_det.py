# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 8:44 
# @Description  : server_quick.py
import sys

from base_common import Context, LoggerFactory, Constants, ParamLoader
from quick_calc_det_service.service.service_of_quick_detect import QuickDetectService
log = LoggerFactory.get_logger('QuickDetectService')
if __name__ == '__main__':
    args = sys.argv[1:]
    params = ParamLoader(args)
    if not params.is_success():
        log.error('Usage: python xx.py <port> <gpu_num>')
        exit(1)

    Context.setup()
    Constants.setup(Context.is_product())
    Context.set_gpu_num(params.get_gpu_num())
    service = QuickDetectService()
    service.set_stop_time(params.get_stop_time())
    service.start(params.get_port())

