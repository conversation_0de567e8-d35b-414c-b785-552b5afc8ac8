# -*- coding: utf-8 -*-
# @Time    : 2019/8/23 21:58
# <AUTHOR> zhoujun
import time
import os
import cv2
import shutil
import torch
import torchvision.utils as vutils
from tqdm import tqdm

from base import BaseTrainer
from utils import WarmupPolyLR, runningScore, cal_text_score,WarmupMultiStepLR,draw_result,get_metric


class Trainer(BaseTrainer):
    def __init__(self, config, model, criterion, train_loader, validate_loader, metric_cls, post_process=None):
        super(Trainer, self).__init__(config, model, criterion)
        self.show_images_iter = self.config['trainer']['show_images_iter']
        self.cls_list = self.config['cls_list']
        self.cls_weight = self.config['cls_weight']
        self.train_loader = train_loader
        if validate_loader is not None:
            assert post_process is not None and metric_cls is not None
        self.validate_loader = validate_loader
        self.post_process = post_process
        self.metric_cls = metric_cls
        self.train_loader_len = len(train_loader)
        if self.config['lr_scheduler']['type'] == 'WarmupPolyLR':
            warmup_iters = config['lr_scheduler']['args']['warmup_epoch'] * self.train_loader_len
            if self.start_epoch > 1:
                self.config['lr_scheduler']['args']['last_epoch'] = (self.start_epoch - 1) * self.train_loader_len
            self.scheduler = WarmupPolyLR(self.optimizer, max_iters=self.epochs * self.train_loader_len,
                                          warmup_iters=warmup_iters, **config['lr_scheduler']['args'])
#         if self.config['lr_scheduler']['type'] == 'WarmupMultiStepLR':
#             warmup_iters = config['lr_scheduler']['args']['warmup_epoch'] * self.train_loader_len
#             if self.start_epoch > 1:
#                 self.config['lr_scheduler']['args']['last_epoch'] = (self.start_epoch - 1) * self.train_loader_len
#             self.scheduler = WarmupMultiStepLR(self.optimizer,warmup_iters=warmup_iters, **config['lr_scheduler']['args'])
           
#         self.scheduler = torch.optim.lr_scheduler.MultiStepLR(self.optimizer, milestones=self.config['lr_scheduler']['args']['milestones'], gamma=0.1)
        if self.validate_loader is not None:
            self.logger_info(
                'train dataset has {} samples,{} in dataloader, validate dataset has {} samples,{} in dataloader'.format(
                    len(self.train_loader.dataset), self.train_loader_len, len(self.validate_loader.dataset), len(self.validate_loader)))
        else:
            self.logger_info('train dataset has {} samples,{} in dataloader'.format(len(self.train_loader.dataset), self.train_loader_len))
            
        self.vis_dir = './vis_eval/'
        if os.path.exists(self.vis_dir):
            shutil.rmtree(self.vis_dir)
        os.mkdir(self.vis_dir)
        
        metric_cls1 = get_metric(config['metric'])
        metric_cls2 = get_metric(config['metric'])
        metric_cls3= get_metric(config['metric'])
        metric_cls4 = get_metric(config['metric'])
        self.metric_clses = [metric_cls1,metric_cls2,metric_cls3,metric_cls4]
            
        self.metric_cls_all = get_metric(config['metric'])
        
        self.big_loss_dir = './big_loss/'
        if os.path.exists(self.big_loss_dir):
            shutil.rmtree(self.big_loss_dir)
        os.mkdir(self.big_loss_dir)

    def _train_epoch(self, epoch):
        
#         self._eval(epoch)
#         raise
        
        self.model.train()
        epoch_start = time.time()
        batch_start = time.time()
        train_loss = 0.
        running_metric_text = runningScore(2)
        lr = self.optimizer.param_groups[0]['lr']

        for i, batch in enumerate(self.train_loader):
#             print(batch.keys())
            if i >= self.train_loader_len:
                break
            self.global_step += 1
            lr = self.optimizer.param_groups[0]['lr']

            # 数据进行转换和丢到gpu
            for key, value in batch.items():
                if value is not None:
                    if isinstance(value, torch.Tensor):
                        batch[key] = value.to(self.device)
            cur_batch_size = batch['img'].size()[0]
#             print('batch: ',batch.keys())
            preds = self.model(batch['img'])
            loss_dict = self.criterion(preds, batch, self.cls_list,self.cls_weight)
        
            big_loss_record = loss_dict['big_loss_record']
        
#             print('loss_dict: ',loss_dict)

            # backward
            self.optimizer.zero_grad()
            for cls_i,cls_ in enumerate(self.cls_list):
#                 记录训练中loss很大的样本，标注出来进行再次审核
                if len(big_loss_record[cls_]) == 0:
                    continue
                else:
                    for big_loss_id in big_loss_record[cls_]:
                        img_path = batch['img_path'][big_loss_id]
                        with open(os.path.join(self.big_loss_dir,cls_+'.txt'),'a') as ff:
                            ff.write(img_path+'\n')
                
#                 if cls_i == len(self.cls_list)-1:
#                     loss_dict['loss_'+cls_].backward()
#                 else:
#                     loss_dict['loss_'+cls_].backward(retain_graph=True)


            loss_dict['loss'].backward()
            self.optimizer.step()
            if self.config['lr_scheduler']['type'] == 'WarmupPolyLR' or self.config['lr_scheduler']['type'] == 'WarmupMultiStepLR':
                self.scheduler.step()
                
            # acc iou
            score_shrink_map = {'Overall Acc':0,'Mean Acc':0,'FreqW Acc':0,'Mean IoU':0}
            for jj,cls_ in enumerate(self.cls_list):
                score_shrink_map_jj = cal_text_score(preds[jj][:, 0, :, :], batch['shrink_map_'+cls_], batch['shrink_mask_'+cls_], running_metric_text,
                                              thred=self.config['post_processing']['args']['thresh'])
                score_shrink_map['Overall Acc'] +=score_shrink_map_jj['Overall Acc']*(1/len(self.cls_list))
                score_shrink_map['Mean Acc'] +=score_shrink_map_jj['Mean Acc']*(1/len(self.cls_list))
                score_shrink_map['FreqW Acc'] +=score_shrink_map_jj['FreqW Acc']*(1/len(self.cls_list))
                score_shrink_map['Mean IoU'] +=score_shrink_map_jj['Mean IoU']*(1/len(self.cls_list))
                
            
            # loss 和 acc 记录到日志
            loss_str = 'loss: {:.4f}, '.format(loss_dict['loss'].item())
            for idx, (key, value) in enumerate(loss_dict.items()):
                if key == 'big_loss_record':
                    continue
                loss_dict[key] = value.item()
                if key == 'loss':
                    continue
                loss_str += '{}: {:.4f}'.format(key, loss_dict[key])
                if idx < len(loss_dict) - 1:
                    loss_str += ', '

            train_loss += loss_dict['loss']
            acc = score_shrink_map['Mean Acc']
            iou_shrink_map = score_shrink_map['Mean IoU']

            if self.global_step % self.log_iter == 0:
                batch_time = time.time() - batch_start
                self.logger_info(
                    '[{}/{}], [{}/{}], global_step: {}, speed: {:.1f} samples/sec, acc: {:.4f}, iou_shrink_map: {:.4f}, {}, lr:{:.6}, time:{:.2f}'.format(
                        epoch, self.epochs, i + 1, self.train_loader_len, self.global_step, self.log_iter * cur_batch_size / batch_time, acc,
                        iou_shrink_map, loss_str, lr, batch_time))
                batch_start = time.time()

            if self.tensorboard_enable and self.config['local_rank'] == 0:
                # write tensorboard
                for key, value in loss_dict.items():
                    if key == 'big_loss_record':
                        continue
                    self.writer.add_scalar('TRAIN/LOSS/{}'.format(key), value, self.global_step)
                self.writer.add_scalar('TRAIN/ACC_IOU/acc', acc, self.global_step)
                self.writer.add_scalar('TRAIN/ACC_IOU/iou_shrink_map', iou_shrink_map, self.global_step)
                self.writer.add_scalar('TRAIN/lr', lr, self.global_step)
                if self.global_step % self.show_images_iter == 0:
                    # show images on tensorboard
                    self.inverse_normalize(batch['img'])
                    self.writer.add_images('TRAIN/imgs', batch['img'], self.global_step)
                    # shrink_labels and threshold_labels
                    for cls_i,cls_ in enumerate(self.cls_list):
                        shrink_labels = batch['shrink_map_'+cls_]
                        threshold_labels = batch['threshold_map_'+cls_]
                        
#                         big_weights = batch['big_weight_'+cls_]
#                         small_weights = batch['small_weight_'+cls_]
                    
                        shrink_labels[shrink_labels <= 0.5] = 0
                        shrink_labels[shrink_labels > 0.5] = 1
                        
#                         show_label = torch.cat([shrink_labels, threshold_labels,big_weights,small_weights])
                        
                        show_label = torch.cat([shrink_labels, threshold_labels])
                        show_label = vutils.make_grid(show_label.unsqueeze(1), nrow=cur_batch_size, normalize=False, padding=20, pad_value=1)
                        self.writer.add_image('TRAIN/gt_'+cls_, show_label, self.global_step)
                        # model output
                        show_pred = []
                        for kk in range(preds[cls_i].shape[1]):
                            show_pred.append(preds[cls_i][:, kk, :, :])
                        show_pred = torch.cat(show_pred)
                        show_pred = vutils.make_grid(show_pred.unsqueeze(1), nrow=cur_batch_size, normalize=False, padding=20, pad_value=1)
                        self.writer.add_image('TRAIN/preds_'+cls_, show_pred, self.global_step)
#             break
        return {'train_loss': train_loss / self.train_loader_len, 'lr': lr, 'time': time.time() - epoch_start,
                'epoch': epoch}

    def _eval(self, epoch):
        self.model.eval()
        # torch.cuda.empty_cache()  # speed up evaluating after training finished
        raw_metrics = [[],[],[],[]]
        raw_metrics_all = []
        total_frame = 0.0
        total_time = 0.0
        for i, batch in tqdm(enumerate(self.validate_loader), total=len(self.validate_loader), desc='test model'):
            with torch.no_grad():
                # 数据进行转换和丢到gpu
                for key, value in batch.items():
                    if value is not None:
                        if isinstance(value, torch.Tensor):
                            batch[key] = value.to(self.device)
                start = time.time()
                preds = self.model(batch['img'])
                image_input = cv2.imread(batch['img_path'][0])
                boxes_all = []
                scores_all = []
                clses_all = []
                for kk,cls_ in enumerate(self.cls_list):
#                     ['img','for','tab','ver']
#                     if kk != 1:
#                         continue

                    boxes, scores = self.post_process(batch, preds[kk],is_output_polygon=self.metric_cls_all.is_output_polygon)
#                     print(boxes)
                    boxes_all.extend(boxes[0])
                    scores_all.extend(scores[0])
                    clses_all.extend([cls_]*len(boxes[0]))
                    
                    
                    raw_metric_kk = self.metric_clses[kk].validate_measure(batch, (boxes, scores),cls_,self.config['post_processing']['args']['box_thresh'])
                    raw_metric_all = self.metric_cls_all.validate_measure(batch, (boxes, scores),cls_,self.config['post_processing']['args']['box_thresh'])
                    
                    raw_metrics[kk].append(raw_metric_kk)
                    raw_metrics_all.append(raw_metric_all)

                draw_result(self.vis_dir,batch['img_name'][0],image_input,boxes_all,scores_all,clses_all)#可视化结果

                total_frame += batch['img'].size()[0]
                total_time += time.time() - start
#                 break
                
        for kk in range(len(self.cls_list)):
            metrics = self.metric_clses[kk].gather_measure(raw_metrics[kk])
            print(self.cls_list[kk],metrics['recall'].avg,metrics['precision'].avg,metrics['fmeasure'].avg)
            
        metrics = self.metric_cls_all.gather_measure(raw_metrics_all)
        recall = metrics['recall'].avg
        precision = metrics['precision'].avg
        fmeasure = metrics['fmeasure'].avg
        print('all cls together: ',recall,precision,fmeasure)
        
        print('FPS:{}'.format(total_frame / total_time))
        return recall,precision,fmeasure


    def _on_epoch_finish(self):
#         self.scheduler.step()
        self.logger_info('[{}/{}], train_loss: {:.4f}, time: {:.4f}, lr: {}'.format(
            self.epoch_result['epoch'], self.epochs, self.epoch_result['train_loss'], self.epoch_result['time'],
            self.epoch_result['lr']))
        net_save_path = '{}/model_latest.pth'.format(self.checkpoint_dir)
        net_save_path_best = '{}/model_best.pth'.format(self.checkpoint_dir)

        if self.config['local_rank'] == 0:
            self._save_checkpoint(self.epoch_result['epoch'], net_save_path)
            save_best = False
            if self.validate_loader is not None and self.metric_cls is not None:  # 使用f1作为最优模型指标
                recall, precision, hmean = self._eval(self.epoch_result['epoch'])

                if self.tensorboard_enable:
                    self.writer.add_scalar('EVAL/recall', recall, self.global_step)
                    self.writer.add_scalar('EVAL/precision', precision, self.global_step)
                    self.writer.add_scalar('EVAL/hmean', hmean, self.global_step)
                self.logger_info('test: recall: {:.6f}, precision: {:.6f}, f1: {:.6f}'.format(recall, precision, hmean))

                if hmean >= self.metrics['hmean']:
                    save_best = True
                    self.metrics['train_loss'] = self.epoch_result['train_loss']
                    self.metrics['hmean'] = hmean
                    self.metrics['precision'] = precision
                    self.metrics['recall'] = recall
                    self.metrics['best_model_epoch'] = self.epoch_result['epoch']
            else:
                if self.epoch_result['train_loss'] <= self.metrics['train_loss']:
                    save_best = True
                    self.metrics['train_loss'] = self.epoch_result['train_loss']
                    self.metrics['best_model_epoch'] = self.epoch_result['epoch']
            best_str = 'current best, '
            for k, v in self.metrics.items():
                best_str += '{}: {:.6f}, '.format(k, v)
            self.logger_info(best_str)
            if save_best:
                import shutil
                shutil.copy(net_save_path, net_save_path_best)
                self.logger_info("Saving current best: {}".format(net_save_path_best))
            else:
                self.logger_info("Saving checkpoint: {}".format(net_save_path))


    def _on_train_finish(self):
        for k, v in self.metrics.items():
            self.logger_info('{}:{}'.format(k, v))
        self.logger_info('finish train')
