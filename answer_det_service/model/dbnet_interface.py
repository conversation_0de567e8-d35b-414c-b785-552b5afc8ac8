# -*- coding: utf-8 -*-
# @Time    : 2019/8/24 12:06
# <AUTHOR> zhoujun
import gc
import os
import shutil
import time
import cv2
import torch
import random

from base_common import Constants
from answer_det_service.model.data_loader import get_transforms
from answer_det_service.model.models import build_model
from answer_det_service.model.post_processing import get_post_processing
from answer_det_service.model.utils import draw_result

def resize_image_long(img, long_size):
    try:
        height, width, _ = img.shape
        if height > width:
            long_size = (1560,1280) #(1280,896)
        else:
            long_size = (1560,2048) #(1280,896)
        
        #if height < long_size[0] and width < long_size[1]:
        #    scale = 1
        #else:
        scale = min(long_size[0]/height,long_size[1]/width)
        new_height = int(round(round(height*scale) / 32) * 32)
        new_width = int(round(round(width*scale) / 32) * 32)
        resized_img = cv2.resize(img, (new_width, new_height))
        return resized_img
    except:
        return None


def resize_image_short(img, short_size):
    # 最短边至少resize到short_size，超过则不处理
    height, width, _ = img.shape
    if height < width:
        new_height = short_size
        new_width = new_height / height * width
    else:
        new_width = short_size
        new_height = new_width / width * height
    new_height = int(round(new_height / 32) * 32)
    new_width = int(round(new_width / 32) * 32)
    resized_img = cv2.resize(img, (new_width, new_height))
    return resized_img


class Pytorch_model:
    def __init__(self, model_path,vis_handwrite_detect):
        '''
        初始化pytorch模型
        :param model_path: 模型地址(可以是模型的参数或者参数和计算图一起保存的文件)
        :param gpu_id: 在哪一块gpu上运行
        '''        
        checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
        self.vis_handwrite_detect = vis_handwrite_detect

        config = checkpoint['config']
        config['arch']['backbone']['pretrained'] = False
        self.model = build_model(config['arch'])
        
        config['post_processing']['args']['thresh'] = 0.01
        config['post_processing']['args']['box_thresh'] = 0.01
        config['post_processing']['args']['unclip_ratio'] = 1.2

        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
#         self.device = torch.device('cpu')
        
        self.post_process = get_post_processing(config['post_processing'])
        self.img_mode = config['dataset']['train']['dataset']['args']['img_mode']
        self.model.load_state_dict(checkpoint['state_dict'])
#         self.model.cuda()
        self.model = self.model.to(self.device)
        self.model.eval()

        self.transform = []
        for t in config['dataset']['train']['dataset']['args']['transforms']:
            if t['type'] in ['ToTensor', 'Normalize']:
                self.transform.append(t)
        self.transform = get_transforms(self.transform)
        
        if self.vis_handwrite_detect:
            self.vis_dir = f'{Constants.VIS_PATH}/vis_handwrite_dbnet/'
            if os.path.exists(self.vis_dir):
                shutil.rmtree(self.vis_dir)
            os.makedirs(self.vis_dir)
        
            self.crop_dir = ''
            # self.crop_dir = './vis/crop_maths_part1/'
            # if os.path.exists(self.crop_dir):
            #     shutil.rmtree(self.crop_dir)
            # os.mkdir(self.crop_dir)
        del checkpoint
        gc.collect()

    def predict(self, image, is_output_polygon=False, short_size: int = 1024):
        '''
        对传入的图像进行预测，支持图像地址,opecv 读取图片，偏慢
        :param image: 图像cv
        :param is_numpy:
        :return:
        '''
        # cv2.imwrite('/home/<USER>', image)
        # if self.img_mode == 'RGB':
        #     image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        # cv2.imwrite('/home/<USER>', image)
        
        h, w = image.shape[:2]
        img = resize_image_long(image, short_size)
        if img is None:
            return [],[],[],0
        
#         cv2.imwrite(self.vis_dir+'{}.jpg'.format(random.randint(0,100000)),img)
        # 将图片由(w,h)变为(1,img_channel,h,w)
        tensor = self.transform(img)
        tensor = tensor.unsqueeze_(0)

#         tensor = tensor.cuda()
        tensor = tensor.to(self.device)
        batch = {'shape': [(h, w)]}
        with torch.no_grad():
            start = time.time()
            preds = self.model(tensor)
            '''
            box_list_4p, score_list = self.post_process(batch, preds[0], is_output_polygon=False)
            box_list_4p, score_list = box_list_4p[0], score_list[0]
            if len(box_list_4p) > 0:
                if is_output_polygon:
                    idx = [x.sum() > 0 for x in box_list_4p]
                    box_list_4p = [box_list_4p[i].tolist() for i, v in enumerate(idx) if v]
                    score_list = [score_list[i] for i, v in enumerate(idx) if v]
                else:
                    idx = box_list_4p.reshape(box_list_4p.shape[0], -1).sum(axis=1) > 0  # 去掉全为0的框
                    box_list_4p, score_list = box_list_4p[idx].tolist(), score_list[idx]
            else:
                box_list_4p, score_list = [], []
            '''
            box_list, score_list = self.post_process(batch, preds[0], is_output_polygon=True)
            box_list, score_list = box_list[0], score_list[0]
            if len(box_list) > 0:
                if is_output_polygon:
                    idx = [x.sum() > 0 for x in box_list]
                    box_list = [box_list[i] for i, v in enumerate(idx) if v]
                    score_list = [score_list[i] for i, v in enumerate(idx) if v]
                else:
                    idx = box_list.reshape(box_list.shape[0], -1).sum(axis=1) > 0  # 去掉全为0的框
                    box_list, score_list = box_list[idx], score_list[idx]
            else:
                box_list, score_list = [], []

            new_box_list_4p = []  # 不再使用最小外接矩形
            box_list_2p = []
            for bb in box_list:
                xmin = bb[:,0].min()
                xmax = bb[:,0].max()
                ymin = bb[:,1].min()
                ymax = bb[:,1].max()
                box_list_2p.append([xmin,ymin,xmax,ymax])
                new_box_list_4p.append([[int(xmin), int(ymin)], [int(xmax), int(ymin)], [int(xmax), int(ymax)], [int(xmin), int(ymax)]])
            
            t = time.time() - start

        if self.vis_handwrite_detect:
            draw_result(self.vis_dir,str(random.randint(0,100)),image,box_list,score_list,['print']*len(score_list),crop_dir=self.crop_dir)#可视化结果
        
        return new_box_list_4p, box_list_2p,box_list,t
class Dbnet_Interface:
    def __init__(self, model_path, vis_handwrite_detect=False):
        self.polygon = True
        self.model = Pytorch_model(model_path, vis_handwrite_detect)
        
    def detect_img(self,image):
        box_list_4p, box_list_2p, box_list_np, _ = self.model.predict(image, is_output_polygon=self.polygon)
        return box_list_4p, box_list_2p, box_list_np

