# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 10:57 
# @Description  : server_answer_det.py
import sys
from base_common import Context, LoggerFactory, Constants, ParamLoader
from answer_det_service.service.service_of_answer_det import AnswerDetectService

log = LoggerFactory.get_logger('AnswerDetectService')
if __name__ == '__main__':
    args = sys.argv[1:]
    params = ParamLoader(args)
    if not params.is_success():
        log.error('Usage: python xx.py <port> <gpu_num>')
        exit(1)

    Context.setup()
    Constants.setup(Context.is_product())
    Context.set_gpu_num(params.get_gpu_num())
    service = AnswerDetectService()
    service.set_use_ai_lab(True)
    service.set_stop_time(params.get_stop_time())
    service.start(params.get_port())
