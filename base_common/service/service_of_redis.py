# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/8/1 15:47 
# @Description  : service_of_redis.py
import uuid
from ..constants.redis_config import RedisConfigs
from ..logger import LoggerFactory
log = LoggerFactory.get_logger('redis_service')

class RedisManager:
    services = {}
    @classmethod
    def __get_redis_service(cls, redis_name, channel):
        services = cls.services.get(redis_name, None)
        if services is None:
            __client = RedisConfigs.get_redis_client(redis_name, channel)
            service = RedisService(__client)
            cls.services[redis_name] = {channel: service}
        else:
            service = services.get(channel, None)
            if service is None:
                __client = RedisConfigs.get_redis_client(redis_name, channel)
                service = RedisService(__client)
                cls.services[redis_name].update({channel: service})
        return service
    @classmethod
    def get_mission_data_redis(cls, channel="default_channel"):
        return cls.__get_redis_service('mission_data', channel)
    @classmethod
    def get_mission_queue_redis(cls, channel="default_channel"):
        return cls.__get_redis_service('mission_queue', channel)

class RedisService:
    def __init__(self, client):
        self.__client = client

    def __gen_cache_key(self):
        return str(uuid.uuid4()).replace('-', '').upper()
    def pubsub_numsub(self, key):
        _list = self.__client.pubsub_numsub(key)
        if len(_list) == 0:
            return 0
        return _list[0][1]

    def set_val(self, value, expire=24):
        cache_key = self.__gen_cache_key()
        self.set(cache_key, value, expire)
        return cache_key
    def set(self, key, value, expire=24):
        if expire == -1:
            self.__client.set(key, value)
        else:
            self.__client.set(key, value, ex=expire)

    def exists(self, key):
        return self.__client.exists(key)
    def get_bytes(self, cache_key):
        if cache_key is None:
            raise None
        return self.__client.get(cache_key)

    def get_val(self, cache_key):
        val = self.get_bytes(cache_key)
        if val is not None and isinstance(val, bytes):
            val = val.decode("UTF-8")
        return val

    def remove(self, key):
        self.__client.delete(key)

    def remove_patten(self, patten):
        keys = self.__client.keys(patten)
        if keys is not None and len(keys) > 0:
            self.__client.delete(*keys)
    def removes(self, keys):
        if len(keys) > 0:
            self.__client.delete(*keys)
    def pubsub(self):
        return self.__client.pubsub()

    def publish(self, channel, value):
        self.__client.publish(channel, value)
		
    def pipeline(self):
        return self.__client.pipeline()

    # FIFO right_push+left_pop 先进先出
    # block_left_pop 阻塞式左弹出，当timeout为0时，无限期阻塞
    def block_left_pop(self, key, timeout=0):
        cache_tuple = self.__client.blpop(key, timeout)
        if cache_tuple is not None:
            _, resp = cache_tuple
            if isinstance(resp, bytes):
                return resp.decode("UTF-8")
            return resp
        return None

    # FIFO right_push+left_pop 先进先出
    def right_push(self, key, *values):
        return self.__client.rpush(key, *values)

    def left_pop(self, key):
        return self.__client.lpop(key)
    def llen(self, key):
        return self.__client.llen(key)

    def increment(self, key):
        return self.__client.incr(key)
