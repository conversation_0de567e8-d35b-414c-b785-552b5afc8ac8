# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/8 11:39 
# @Description  : service_of_direction.py
from ..context import Context
from ..logger import LoggerFactory
from ..mission_mode import MissionMode
from ..redis_requestor import RedisRequestor
from ..service.service_of_base import BaseService
from ..enums.enums_of_correction import ProcessError as PE

log = LoggerFactory.get_logger('DirectionService')
class DirectionService(BaseService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.DIRECTION_MISSION
        self.init_redis()
    def do_direction(self, mission_id, img_user, img_key=None, img_type=None):
        # 裁剪并旋转图像
        if img_key is None:
            img_key, img_type = self.set_image(img_user)
        data_json = {'img_key': img_key, 'img_type': img_type}
        model_response = RedisRequestor.ai_direction(data_json, Context.get_record_id(mission_id))
        if not model_response.is_success():
            Context.report_error(mission_id, PE.CROP_ROTATE_IMAGE)
        data_json = model_response.get_json_response()
        angle = int(data_json['angle'])
        if angle == 0:
            return img_key, img_type, img_user, 0

        img_key = data_json.get('img_key', None)
        img_type = data_json.get('img_type', None)
        if img_key is None or img_type is None:
            Context.report_error(mission_id, PE.CROP_ROTATE_IMAGE)
            return None, None, None, None
        return img_key, img_type, self.get_image(data_json), angle
