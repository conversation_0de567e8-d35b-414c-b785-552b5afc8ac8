# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/8 11:38 
# @Description  : service_of_detection.py

from ..context import Context
from ..logger import LoggerFactory
from ..mission_mode import MissionMode
from ..redis_requestor import RedisRequestor
from ..service.service_of_base import BaseService
from ..enums.enums_of_correction import ProcessError as PE

log = LoggerFactory.get_logger('DetectionService')

class DetectionService(BaseService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.DETECTION_MISSION
        self.init_redis()

    def do_detection(self, mission_id, img_user):
        img_key, img_type = self.set_image(img_user)
        data_json = {'img_key': img_key, 'img_type': img_type}
        model_response = RedisRequestor.ai_detection(data_json, Context.get_record_id(mission_id))
        if not model_response.is_success():
            Context.report_error(mission_id, PE.CUT_IAMGE)
            return None, None, None
        data_json = model_response.get_json_response()
        img_key = data_json.get('img_key', None)
        img_type = data_json.get('img_type', None)
        if img_key is None or img_type is None:
            Context.report_error(mission_id, PE.CUT_IAMGE)
            return None, None, None
        return img_key, img_type, self.get_image(data_json)
