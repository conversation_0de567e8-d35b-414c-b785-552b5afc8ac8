# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 15:45 
# @Description  : context.py
import os
import time
import traceback
from datetime import datetime
from typing import List, Tuple

from .logger import LoggerFactory
from .constants import Constants
from .mission_mode import Mission<PERSON>lisa
from .constants.redis_config import RedisConfigs

log = LoggerFactory.get_logger('Context')
ERROR_CODE = [20010, 20011, 20012, 20013, 20014, 20015, 20016, 20017, 20021, 20030, 20031, 20040, 20041, 30001, 30010, 30011, 30012, 30020, 30022, 30023, 500002]

class ReportRedisService:
    __mission_queue_redis = None
    __enable = False
    def __init__(self):
        try:
            self.__mission_queue_redis = RedisConfigs.get_redis_client('mission_queue')
            self.__enable = True
        except:
            log.error('missions pool is None')
    def isEnable(self):
        return self.__enable
    def pipeline(self):
        return self.__mission_queue_redis.pipeline()
def report_cost_to_chart(cost_collector, t_cost, tt_cost, resp, is_quick, reportRedisService, req_data):
    try:
        if reportRedisService is None or not reportRedisService.isEnable():
            return

        today = datetime.now().strftime("%Y-%m-%d")
        cost_time_key = f"{today}:cost_time:main"
        costt_time_key = f"{today}:cost_time:maint"
        quick_cost_time_key = f"{today}:cost_time:quick_main"
        quick_costt_time_key = f"{today}:cost_time:quick_maint"
        pipeline = reportRedisService.pipeline()
        if not is_quick:
            pipeline.rpush(cost_time_key, f"{t_cost:.4f}")
            if tt_cost:
                pipeline.rpush(costt_time_key, f"{tt_cost:.4f}")
        else:
            pipeline.rpush(quick_cost_time_key, f"{t_cost:.4f}")
            if tt_cost:
                pipeline.rpush(quick_costt_time_key, f"{tt_cost:.4f}")

        if resp is not None and isinstance(resp, dict):
            error_code = resp.get('errorCode', None)
            if error_code is not None and error_code != 30000:
                if error_code not in ERROR_CODE:
                    error_code = 20012
                pipeline.incrby(f"{today}:error_service:{error_code}")
                if error_code == 20012 and req_data is not None:
                    pipeline.rpush(f"{today}:error_service_list:20012", req_data)

        for item in cost_collector:
            if MissionAlisa.exists(item[0]):
                #key = f"{today}:cost_time:{item[0]}" if not is_quick else f"{today}:quick_cost_time:{item[0]}"
                key = f"{today}:cost_time:{item[0]}"
                pipeline.rpush(key, f"{item[1]:.4f}")
        pipeline.execute()
    except:
        log.error(f"设置redis错误： {traceback.format_exc()}")
        pass
class PrivateContext:
    def __init__(self, is_quick=False):
        log.info("服务上下文正在初始化...")

        self.correct_cost_dict = dict()
        self.cost_collectors = dict()
        self.costr_collectors = dict()
        self.error_collectors = dict()
        self.request_data = dict()

        self.GB = 1024 ** 3

        self.task_cost_time_total = 0
        self.task_cost_time_count = 0
        self.task_async_cost_time_total = 0
        self.task_async_cost_time_count = 0
        self.__env_type = os.environ.get("EnvType", 'prepub')
        self.__local_ip = Constants.LOCAL_IP
        self.__is_quick = is_quick
        self.reportRedisService = ReportRedisService()
        self._today = datetime.now().strftime("%Y-%m-%d")
        log.info("服务上下文已初始化.")

    def get_task_req_url(self):
        if self.is_product():
            return 'https://itechserver.bookln.cn/taskrecordservices/blockingGetTask.do'
        else:
            return 'https://itechserver-prepub.bookln.cn/taskrecordservices/getTask.do'

    def get_task_resp_url(self):
        if self.is_product():
            return 'https://itechserver.bookln.cn/taskrecordservices/taskSuccess.do'
        else:
            return 'https://itechserver-prepub.bookln.cn/taskrecordservices/taskSuccess.do'

    def get_failure_task_resp_url(self):
        if self.is_product():
            return 'https://itechserver.bookln.cn/taskrecordservices/taskFailure.do'
        else:
            return 'https://itechserver-prepub.bookln.cn/taskrecordservices/taskFailure.do'

    def is_dev(self):
        return self.__env_type == 'dev'
    def is_prepub(self):
        return self.__env_type == 'prepub'

    def is_product(self):
        return self.__env_type == 'product'

    def get_env(self):
        return self.__env_type

    def get_error_judge_url(self, env):
        server_url_suffix = '' if env == 'product' else '-%s' % env
        return f"https://bizboot{server_url_suffix}.bookln.cn/userCameraCorrect/judgeAnswerError.do"

    def get_judge_url(self, user_id, env):
        server_url_suffix = '' if env == 'product' else '-%s' % env
        if user_id is not None:
            return f"https://bizboot{server_url_suffix}.bookln.cn/cameraCutTask/judgeAnswerV4.do"
        else:
            return f"https://bizboot{server_url_suffix}.bookln.cn/cameraCutTask/judgeAnswer.do"

    def get_save_align_url(self, env) -> str:
        server_url_suffix = '' if env == 'product' else '-%s' % env
        return f"https://bizboot{server_url_suffix}.bookln.cn/userCameraCorrect/storeCorrectAheadImage.do"

    def correct_start(self, mission_id: str, from_task=None):
        self.correct_cost_dict[mission_id] = {'inner': time.time(), 'from_task': from_task}
    def set_request(self, mission_id: str, req_data=None):
        if req_data is not None:
            self.request_data[mission_id] = req_data

    def exculde_error(self, mission_id: str):
        error_collector = self.error_collectors.get(mission_id, None)
        if error_collector is not None:
            if len(error_collector) == 1 and "搜页" == error_collector[0]:
                self.error_collectors.pop(mission_id)

    def __print_r(self, mission_id):
        cost_collector: List[Tuple[str, float]] = self.costr_collectors.get(mission_id, None)
        for item in cost_collector:
            log.info(f"(mission_id: {mission_id}) {item[0]}耗时: {'%.4f' % item[1]} sec")

    def correct_end(self, mission_id: str, resp: dict):
        s_total = None
        s_async_total = None
        t_end = time.time()
        cost_collector: List[Tuple[str, float]] = self.cost_collectors.get(mission_id, None)
        total = self.correct_cost_dict.get(mission_id, {'inner': None, 'from_task': None})
        t_start = total['inner']
        day_now = datetime.now().strftime("%Y-%m-%d")
        if cost_collector is not None and t_start is not None:
            t_cost = t_end - t_start
            self.task_cost_time_count += 1
            self.task_cost_time_total += t_cost

            for item in cost_collector:
                if '批改接口' == item[0]:
                    t_cost -= item[1]
                log.info(f"(mission_id: {mission_id}) {MissionAlisa.get_service_name(item[0])}耗时: {'%.4f' % item[1]} sec")

            tt_cost = None
            if total['from_task'] is not None:
                tt_cost = t_end - total['from_task']
                log.info(f"(mission_id: {mission_id}) 批改总耗时: {'%.4f' % min(t_cost, tt_cost)} sec")
                log.info(f"(mission_id: {mission_id}) 任务总耗时: {'%.4f' % max(t_cost, tt_cost)} sec")
                self.task_async_cost_time_count += 1
                self.task_async_cost_time_total += tt_cost
            else:
                log.info(f"(mission_id: {mission_id}) 批改总耗时: {'%.4f' % t_cost} sec")
            try:
                if self.is_product():
                    req_data = self.request_data.get(mission_id, None)
                    report_cost_to_chart(cost_collector, t_cost, tt_cost, resp, self.__is_quick, self.reportRedisService, req_data)
            except:
                log.error(f"上报错误 {traceback.format_exc()}")
                pass
            error_collector = self.error_collectors.get(mission_id, None)
            if error_collector is not None and len(error_collector) > 0:
                log.error(f"****************************************************")
                for info in error_collector:
                    log.error(f"(mission_id: {mission_id}) 流程({info})出错了！")
                log.error(f"****************************************************")

            #if self.task_cost_time_count % 10 == 0:
            if self.task_cost_time_count != 0:
                t_cost = self.task_cost_time_total / self.task_cost_time_count
                s_total = f"{self.task_cost_time_count}次批改平均耗时: {'%.4f' % t_cost} sec"
                if day_now != self._today:
                    self.task_cost_time_count = 0
                    self.task_cost_time_total = 0
                    self._today = day_now
            if self.task_async_cost_time_count != 0:
                t_cost = self.task_async_cost_time_total / self.task_async_cost_time_count
                s_async_total = f"异步任务{self.task_async_cost_time_count}次批改平均耗时: {'%.4f' % t_cost} sec"
                if day_now != self._today:
                    self.task_async_cost_time_count = 0
                    self.task_async_cost_time_total = 0
                    self._today = day_now

        #self.__print_sys_info()
        self.costr_collectors.pop(mission_id, None)
        self.correct_cost_dict.pop(mission_id, None)
        self.cost_collectors.pop(mission_id, None)
        self.error_collectors.pop(mission_id, None)
        self.request_data.pop(mission_id, None)
        return s_total, s_async_total, day_now

    def report_cost(self, mission_id: str, info):
        cost_collector = self.cost_collectors.get(mission_id, None)
        if cost_collector is None:
            cost_collector: List[Tuple[str, float]] = []
            self.cost_collectors[mission_id] = cost_collector
        cost_collector.append(info)

    def report_error(self, mission_id: str, info):
        error_collector = self.error_collectors.get(mission_id, None)
        if error_collector is None:
            error_collector = []
            self.error_collectors[mission_id] = error_collector
        error_collector.append(info)

class Context:
    context = None
    error_api_count = {}
    __id_maps = {}
    __img_key_maps = {}
    __cost_collector = {}
    __day_now = datetime.now().strftime("%Y-%m-%d")
    __mission_data_redis = None
    @classmethod
    def setup(cls, is_quick=False):
        RedisConfigs.print()
        cls.context = PrivateContext(is_quick)
        cls.__mission_data_redis = RedisConfigs.get_redis_client('mission_data')
    @classmethod
    def is_prepub(cls):
        return cls.context.is_prepub()

    @classmethod
    def is_product(cls):
        return cls.context.is_product()
    @classmethod
    def get_env(cls):
        return cls.context.get_env()

    @classmethod
    def get_error_judge_url(cls, env) -> str:
        return cls.context.get_error_judge_url(env)

    @classmethod
    def get_judge_url(cls, user_id, env) -> str:
        return cls.context.get_judge_url(user_id, env)

    @classmethod
    def get_save_align_url(cls, env) -> str:
        return cls.context.get_save_align_url(env)

    @classmethod
    def correct_start(cls, mission_id: str, from_task=None):
        cls.context.correct_start(mission_id, from_task)
    @classmethod
    def set_request(cls, mission_id: str, req_data=None):
        cls.context.set_request(mission_id, req_data)

    @classmethod
    def exculde_error(cls, mission_id: str):
        cls.context.exculde_error(mission_id)

    @classmethod
    def correct_end(cls, mission_id: str, resp: dict):
        record, record1, day_now = cls.context.correct_end(mission_id, resp)
        # try:
        #     if cls.__day_now != day_now:
        #         cls.__day_now = day_now
        #         cls.__img_key_maps = {}
        #         return record, record1
        #     img_keys = cls.get_img_keys(mission_id)
        #     if img_keys is not None:
        #         cls.__mission_data_redis.removes(*img_keys)
        # except:
        #     log.error(f"清理图片出错 {traceback.format_exc()}")
        return record, record1

    @classmethod
    def report_cost(cls, mission_id: str, info):
        cls.context.report_cost(mission_id, info)

    @classmethod
    def report_error(cls, mission_id: str, info):
        cls.context.report_error(mission_id, info.value)

    @classmethod
    def is_ecs(cls):
        return True

    @classmethod
    def is_dev(cls):
        return cls.context.is_dev()

    @classmethod
    def get_task_req_url(cls):
        return cls.context.get_task_req_url()

    @classmethod
    def get_task_req_data(cls, getLimit=1):
        if cls.context.is_product():
            return {'bizTypeCode': 'camera_correct', 'workGroup': 'product', 'getLimit': getLimit}
        return {'bizTypeCode': 'camera_correct', 'workGroup': 'daily', 'getLimit': getLimit}
    @classmethod
    def get_mission_list_key(cls):
        return "CORRECTION_MISSION_LIST"
    @classmethod
    def get_mission_list_channel(cls):
        return "CORRECTION_MISSION_CHANNEL"
    @classmethod
    def get_task_resp_url(cls, success=True):
        return cls.context.get_task_resp_url() if success else cls.context.get_failure_task_resp_url()
    @classmethod
    def get_notify_url(cls):
        return Constants.MESSAGE_URL
    @classmethod
    def get_working_task_count_key(cls):
        return Constants.WORKING_TASK_COUNT_KEY
    @classmethod
    def set_gpu_num(cls, default_num=None):
        if default_num is not None:
            if isinstance(default_num, int):
                default_num = str(default_num)
            os.environ['CUDA_VISIBLE_DEVICES'] = default_num
        else:
            gpu_num = os.environ.get('CUDA_VISIBLE_DEVICES', "0")
            os.environ['CUDA_VISIBLE_DEVICES'] = gpu_num

    @classmethod
    def get_record_id(cls, mission_id):
        if mission_id is None:
            return None
        return cls.__id_maps.get(mission_id, None)

    @classmethod
    def set_record_id(cls, mission_id, record_id):
        cls.__id_maps[mission_id] = record_id

    @classmethod
    def print_cost(cls, mission_name, model_time, total_time):
        if mission_name is None or model_time is None or total_time is None:
            return
        store = cls.__cost_collector.get(mission_name, None)
        if store is None:
            store = {}
        store_count = store.get('store_count', 0)
        if store_count == 0:
            model_times = []
            total_times = []
        elif store_count > 10000:
            return
        else:
            model_times = store.get('model_times', [])
            total_times = store.get('total_times', [])
        model_times.append(model_time)
        total_times.append(total_time)
        store['model_times'] = model_times
        store['total_times'] = total_times
        store['store_count'] = store_count + 1
        cls.__cost_collector[mission_name] = store

        model_count = len(model_times)
        total_count = len(total_times)
        log.info(f"{mission_name}-模型{model_count}次平均耗时:{sum(model_times) / model_count:.4f} "
                 f"整体{total_count}次平均耗时:{sum(total_times) / total_count:.4f}")
    @classmethod
    def del_record_id(cls, mission_id):
        if mission_id in cls.__id_maps:
            del cls.__id_maps[mission_id]

    @classmethod
    def add_img_key(cls, mission_id, img_key):
        if img_key is None or mission_id is None:
            return
        img_keys = cls.__img_key_maps.get(mission_id, None)
        if img_keys is None:
            img_keys = []
        img_keys.append(img_key)
        cls.__img_key_maps.update({mission_id: img_keys})

    @classmethod
    def get_img_keys(cls, mission_id):
        img_keys = cls.__img_key_maps.get(mission_id, None)
        if img_keys is None:
            return None
        return img_keys