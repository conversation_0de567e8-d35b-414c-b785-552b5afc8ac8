# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/8/24 13:48
# @Description  : redis_requestor.py
import json
import time
import asyncio
import traceback

from .logger import LoggerFactory
from .mission_mode import MissionMode
from .dto.data_of_base import HttpResponse
from .enums.enums_of_correction import ApiResponse
from .missions.mission_producer import MissionProducer

log = LoggerFactory.get_logger('AiRequestor')

class RedisRequestor:
    __producer_map = {}

    @classmethod
    def init_producers(cls, is_full=True):
        cls.get_producer(MissionMode.DETECTION_MISSION)
        cls.get_producer(MissionMode.DIRECTION_MISSION)
        cls.get_producer(MissionMode.QUICK_CALC_DET_MISSION)
        cls.get_producer(MissionMode.QUICK_CALC_REC_MISSION)
        cls.get_producer(MissionMode.VERTICAL_CALC_DET_MISSION)
        cls.get_producer(MissionMode.VERTICAL_CALC_REC_MISSION)
        cls.get_producer(MissionMode.FORMULAR_HW_OCR_MISSION)
        cls.get_producer(MissionMode.ANSWER_DETECT_MISSION)
        cls.get_producer(MissionMode.ZONE_ALIGN_MISSION)

        if is_full:
            cls.get_producer(MissionMode.SURF_MISSION)
            cls.get_producer(MissionMode.LINE_CALC_MISSION)
            cls.get_producer(MissionMode.GRAPHICS_CALC_MISSION)
            cls.get_producer(MissionMode.CH_HW_OCR_MISSION)
            cls.get_producer(MissionMode.EN_HW_OCR_MISSION)
            cls.get_producer(MissionMode.EMBEDDING_MISSION2)
            cls.get_producer(MissionMode.PRINT_OCR_DET_MISSION)
            cls.get_producer(MissionMode.PRINT_OCR_REC_MISSION)

    @classmethod
    async def _async_do_publish(cls, mission_mode, str_data: str, record_id=None) -> HttpResponse:
        t0 = time.time()
        try:
            producer = cls.get_producer(mission_mode)
            resp = await producer.publish(str_data, record_id)
            if resp is None or resp == 'None':
                return HttpResponse(st=t0, api_result=ApiResponse.ResponseError)
            return HttpResponse(st=t0, response=resp, success=True)
        except BaseException:
            log.error(f"Oops!!! bad publish error by exception\r\n{traceback.format_exc()}")
            return HttpResponse(st=t0, api_error=True, api_result=ApiResponse.ResponseException)

    @classmethod
    def _do_publish(cls, mission_mode, str_data: str, record_id=None) -> HttpResponse:
        t0 = time.time()
        try:
            producer = cls.get_producer(mission_mode)
            resp = asyncio.run(producer.publish(str_data, record_id))
            if resp is None or resp == 'None':
                return HttpResponse(st=t0, api_result=ApiResponse.ResponseError)
            return HttpResponse(st=t0, response=resp, success=True)
        except BaseException:
            log.error(f"Oops!!! bad publish error by exception\r\n{traceback.format_exc()}")
            return HttpResponse(st=t0, api_error=True, api_result=ApiResponse.ResponseException)

    @classmethod
    async def ai_zone_align(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求答案检测。
        参数:
        :param str_data: 请求json
        :param record_id: record_id
        返回:
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.ZONE_ALIGN_MISSION, str_data, record_id)
        return api_response.response_to_json()
    @classmethod
    async def ai_answer_detect(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求答案检测。
        参数:
        :param str_data: 图片base64
        :param record_id: record_id
        返回:
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.ANSWER_DETECT_MISSION, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    def ai_print_detect(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求印刷体识别。
        参数--
        :param str_data: base64图片
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        # 构造请求体数据
        api_response = cls._do_publish(MissionMode.PRINT_OCR_DET_MISSION, str_data, record_id)
        try:
            if api_response.is_success():
                response = api_response.get_json_response()
                response = response['result']
                api_response.set_response(response)
            else:
                api_response.set_response([])
        except:
            api_response.set_response([])
        return api_response

    @classmethod
    async def ai_ocr_hand_write_cn(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求手写中文识别。
        参数--
        :param str_data: 中文识别参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.CH_HW_OCR_MISSION, str_data, record_id)
        if api_response.is_success():
            resp_json = api_response.get_json_response()
            if resp_json['flag'] != 1000:
                log.warn(f"--error ocr_text_reg")
                pred_chars_text = [''] * len(json.loads(str_data)['imgs'])
            else:
                pred_chars_text = resp_json['result']
            api_response.set_response(pred_chars_text)

        return api_response

    @classmethod
    async def ai_ocr_hand_write_en(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求手写英文识别。
        参数--
        :param str_data: 英文识别参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.EN_HW_OCR_MISSION, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    async def ai_ocr_hand_write_formula(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求手写公式识别。
        参数--
        :param str_data: 识别参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.FORMULAR_HW_OCR_MISSION, str_data, record_id)
        if api_response.is_success():
            response = api_response.get_json_response()
            if 1000 != response['flag']:
                api_response.set_success(False)
            else:
                api_response.set_response(response['result'])
        return api_response

    @classmethod
    def ai_detection(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求抠图检测模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        return cls._do_publish(MissionMode.DETECTION_MISSION, str_data, record_id)

    @classmethod
    def ai_direction(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求方向检测模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        return cls._do_publish(MissionMode.DIRECTION_MISSION, str_data, record_id)

    @classmethod
    def ai_embedding2(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求图像特征模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = cls._do_publish(MissionMode.EMBEDDING_MISSION2, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    def ai_align_images_surf(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求图像对齐模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = cls._do_publish(MissionMode.SURF_MISSION, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    async def ai_vertical_det_calc(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求竖式计算模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.VERTICAL_CALC_DET_MISSION, str_data, record_id)
        return api_response.response_to_json()
    @classmethod
    async def ai_vertical_rec_calc(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求竖式计算模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.VERTICAL_CALC_REC_MISSION, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    async def ai_graphics_calc(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求作图题模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.GRAPHICS_CALC_MISSION, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    async def ai_line_calc(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求连线题模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.LINE_CALC_MISSION, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    async def ai_quick_calc_detect(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求智能口算模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.QUICK_CALC_DET_MISSION, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    async def ai_quick_calc_rec(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求智能口算模型
        参数--
        :param str_data: 检测参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.QUICK_CALC_REC_MISSION, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    async def ai_print_rec(cls, str_data: str, record_id: str) -> HttpResponse:
        """
        请求印刷体识别模型
        参数--
        :param str_data: 识别参数
        :param record_id: record_id
        返回--
        :return @HttpResponse
        """
        api_response = await cls._async_do_publish(MissionMode.PRINT_OCR_REC_MISSION, str_data, record_id)
        return api_response.response_to_json()

    @classmethod
    def get_producer(cls, mission_type):
        producer = cls.__producer_map.get(mission_type, None)
        if producer is None:
            producer = MissionProducer(mission_type)
            cls.__producer_map[mission_type] = producer
        return producer
