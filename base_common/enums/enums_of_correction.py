# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> yanhui
# @Date         ：2024/6/7 14:57
# @Description  : enums_of_correction.py
from enum import Enum, unique
@unique
class CorrectionError(Enum):
    ACCESS_SEARCH_PAGE_API_FAILED = 20010
    SEARCH_PAGE_FAILED = 20011
    PIGAI_PROCESS_UNDEFINED_EXCEPTION = 20012
    LOAD_COLUMN_INFO_FAILED = 20013
    ALIGN_PAGE_FAILED = 20014
    UPLOAD_ALIGN_PAGE_FAILED = 20015
    PREPARE_USER_ANSWER_INFO_FAILED = 20016
    ACCESS_BOOK_NOT_FOUND = 20017
    INVALID_USER_IMAGE = 20021
    ACCESS_ANSWER_DETECT_API_FAILED = 20030
    INVALID_ALIGN_PAGE = 20031
    ACCESS_ANSWER_JUDGE_API_FAILED = 20040
    ANSWER_JUDGE_API_LOGIC_ERROR = 20041
    NO_HANDWRITTEN_ANSWER_DETECTED = 30001

    def __str__(self):
        return f"errorNumber {self.value}, {' '.join([x.capitalize() for x in self.name.split('_')])}"
@unique
class AutoCorrectErrorNum(Enum):
    #for upper layer
    NO_ANY_ANSWER_ITEM_DETECT = 30001
    FETCH_ASYNC_FROM_POOL_TOO_SLOW = 30010
    ACCESS_AUTO_CORRECTION_API_FAILED = 30011
    ACCESS_AUTO_CORRECTION_API_OTHER_EXCEPTION = 30012

    #for inner layer
    CROP_AND_ROTATE_API_FAILED = 30020
    NO_PDF_INVALID_USER_IMAGE = 30021

    ACCESS_AUTO_CORRECTION_ALGO_API_FAILED = 30022
    ACCESS_AUTO_CORRECTION_ALGO_API_OTHER_EXCEPTION = 30023

    def __str__(self):
        return f"errorNumber {self.value}, {' '.join([x.capitalize() for x in self.name.split('_')])}"
@unique
class ApiResponse(Enum):
    ResponseError = (-1, "Api Response Code Not 200")
    ResponseException = (-2, "Api Request Exception")
    ResponseSuccess = (0, "Everything is fine!")

    def __str__(self):
        return self.value[1]
@unique
class BizCode(Enum):
    WITH_PDF_PIGAI = 1
    NO_PDF_PIGAI = 2
    PARTIAL_PDF_PIGAI = 3


    def __str__(self):
        return BizCode.value
@unique
class ProcessError(Enum):
    FIND_BOOK = "找书"
    SEARCH_PAGE = "搜页"
    LOAD_BOOK = "加载书籍"
    ANSWER_DETECT = "答案检测"
    CORRECT = "批改"
    SAVE_ALIGN_IMAGE = "保存对齐后的图片"
    ALIGN_IAMGE = "图像矫正"
    CUT_IAMGE = "抠图"
    CROP_ROTATE_IMAGE = "裁剪和旋转图片"
    FEATURE_DETECT = "特征检测"
    CN_HANDWRITE_OCR = "中文手写识别"
    EN_HANDWRITE_OCR = "英文手写识别"
    FORMULA_HANDWRITE_OCR = "公式手写识别"
    PRINTE_OCR_DET = "印刷体检测"
    PRINTE_OCR_REC = "印刷体识别"

    def __str__(self):
        return self.value
class ErrorCode:
    SEARCH_PAGE_ERROR = 20011
    CORRECTION_ERROR = 20012
    BOOK_NOT_FOUND_ERROR = 20017
    USER_IMAGE_ERROR = 20021
    SEARCH_USER_IMAGE_ERROR = 20022
    ALIGN_PAGE_ERROR = 20031