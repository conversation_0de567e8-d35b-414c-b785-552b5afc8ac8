# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/12/13 16:31 
# @Description  : mission_handler.py
import json
import time
import uuid
import traceback
from datetime import datetime

from ..constants import Constants
from ..logger import LoggerFactory
from ..mission_mode import MissionMode
log = LoggerFactory.get_logger("MissionHandler")

class MissionHandler:
    def __init__(self, mission_queue_redis, mission_channel: str):
        self.mission_queue_redis = mission_queue_redis
        self.mission_channel = mission_channel
        self.service_name = MissionMode.get_service_name(self.mission_channel)
        self.busy_times_count = MissionMode.get_busy_times_count(self.mission_channel)
        self.stop_flag = False
        self._uid = str(uuid.uuid4()).replace('-', '')
        self.mission_queue_redis.remove_patten(f"RUNNING_SERVICES_LIST:{Constants.LOCAL_IP}:*")
        self._alive_key = f"RUNNING_SERVICES_LIST:{Constants.LOCAL_IP}:{mission_channel}:{self._uid}"
        self._stop_time = "9223372036"
        self.stop_timestamp = 9223372036
        self._busy_times = []
        self.__counter = 0

    def right_push(self, message_data: str):
        """添加消息到队列中"""
        self.mission_queue_redis.right_push(self.mission_channel, message_data)

    def set_stop_time(self, stop_time='0'):
        if isinstance(stop_time, int):
            stop_time = str(stop_time)
        self._stop_time = stop_time
        if '0' != stop_time:
            self.stop_timestamp = int(stop_time)
        else:
            self._stop_time = str(self.stop_timestamp)
    def __pop_and_consumer(self, start, consumer, timeout, redis_service):
        if 0 == timeout:
            mission = redis_service.left_pop(self.mission_channel)
        else:
            mission = redis_service.block_left_pop(self.mission_channel, timeout=timeout)

        if mission is not None:
            t1 = time.time()
            consumer(json.loads(mission))
            wt = time.time() - t1
            useage = time.time() - start
            if useage != 0:
                useage = wt / useage
                log.info(f"任务消费结束，耗时：{wt:.4f} sec 利用率：{useage:.4f}%")
            else:
                log.info(f"任务消费结束，耗时：{wt:.4f} sec")
            return True, wt
        return False, 0
    def __end_check(self):
        try:
            if Constants.IS_PRODUCT:
                if time.time() >= self.stop_timestamp:
                    log.info(f"服务已超时停止：{self._alive_key} {self.stop_timestamp}")
                    self.stop_flag = True
                    return True
                self.__counter += 1
                if self.__counter >= 5:
                    self.__counter = 0
                    val = self.mission_queue_redis.get_val(self._alive_key)
                    if val == '0':
                        log.info(f"通过设置redis参数关闭服务：{self._alive_key} {val}")
                        self._stop_time = val
                        self.stop_flag = True
                        return True
                self.mission_queue_redis.set(self._alive_key, self._stop_time, 60)
        except:
            pass
        return False

    def listen(self, consumer, is_stop=None, timeout=5):
        log.info(f"开始监听 {self.mission_channel} {Constants.IS_PRODUCT}")
        client_stop = is_stop is not None and is_stop()
        try:
            if Constants.IS_PRODUCT:
                self.mission_queue_redis.set(self._alive_key, self._stop_time, 60)
        except:
            log.error(f"{traceback.format_exc()}")
        log.info(f"服务停止标识：{self.stop_flag} 客户端停止标识：{client_stop}")

        while not (self.stop_flag or client_stop):
            work_time = 0
            start = time.time()
            try:
                _, t = self.__pop_and_consumer(start, consumer, timeout, self.mission_queue_redis)
                work_time = t
            except:
                log.error(f"Redis {self.mission_channel} 消费消息异常\n{traceback.format_exc()}")
            finally:
                self.__store_cost(work_time, time.time() - start)
                client_stop = is_stop is not None and is_stop()
                if self.__end_check() or client_stop:
                    break
        self.mission_queue_redis.remove(self._alive_key)
    def __store_cost(self, busy_time, total_time):
        self._busy_times.append(f"{busy_time:.4f}:{total_time:.4f}")
        if len(self._busy_times) >= self.busy_times_count:
            if True or self.mission_queue_redis.exists("START_UTILIZATION"):
                today = datetime.now().strftime("%Y-%m-%d")
                cost_busy_channel = f"{today}:busy_time:{self.service_name}"
                busy_times = self._busy_times.copy()
                self.mission_queue_redis.right_push(cost_busy_channel, *busy_times)
            self._busy_times = []

    def stop(self):
        self.stop_flag = True
