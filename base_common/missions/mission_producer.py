# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/8/23 19:34 
# @Description  : queue_producer.py
import time
import uuid
import json
import asyncio
import threading
import traceback

from ..logger import LoggerFactory
from ..mission_mode import MissionMode
from .mission_handler import MissionHandler
from ..constants.constants import Constants
from ..service.service_of_redis import RedisManager
log = LoggerFactory.get_logger('MissionProducer')
pedding_missions = {}
class MissionProducer:
    def __init__(self, mission_req_channel):
        self.mission_queue_redis = RedisManager.get_mission_queue_redis()
        self.mission_req_channel = mission_req_channel
        self.mission_name = MissionMode.get_mission_name(self.mission_req_channel)
        self.mission_resp_channel = f"MISSION:{Constants.LOCAL_IP}:{mission_req_channel}:{str(uuid.uuid4()).replace('-', '')}"
        self.mission_queue_redis.remove_patten(f"MISSION:{Constants.LOCAL_IP}:{self.mission_req_channel}:*")
        self.redisRequest = None
        self.init_handler()
    def init_handler(self):
        self.redisRequest = MissionHandler(self.mission_queue_redis, self.mission_req_channel)
        response_thread = threading.Thread(target=self.__listen_response)
        response_thread.daemon = True
        response_thread.start()

    def on_message(self, response):
        global pedding_missions
        mission_id = None
        future = None
        mission_cost = None
        mission_resp = None
        try:
            log.debug(f"{self.mission_resp_channel}收到任务响应结果")
            mission_id = response.get('mission_id', None)
            if mission_id is not None and mission_id in pedding_missions:
                future = pedding_missions[mission_id]['future']
                mission_cost = time.time() - pedding_missions[mission_id]['time']
                mission_resp = response
                work_ip = response.get('mission_ip', None)
                model_cost = response.get('mission_cost', None)
                if work_ip is not None and model_cost is not None and model_cost > 8:
                    log.info(f"{mission_id} {self.mission_name} 结束 {mission_cost:.4f} sec, 服务耗时：{model_cost:.4f} sec, 工作IP: {work_ip}")
                    if Constants.IS_PRODUCT:
                        log.info(f"服务《{self.mission_name}》单次任务超过8秒，服务耗时：{model_cost:.4f} sec，工作服务器IP: {work_ip}")

        except Exception:
            log.error(f"读取响应异常，已忽略，异常信息\n{traceback.format_exc()}")
            mission_resp = {
                'mission_id': mission_id,
                'mission_data': None,
                'mission_cost': mission_cost,
                'mission_result': False
            }
        finally:
            if mission_id is not None and future is not None and mission_id in pedding_missions:
                future.set_result(mission_resp)
    def __listen_response(self):
        while True:
            try:
                mission = self.mission_queue_redis.block_left_pop(self.mission_resp_channel, timeout=5)
                if mission is not None:
                    log.debug(f"{self.mission_resp_channel} 收到消息回调 {mission}")
                    self.on_message(json.loads(mission))
            except Exception:
                log.error(f"Redis {self.mission_resp_channel} 消费消息异常\n{traceback.format_exc()}")

    async def publish(self, mission_data, record_id=None, timeout=None):
        global pedding_missions
        if timeout is None:
            timeout = 8
            if not Constants.IS_PRODUCT:
                timeout = 60
        end_time = time.time() + timeout
        mission_create = time.time()
        mission_id = str(uuid.uuid4()).replace('-', '')
        future = asyncio.Future()
        pedding_missions[mission_id] = {"future": future, "time": mission_create}
        self.redisRequest.right_push(json.dumps({
            'record_id': record_id,
            'mission_create': mission_create,
            'mission_id': mission_id,
            'mission_data': mission_data,
            'mission_channel': self.mission_resp_channel
        }))
        log.debug(f'{self.mission_name} 任务{mission_id} 已发布')
        resp = None
        is_timeout = False
        while timeout == -1 or not is_timeout:
            if future.done():
                mission_resp = future.result()
                log.debug(f"{self.mission_name} 任务已完成, 耗时： "
                          f"{mission_resp.get('mission_cost', time.time() - mission_create):.4f} sec")
                if mission_resp.get('mission_result', False):
                    resp = mission_resp.get('mission_data', None)
                else:
                    log.error(f"任务执行异常，请持续关注！record_id:{record_id} {self.mission_name}")
                break
            await asyncio.sleep(0.005)
            is_timeout = time.time() > end_time
        if not future.done():
            future.set_result(None)
        del pedding_missions[mission_id]
        if is_timeout:
            log.info(f"{self.mission_name} 任务已超时，耗时： {time.time() - mission_create:.4f} sec")
        return resp