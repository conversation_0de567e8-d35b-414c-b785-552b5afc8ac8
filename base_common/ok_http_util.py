# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/11 14:14 
# @Description  : ok_http_util.py
import asyncio
import json
import time
import aiohttp
import traceback
import requests

from queue import Queue
from concurrent.futures import ThreadPoolExecutor

from .context import Context
from .constants.constants import Constants
from .dto import HttpResponse
from .enums import ApiResponse
from .logger import LoggerFactory
log = LoggerFactory.get_logger('OkHttpUtil')

class ThreadExecutor(ThreadPoolExecutor):
    """
    修改线程池修改队列数
    """
    def __init__(self, max_workers=None, thread_name_prefix=''):
        super().__init__(max_workers, thread_name_prefix)
        self._work_queue = Queue(self._max_workers * 2)
class OkHttpUtil:
    @classmethod
    def post_form(cls, url, form_data, timeout=10) -> HttpResponse:
        t0 = time.time()
        try:
            response = requests.post(url, data=form_data, timeout=timeout)
            if response.status_code == 200:
                return HttpResponse(st=t0, response=response.json(), success=True)
            return HttpResponse(st=t0, api_result=ApiResponse.ResponseError)
        except:
            log.error(f'form request exception{traceback.format_exc()}')
            return HttpResponse(st=t0, api_result=ApiResponse.ResponseException)

    @classmethod
    def post_request(cls, url, json_data=None, str_data=None, timeout=30) -> HttpResponse:
        return asyncio.run(cls.post_request_async(url, json_data, str_data, timeout))

    @classmethod
    async def post_request_async(cls, url, json_data=None, str_data=None, timeout=30) -> HttpResponse:
        t0 = time.time()
        try:
            # log.debug(f"async request from url {url}")
            async with aiohttp.ClientSession() as session:
                async with session.post(url=url, json=json_data, data=str_data, timeout=timeout) as response:
                    if response.status != 200:
                        log.error(f"request fail {url} by code {response.status}\r\n {await response.text()}")
                        cls.error_request(str_data, url, response.status)
                        return HttpResponse(st=t0, api_error=True, api_result=ApiResponse.ResponseError)
                    else:
                        Context.error_api_count[url] = 0
                    text = await response.text()
                    return HttpResponse(st=t0, response=json.loads(text), success=True)
        except BaseException:
            error_info = traceback.format_exc()
            if 'TimeoutError' in error_info:
                cls.error_request(str_data, url, f"Timeout {timeout}s")
            log.error(f"Oops!!! bad request {url} error by exception\r\n{error_info}")
            return HttpResponse(st=t0, api_error=True, api_result=ApiResponse.ResponseException)

    @classmethod
    def error_request(cls, json_data, url, code):
        if Context.is_product():
            if isinstance(json_data, str):
                json_data = json.loads(json_data)
            recordId = json_data.get('recordId', None)
            api = url[url.rfind('/') + 1:]
            notify_message = f'接口{api}请求失败，错误：{code}, recordId: {recordId}, 若连续出现请重点关注'
            cls.notify_feishu(notify_message)

    @classmethod
    def notify_feishu(cls, msg_body: str):
        msg_body = f"{msg_body}。服务器位置：{Constants.LOCAL_IP}"
        log.info(f"notify_feishu {msg_body}")
        try:
            response = requests.post(Constants.MESSAGE_URL, data=msg_body.encode('utf-8'))
            log.debug(f"request notify feishu success {response.text}")
        except:
            log.error(f"Oops!!! bad request {Constants.MESSAGE_URL} error by exception\r\n{traceback.format_exc()}")