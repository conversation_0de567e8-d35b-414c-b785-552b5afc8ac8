# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/7 10:06 
# @Description  : data_of_base.py

import json
import time
import numpy
from ..enums.enums_of_correction import ApiResponse

class BaseData:
    def to_string(self, ensure_ascii=False) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=ensure_ascii)

    def to_dict(self) -> dict:
        result = {}
        for key, value in self.__dict__.items():
            val = getattr(self, key)
            if isinstance(val, numpy.ndarray):
                continue
            # 去除私有属性前的下划线
            public_key = key.lstrip('_')
            result[public_key] = getattr(self, key)
        return result

class BaseResponse(BaseData):
    def __init__(self, st=None, et=time.time(), message=None, error_code=None, success=False, response=None):
        self._message = message
        self._error_code = error_code
        self._success = success
        self._st = st
        self._et = et
        self._response = response
    @classmethod
    def error(cls, message=None, error_code=None):
        return cls(success=False, message=message, error_code=error_code)
    @classmethod
    def ok(cls): return cls(success=True)

    def set_message(self, message=None):
        self._message = message
        return self

    def get_message(self): return self._message

    def set_error_code(self, error_code=None):
        self._error_code = error_code
        return self

    def get_error_code(self): return self._error_code

    def set_response(self, response=None):
        self._response = response
        return self

    def get_response(self): return self._response
    def get_json_response(self):
        if isinstance(self._response, str):
            return json.loads(self._response)
        return self._response

    def set_st(self, st=None):
        self._st = st
        return self

    def get_st(self): return self._st

    def set_et(self, et=None):
        self._et = et
        return self

    def get_et(self): return self._et

    def is_success(self) -> bool: return self._success

    def set_success(self, success: bool):
        self._success = success
        return self

    def time(self):
        return self._et - self._st

    def response_to_json(self):
        if self.is_success() and isinstance(self._response, str):
            self._response = json.loads(self._response)
        return self

class HttpResponse(BaseResponse):
    def __init__(self, st=None, et=time.time(), message=None, error_code=None, success=False, response=None,
                 api_error=False, api_result=ApiResponse.ResponseSuccess):
        super().__init__(st, et, message, error_code, success, response)
        self._api_error = api_error
        self._api_result = api_result

    def get_api_result(self) -> ApiResponse: return self._api_result
    def set_api_result(self, api_result: ApiResponse):
        self._api_result = api_result
        return self
    def is_api_error(self):
        return self._api_error
    def is_exception(self) -> bool: return self._api_result == ApiResponse.ResponseException
    def is_error(self) -> bool: return self._api_result == ApiResponse.ResponseError
    def is_success(self) -> bool: return self._api_result == ApiResponse.ResponseSuccess
    def to_string(self, ensure_ascii=False) -> str:
        if self.is_success():
            if isinstance(self._response, str):
                return self._response
            return json.dumps(self._response, ensure_ascii=False)
        return "{}"

class ImageData:
    def __init__(self, img=None, mission_id=None, img_key=None, img_type=None, redis_name=None, img_fmt='.jpg'):
        self._img = img
        self._mission_id = mission_id
        self._img_key = img_key
        self._img_type = img_type
        self._redis_name = redis_name
        self._img_fmt = img_fmt
    def set_img(self, img=None): self._img = img
    def get_img(self): return self._img
    def set_mission_id(self, mission_id=None): self._mission_id = mission_id
    def get_mission_id(self): return self._mission_id
    def set_img_key(self, img_key=None): self._img_key = img_key
    def get_img_key(self): return self._img_key
    def set_img_type(self, img_type=None): self._img_type = img_type
    def get_img_type(self): return self._img_type
    def set_redis_name(self, redis_name=None): self._redis_name = redis_name
    def get_redis_name(self): return self._redis_name
    def set_img_fmt(self, img_fmt='.jpg'): self._img_fmt = img_fmt
    def get_img_fmt(self): return self._img_fmt

    def can_save(self):
        return self._img is not None

    def from_data_json(self, data_json):
        self._img = None
        self._img_key = data_json.get('img_key', None)
        self._img_type = data_json.get('img_type', None)
        self._redis_name = data_json.get('redis_name', None)
    def to_data_json(self):
        return {'img_key': self._img_key, 'img_type': self._img_type, 'redis_name': self._redis_name}