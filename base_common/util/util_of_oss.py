# coding=utf-8
import json
import os
import socket
import sys
import traceback

import oss2
import requests

from ..constants import Constants
from ..logger import LoggerFactory
log = LoggerFactory.get_logger("OSSUtil")

class OssUtil:
    ak = None
    sk = None
    endpoint = None
    bucket = None
    host = None
    @staticmethod
    def upload(local_file_path, oss_file_path):
        if os.path.exists(local_file_path):
            resp = OssUtil._do_upload(local_file_path, oss_file_path)
            if resp is not None:
                return OssUtil.host % oss_file_path
            return ''
        else:
            log.info('没有匹配到相符合的文件')
            raise FileNotFoundError(f"没有匹配到相符合的文件")

    @staticmethod
    def _do_upload(local_file_path, oss_file_path):
        index = 0
        auth = oss2.Auth(OssUtil.ak, OssUtil.sk)
        bucket = oss2.Bucket(auth, OssUtil.endpoint, OssUtil.bucket, connect_timeout=5)
        with open(local_file_path, 'rb') as f:
            while 1:
                try:
                    str_c = bucket.put_object(oss_file_path, f)
                except:
                    log.error(f"上传OSS异常，失败次数：{index} {traceback.format_exc()}")
                    if index == 10:
                        return None
                    index += 1
                else:
                    str_status = str_c.status
                    str_content = str_c.resp
                    if str_status != 200:
                        requests.post(Constants.MESSAGE_URL, data=f"OSS文件上传失败;状态码：{str(str_status)}, 错误信息：{str(str_content)}")
                        return None
                return oss_file_path
    @staticmethod
    def load_oss_config():
        count = 0
        while count < 3:
            try:
                if count == 0:
                    url = "https://app.bookln.cn/antxconfigservice/getantx.do"
                else:
                    url = "https://app-prepub.bookln.cn/antxconfigservice/getantx.do"
                with requests.session() as session:
                    response = session.post(url, data={"appCode": "bookln", "projectName": "correction", "env": "product"})
                    log.info(f"加载OSS配置信息，状态码: {response.status_code}")
                    if response.status_code == 200:
                        data_json = response.json()
                        if data_json.get('success', False) and data_json.get('data', None):
                            OssUtil.ak = data_json['data']['oss_ak']
                            OssUtil.sk = data_json['data']['oss_sk']
                            OssUtil.bucket = data_json['data']['oss_bucket']
                            OssUtil.endpoint = data_json['data']['oss_endpoint']
                            OssUtil.host = data_json['data']['oss_host'] + '/%s'
                            break
                        else:
                            log.error(f"加载OSS配置信息失败，返回结果：{json.dumps(data_json, ensure_ascii=False)}")
                            if data_json.get('msg', None) == '当前ip无法获取配置信息':
                                count = 100
            except:
                log.error(f"加载OSS配置信息异常\n{traceback.format_exc()}")
            finally:
                count += 1
        if OssUtil.ak is None or OssUtil.sk is None:
            try:
                hostname = socket.gethostname()
                _local_ip = socket.gethostbyname(hostname)
            except Exception as e:
                _local_ip = 'Error'
            try:
                response = requests.post(Constants.MESSAGE_URL, data=f"当前服务器{_local_ip}无法访问OSS，请立即查看！".encode('utf-8'))
                log.debug(f"通知飞书成功，返回结果：{response.text}")
            except:
                log.error(f"Oops!!! bad request {Constants.MESSAGE_URL} error by exception\r\n{traceback.format_exc()}")
            try:
                response = requests.post("http://127.0.0.1:18886/stop", data="{}")
                log.info(f"请求停止获取任务，返回结果： {response.text}")
            except:
                log.error(f"Oops!!! bad request http://127.0.0.1:18886/stop error by exception\r\n{traceback.format_exc()}")
            sys.exit(0)
