# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/29 16:08 
# @Description  : util_of_detection_cpu.py

import cv2
import numpy as np

class ImageProcessorCpu:
    @staticmethod
    def draw_contours_cut(image, largest_contour):
        largest_contour = np.array(largest_contour)

        epsilon = 0.01 * cv2.arcLength(largest_contour, True)
        approx = cv2.approxPolyDP(largest_contour, epsilon, True)
        x, y, w, h = cv2.boundingRect(largest_contour)

        avg_color = np.mean(image, axis=(0, 1)).astype(np.uint8)

        # 创建一个与图像相同大小的数组，其中每个通道都是图像的平均值
        mask_ = np.full_like(image, avg_color)

        # 创建一个与图像相同大小的数组，将轮廓外的部分设为0，内部设为255
        mask_temp = np.zeros_like(image, dtype=np.uint8)
        cv2.drawContours(mask_temp, [approx], -1, (255, 255, 255), thickness=cv2.FILLED)

        # 将mask_temp中为0的位置替换为image中的对应位置的像素值
        mask_[mask_temp == 255] = image[mask_temp == 255]

        color_scalar = tuple(avg_color.tolist())
        cv2.drawContours(mask_, [approx], -1, color_scalar, thickness=2, lineType=cv2.LINE_AA)

        # 裁剪图像
        crop_image = mask_[y:y + h, x:x + w]

        return crop_image


    @staticmethod
    def preprocess_warpAffine(image, dst_width=320, dst_height=320):
        scale = min((dst_width / image.shape[1], dst_height / image.shape[0]))
        ox = (dst_width - scale * image.shape[1]) / 2
        oy = (dst_height - scale * image.shape[0]) / 2
        M = np.array([
            [scale, 0, ox],
            [0, scale, oy]
        ], dtype=np.float32)

        img_pre = cv2.warpAffine(image, M, (dst_width, dst_height), flags=cv2.INTER_LINEAR,
                                 borderMode=cv2.BORDER_CONSTANT, borderValue=(225, 225, 225))
        IM = cv2.invertAffineTransform(M)
        return img_pre, IM

    @staticmethod
    def is_pure_black_or_white(img):
        img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 计算直方图
        hist = cv2.calcHist([img_gray], [0], None, [256], [0, 256])

        num = 0
        for b in range(255):
            if hist[b].item() == 0:
                num += 1

        ratio = num / 256
        return ratio