# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/7 19:12 
# @Description  : util_of_dict.py

class DictUtil:
    @staticmethod
    def replace_dict_key(src_dict, src_key, dest_key):
        dest_dict = {}
        if src_dict is not None:
            for key, value in src_dict.items():
                dest_dict[key.replace(src_key, dest_key)] = value
            return dest_dict
        return src_dict