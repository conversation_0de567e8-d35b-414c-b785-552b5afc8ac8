# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/5 9:58
import base64
import io
import os
import time
import traceback
import uuid

import cv2
import numpy as np
import requests
from PIL import Image, ImageDraw, ImageFont

from .util_of_file import FileUtil
from ..constants.constants import Constants
from ..logger import LoggerFactory
from ..auto_setting import AutoSetting
log = LoggerFactory.get_logger('ImageUtil')

class ImageUtil:
    cdn_internal_host_map = {
        'https://cdnbj.bookln.cn': "https://ajlivebj.oss-cn-beijing-internal.aliyuncs.com"
    }
    @staticmethod
    def base64_to_file(base64_string: str, image_store_path: str):
        try:
            byte_data = base64.b64decode(base64_string, validate=True)
            with open(image_store_path, "wb") as f:
                f.write(byte_data)
                f.flush()
        except:
            log.error(f'save image error:{traceback.format_exc()}')
    @staticmethod
    def get_size(img, max_side_len=960):
        if max(img.size) > max_side_len:
            w, h = [int(size * max_side_len / max(img.size)) for size in img.size]
            return w, h
        else:
            return img.size
    @staticmethod
    def preprocess_batch(img):
        img_pre = (img[..., ::-1] / 255.0).astype(np.float32)
        img_pre = img_pre.transpose(2, 0, 1)
        return img_pre

    @staticmethod
    def numpy_2_base64(img, img_format='.jpg'):
        """
        numpy图片转base64字符串
        参数:
        :param img: numpy图像
        :param img_format: image format
        返回:
        :return @str
        """
        byte_data = ImageUtil.numpy_2_bytes(img, img_format)
        base_64_str = base64.b64encode(byte_data).decode('utf-8')
        return base_64_str
    @staticmethod
    def base64_2_numpy(base_64_str, flags: int = cv2.IMREAD_COLOR):
        """
        base64字符串转numpy图片
        参数:
        :param base_64_str: base64字符串
        :param flags: Color code
        返回:
        :return @numpy
        """
        byte_data = base64.b64decode(base_64_str)
        return ImageUtil.bytes_2_numpy(byte_data, flags)

    @staticmethod
    def bytes_2_numpy(byte_data, flags: int = cv2.IMREAD_COLOR):
        """
        二进制数据转numpy图像
        参数:
        :param byte_data: 二进制数据
        :param flags: Color code
        返回:
        :return @numpy
        """
        img_buff = np.frombuffer(byte_data, dtype=np.uint8)
        img = cv2.imdecode(img_buff, flags)
        return img
    @staticmethod
    def numpy_2_bytes(img, img_fmt='.jpg'):
        """
        numpy图像转二进制数据
        参数:
        :param img: numpy图像
        :param img_fmt: image format
        返回:
        :return @bytes
        """
        success, encoded_img = cv2.imencode(img_fmt, img)
        byte_data = encoded_img.tobytes()
        return byte_data
    @staticmethod
    def numpy_2_pil_bytes(img, img_fmt='.jpg'):
        """
        numpy图像转PIL二进制数据
        参数:
        :param img: numpy图像
        返回:
        :return @bytes
        """
        pil_img = Image.fromarray(img)
        img_byte_arr = io.BytesIO()
        if img_fmt == '.png':
            pil_img.save(img_byte_arr, format='PNG')
        else:
            pil_img.save(img_byte_arr, format='JPEG', quality=95)
        img_data = img_byte_arr.getvalue()
        return img_data
    @staticmethod
    def pil_bytes_2_numpy(img_data):
        """
        PIL二进制数据转numpy图像
        参数:
        :param img_data: PIL二进制数据
        返回:
        :return @numpy
        """
        img_byte_arr = io.BytesIO(img_data)
        pil_img = Image.open(img_byte_arr)
        return np.array(pil_img)

    @staticmethod
    def dis_compute(p1, p2):
        return np.sqrt(np.sum(np.square(p1 - p2)))

    # 根据坐标抠取图像
    @staticmethod
    def cut_img(im_reg, p):
        if not isinstance(p[0], list):
            xmin, ymin, xmax, ymax = p
            img = im_reg[ymin:ymax + 1, xmin:xmax + 1, :]
        else:
            # update by dewen
            if isinstance(p[0][0], int):
                pts1 = np.array(p, np.float32).reshape(-1, 2)
                h_c = int((ImageUtil.dis_compute(pts1[0], pts1[3]) + ImageUtil.dis_compute(pts1[1], pts1[2])) // 2)
                w_c = int((ImageUtil.dis_compute(pts1[0], pts1[1]) + ImageUtil.dis_compute(pts1[2], pts1[3])) // 2)
                pts2 = np.float32([[0, 0], [w_c, 0], [w_c, h_c], [0, h_c]])
                mat = cv2.getPerspectiveTransform(pts1, pts2)
                img = cv2.warpPerspective(im_reg, mat, (w_c, h_c))
            elif isinstance(p[0][0], list):
                tmp_list = []
                for i in range(len(p)):
                    pts1 = np.array(p[i], np.float32).reshape(-1, 2)
                    h_c = int((ImageUtil.dis_compute(pts1[0], pts1[3]) + ImageUtil.dis_compute(pts1[1], pts1[2])) // 2)
                    w_c = int((ImageUtil.dis_compute(pts1[0], pts1[1]) + ImageUtil.dis_compute(pts1[2], pts1[3])) // 2)
                    pts2 = np.float32([[0, 0], [w_c, 0], [w_c, h_c], [0, h_c]])
                    mat = cv2.getPerspectiveTransform(pts1, pts2)
                    img = cv2.warpPerspective(im_reg, mat, (w_c, h_c))
                    tmp_list.append(img)
                return tmp_list
            # update by dewen
            #pts1 = np.array(p, np.float32).reshape(-1, 2)
            #h_c = int((ImageUtil.dis_compute(pts1[0], pts1[3]) + ImageUtil.dis_compute(pts1[1], pts1[2])) // 2)
            #w_c = int((ImageUtil.dis_compute(pts1[0], pts1[1]) + ImageUtil.dis_compute(pts1[2], pts1[3])) // 2)
            #pts2 = np.float32([[0, 0], [w_c, 0], [w_c, h_c], [0, h_c]])
            #mat = cv2.getPerspectiveTransform(pts1, pts2)
            #img = cv2.warpPerspective(im_reg, mat, (w_c, h_c))
        return img

    @staticmethod
    def crop_image(img, points):
        left = int(np.min(points[:, 0]))
        right = int(np.max(points[:, 0]))
        top = int(np.min(points[:, 1]))
        bottom = int(np.max(points[:, 1]))
        try:
            img_crop = img[top:bottom + 1, left:right + 1, :].copy()
        except:
            img_crop = img[top:bottom + 1, left:right + 1].copy()
        return img_crop
    @staticmethod
    def _update_cdn_map():
        try:
            config = AutoSetting.get_config()
            ImageUtil.cdn_internal_host_map = config['cdn_internal_host_map']
        except:
            ImageUtil.cdn_internal_host_map = {
                'https://cdnbj.bookln.cn': "https://ajlivebj.oss-cn-beijing-internal.aliyuncs.com"
            }
    @staticmethod
    def fetch_user_image(image_url: str, target_path: str, replace_url=True, retry=False):
        try:
            url = image_url
            if replace_url:
                ImageUtil._update_cdn_map()
                for k, v in ImageUtil.cdn_internal_host_map.items():
                    if k in url:
                        url = url.replace(k, v)

            log.info(f"开始下载用户图片 {url}")
            with requests.session() as session:
                response = session.get(url, timeout=3)
                FileUtil.write_file(target_path, response.content, 'wb')
            return os.path.exists(target_path)
        except:
            if retry:
                log.error(f"用户图片下载异常，地址: {image_url} \n {traceback.format_exc()}")
                return False
            log.error(f"用户图片下载异常，地址: {image_url} \n {traceback.format_exc()}")
            return ImageUtil.fetch_user_image(image_url, target_path, False, True)
    @classmethod
    def save_vertical_det(cls, img, boxes):
        try:
            for i, bb in enumerate(boxes):
                bb = np.array(bb)
                cv2.polylines(img, [bb.astype(np.int32)], isClosed=True, color=(0, 0, 255), thickness=1)
            cv2.imwrite(os.path.join(Constants.VIS_VERTICAL_DET_PATH, f"vertical_det_{time.time()}.jpg"), img)
        except:
            pass
    @classmethod
    def save_quick_det(cls, img, boxes):
        try:
            for i, item in enumerate(boxes):
                item_box = item['bb']
                b0 = item_box[0]
                b1 = item_box[1]
                boxex = item['rec_boxs']
                for boxs in boxex:
                    for point in boxs:
                        point[0] += b0
                        point[1] += b1
                    box = np.array(boxs)
                    cv2.polylines(img, [box.astype(np.int32)], isClosed=True, color=(255, 0, 0), thickness=1)
            cv2.imwrite(os.path.join(Constants.VIS_QUICK_DET_PATH, f"quick_det_{time.time()}.jpg"), img)
        except:
            log.error(f"{traceback.format_exc()}")
            pass
    @classmethod
    def save_quick_rec(cls, img, rec_res):
        try:
            if img is None:
                return
            for item in rec_res:
                box = item['box']
                pred = item['pred'][0]
                xmin = box[0]
                ymin = box[1]
                xmax = box[2]
                ymax = box[3]

                img = cls.drawChinese(img, pred, (xmin, ymin), fontSize=10, fontColor=(0, 0, 255))
                img = cv2.rectangle(img, (xmin, ymin), (xmax, ymax), (255, 0, 0), 1)

            cv2.imwrite(os.path.join(Constants.VIS_QUICK_REC_PATH, f"quick_rec_{time.time()}.jpg"), img)
        except:
            log.error(f"{traceback.format_exc()}")

    @classmethod
    def drawChinese(cls, img, text, position, fontSize=20, fontColor=(0, 0, 255)):
        # args-(img:numpy.ndarray, text:中文文本, position:位置, fontSize:字体大小默认20, fontColor:字体颜色默认绿色)
        cv2img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # cv2和PIL中颜色的hex码的储存顺序不同
        pilimg = Image.fromarray(cv2img)
        # PIL图片上打印汉字
        draw = ImageDraw.Draw(pilimg)  # 图片上打印
        font = ImageFont.truetype(f"{Constants.MODEL_WEIGHT_PATH}/SimHei.ttf", fontSize,
                                  encoding="utf-8")  # 参数1：字体文件路径，参数2：字体大小
        draw.text(position, text, fontColor, font=font)  # 参数1：打印坐标，参数2：文本，参数3：字体颜色，参数4：字体格式
        cv2charimg = cv2.cvtColor(np.array(pilimg), cv2.COLOR_RGB2BGR)  # PIL图片转cv2 图片

        return cv2charimg
    @classmethod
    def save_vertical_rec(cls, img, boxs, answers):
        try:
            img_tmp = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(img_tmp)
            for i in range(len(boxs)):
                shushi_box = boxs[i]
                shushi_reg = answers[i]
                draw.rectangle([int(shushi_box[0]), int(shushi_box[1]), int(shushi_box[2]), int(shushi_box[3])],
                               outline=(255, 0, 0))
                draw.text((int(shushi_box[0]), int(shushi_box[1])), str(shushi_reg), align="left")
            img_tmp.save(os.path.join(Constants.VIS_VERTICAL_REC_PATH, f"vertical_rec_{time.time()}.jpg"))
        except:
            log.error(f"save error {traceback.format_exc()}")
            pass

    @classmethod
    def merge_and_save(cls, save_path, img_left, img_right):
        try:
            # 获取两个图像的高度
            height_left, width_left, _ = img_left.shape
            height_right, width_right, _ = img_right.shape

            # 计算合并后的图像宽度和高度
            total_width = width_left + width_right + 20
            max_height = max(height_left, height_right)

            # 创建一个新的空白图像，背景为白色
            merged_img = np.ones((max_height, total_width, 3), dtype=np.uint8) * 255
            # 将左侧图像粘贴到新图像上
            merged_img[:height_left, :width_left] = img_left
            # 将右侧图像粘贴到新图像上
            merged_img[:height_right, width_left + 20:width_left + 20 + width_right] = img_right
            # 保存合并后的图像
            cv2.imwrite(save_path, merged_img)
        except:
            log.error(f"保存合并后的图像异常 \n {traceback.format_exc()}")
