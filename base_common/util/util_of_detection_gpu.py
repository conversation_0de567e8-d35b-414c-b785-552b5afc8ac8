# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/29 16:08 
# @Description  : util_of_detection_gpu.py

import torch
import numpy as np
import torch.nn.functional as F
import torchvision.ops as ops


class ImageProcessorGpu:
    @staticmethod
    def NMS(boxes, iou_thres):
        if len(boxes) == 0:  # 检查是否为空张量
            return []
        boxes = torch.tensor(boxes)
        keep = ops.nms(boxes[:, :4], boxes[:, 4], iou_thres)
        return boxes[keep].tolist()

    @staticmethod
    def postprocess(pred, conf_thres=0.7, iou_thres=0.3):
        boxes = []
        for item in pred[0]:
            cx, cy, w, h = item[:4]
            confidence = item[4]
            if confidence < conf_thres:
                continue
            left = cx - w * 0.5
            top = cy - h * 0.5
            right = cx + w * 0.5
            bottom = cy + h * 0.5
            # 在这里，因为只有一个类别，您可以直接使用 0 作为类别索引
            boxes.append([left, top, right, bottom, confidence, 0, *item[-32:]])
        return ImageProcessorGpu.NMS(boxes, iou_thres)

    @staticmethod
    def crop_mask(masks, boxes):
        n, h, w = masks.shape
        x1, y1, x2, y2 = torch.chunk(boxes[:, :, None], 4, 1)  # x1 shape(n,1,1)
        r = torch.arange(w, device=masks.device, dtype=x1.dtype)[None, None, :]  # rows shape(1,1,w)
        c = torch.arange(h, device=masks.device, dtype=x1.dtype)[None, :, None]  # cols shape(1,h,1)
        return masks * ((r >= x1) * (r < x2) * (c >= y1) * (c < y2))

    @staticmethod
    def process_mask(protos, masks_in, bboxes, shape, upsample=False):
        c, mh, mw = protos.shape  # CHW
        ih, iw = shape
        masks = (masks_in.float() @ protos.float().view(c, -1)).sigmoid().view(-1, mh, mw)  # CHW
        downsampled_bboxes = bboxes.clone()
        downsampled_bboxes[:, 0] *= mw / iw
        downsampled_bboxes[:, 2] *= mw / iw
        downsampled_bboxes[:, 3] *= mh / ih
        downsampled_bboxes[:, 1] *= mh / ih
        masks = ImageProcessorGpu.crop_mask(masks, downsampled_bboxes)  # CHW
        if upsample:
            masks = F.interpolate(masks[None], shape, mode='bilinear', align_corners=False)[0]  # CHW
        return masks.gt_(0.5)

    @staticmethod
    def detect_and_process(out, img_pre_shape):
        output0 = out[0].transpose(-1, -2).cpu()  # 1,8400,116 检测头输出
        output1 = out[1][2][0].cpu()  # 32,160,160 分割头输出
        pred = ImageProcessorGpu.postprocess(output0, conf_thres=0.7)
        pred = torch.from_numpy(np.array(pred).reshape(-1, 38))
        if len(pred) > 0:
            masks = ImageProcessorGpu.process_mask(output1, pred[:, 6:], pred[:, :4], img_pre_shape, True)
            return masks
        else:
            return []
