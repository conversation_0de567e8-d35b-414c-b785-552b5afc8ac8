# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/7 16:03
# @Description  : 文件工具类，包含各种文件操作
import os
from ..logger import LoggerFactory
log = LoggerFactory.get_logger('FileUtil')

class FileUtil:
    @staticmethod
    def remove_file(file_path: str):
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except:
            return False

    @staticmethod
    def write_file(file_path: str, content, mode='a'):
        with open(file_path, mode) as file:
            file.write(content)
            file.flush()

    @staticmethod
    def read_file(file_path: str, mode='r'):
        with open(file_path, mode=mode) as file:
            content = file.read()
            return content
