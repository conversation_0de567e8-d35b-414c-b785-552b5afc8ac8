# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/13 15:14 
# @Description  : util_of_gpu.py

import traceback

from ..logger import LoggerFactory
log = LoggerFactory.get_logger('GPUUtil')

class GPUUtil:
    @staticmethod
    def set_memory_growth(tf, growth_enable):
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, growth_enable)
            except RuntimeError:
                log.error(f"Oops!!! set_memory_growth exception {traceback.format_exc()}")

    @staticmethod
    def set_memory_limit(tf, limit_size=1024):
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_virtual_device_configuration(gpu,
                        [tf.config.experimental.VirtualDeviceConfiguration(memory_limit=limit_size)]
                    )
            except RuntimeError:
                log.error(f"Oops!!! set_memory_limit exception {traceback.format_exc()}")