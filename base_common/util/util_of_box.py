# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/7/2 15:17 
# @Description  : util_of_box.py
import re
import cv2
import numpy as np
from .util_of_image import ImageUtil

class BoxUtil:
    @staticmethod
    def custom_analyzer(text):
        # 使用正则表达式拆分字符串，既考虑空格，也考虑数字
        parts = re.findall(r'\d|\S+', text)  # 匹配单个数字或非空白字符
        return parts
    @staticmethod
    def crop_items(img, boxs):
        results = []
        for box in boxs:
            item = {'box': np.array(box), 'img': None}
            pts1 = np.array(box, np.float32).reshape(-1, 2)
            pts1 = cv2.minAreaRect(pts1)
            points = sorted(list(cv2.boxPoints(pts1)), key=lambda x: x[0])

            if points[1][1] > points[0][1]:
                index_1 = 0
                index_4 = 1
            else:
                index_1 = 1
                index_4 = 0

            if points[3][1] > points[2][1]:
                index_2 = 2
                index_3 = 3
            else:
                index_2 = 3
                index_3 = 2

            pts1 = np.array([points[index_1], points[index_2], points[index_3], points[index_4]])
            h_c = int((ImageUtil.dis_compute(pts1[0], pts1[3]) + ImageUtil.dis_compute(pts1[1], pts1[2])) // 2)
            w_c = int((ImageUtil.dis_compute(pts1[0], pts1[1]) + ImageUtil.dis_compute(pts1[2], pts1[3])) // 2)
            pts2 = np.float32([[0, 0], [w_c, 0], [w_c, h_c], [0, h_c]])
            M = cv2.getPerspectiveTransform(pts1, pts2)
            img_crop = cv2.warpPerspective(img, M, (w_c, h_c))
            item['img'] = img_crop
            results.append(item)
        return results

    @classmethod
    def box_to_list(cls, items):
        res_boxs = []
        for item in items:
            res_boxs.append([
                item['x1'],
                item['y1'],
                item['x2'],
                item['y2']
            ])
        return res_boxs