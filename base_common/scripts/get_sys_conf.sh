#!/bin/bash

declare -A conda_map=(
    [server_correction]="opencv-gpu"
    [server_surf]="opencv-gpu"
    [server_detection]="base"
    [server_direction]="base"
    [server_embedding]="pytorch"
    [server_quick_correction]="pytorch"
    [server_answer_det]="pytorch"
    [server_graphics]="opencv-gpu"
    [server_line]="pytorch"
    [server_zone_align]="pytorch"
    [server_quick_det]="pytorch"
    [server_quick_rec]="pytorch"
    [server_vertical_rec]="pytorch"
    [server_vertical_det]="pytorch"
    [server_print_ocr_det]="ocr_det"
    [server_print_ocr_rec]="pytorch"
    [server_ch_hw]="pytorch"
    [server_en_hw]="trocr_gpu_en"
    [server_formula_hw]="base"
)

case "$1" in
    EnvType)
        echo "product"
        ;;
    Conda)
        echo "${conda_map[$2]}"
        ;;
    WorkHome)
        echo "/usr/servering/correction_runtime"
        ;;
    *)
        echo "/usr/servering/correction_runtime"
        ;;
esac
