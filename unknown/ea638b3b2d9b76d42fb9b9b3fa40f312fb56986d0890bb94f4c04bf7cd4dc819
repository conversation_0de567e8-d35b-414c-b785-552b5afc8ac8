# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> yanhui
# @Date         ：2024/6/4 17:14

import threading
from queue import Queue

from quick_correction_service.service import QuickCorrectionService

from base_common import LoggerFactory
log = LoggerFactory.get_logger('ServicePool')

class _PrivateServicePool:
    def __init__(self):
        self.__service_pool: Queue = None
        self.__pool_lock = threading.Lock()

    def __create_service(self):
        return QuickCorrectionService()

    def initialize(self, max_size):
        self.__service_pool = Queue(max_size)
        for i in range(max_size):
            self.__service_pool.put(self.__create_service())
            
    def get_service(self):
        with self.__pool_lock:
            if not self.__service_pool.empty():
                return False, self.__service_pool.get()

        log.warn("****************************************************")
        log.warn(f"服务器资源不足，启用临时服务")
        log.warn("****************************************************")
        return True, self.__create_service()

    def release_service(self, service):
        if not self.__service_pool.full():
            self.__service_pool.put(service)

class ServicePool:
    @classmethod
    def initialize(cls, max_size=2):
        log.info("服务对象池正在初始化...")
        cls.__service_pool = _PrivateServicePool()
        cls.__service_pool.initialize(max_size)
        log.info("服务对象池已初始化")

    @classmethod
    def get_service(cls) -> QuickCorrectionService:
        return cls.__service_pool.get_service()

    @classmethod
    def release_service(cls, service: QuickCorrectionService):
        cls.__service_pool.release_service(service)
