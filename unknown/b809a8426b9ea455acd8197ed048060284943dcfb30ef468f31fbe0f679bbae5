# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/7/3 9:01 
# @Description  : data_of_response.py
import json
import traceback

from base_common import LoggerFactory
log = LoggerFactory.get_logger('AnswerDetectService')
class ADResponse:
    def __init__(self, flag=1000, boxs=None, types=None, recs=None, answers=None, stems=None):
        if boxs is None:
            boxs = {}
        if types is None:
            types = {}
        if recs is None:
            recs = {}
        if answers is None:
            answers = {}
        if stems is None:
            stems = {}
        self.__flag = flag
        self.__boxs = boxs
        self.__types = types
        self.__recs = recs
        self.__answers = answers
        self.__stems = stems
        self.__np_boxs = {}


    def get_flag(self):
        return self.__flag

    def get_boxs(self):
        return self.__boxs

    def get_types(self):
        return self.__types

    def get_recs(self):
        return self.__recs

    def get_answers(self):
        return self.__answers

    def get_stems(self):
        return self.__stems
    def get_np_boxs(self):
        return self.__np_boxs

    def set_flag(self, flag):
        self.__flag = flag

    def set_boxs(self, boxs):
        self.__boxs = boxs

    def set_types(self, types):
        self.__types = types

    def set_recs(self, recs):
        self.__recs = recs

    def set_answers(self, answers):
        self.__answers = answers

    def set_stems(self, stems):
        self.__stems = stems
    def set_np_boxs(self, np_boxs):
        self.__np_boxs = np_boxs

    def is_success(self):
        return self.__flag == 1000

    def to_string(self):
        output = self.to_dict()
        output.pop('np_boxs')
        return json.dumps(output, ensure_ascii=False)

    def to_dict(self):
        return {
            'flag': self.__flag,
            'boxs': self.__boxs,
            'types': self.__types,
            'recs': self.__recs,
            'stems': self.__stems,
            'np_boxs': self.__np_boxs,
            'answers': self.__answers
        }

class ADPrepub:
    def __init__(self, item_ids=None, item_boxs=None, item_box_ids=None, res_boxs=None, res_types=None, success=True):
        if item_ids is None:
            item_ids = []
        if item_boxs is None:
            item_boxs = []
        if item_box_ids is None:
            item_box_ids = []
        if res_boxs is None:
            res_boxs = {}
        if res_types is None:
            res_types = {}
        self.__item_ids = item_ids
        self.__item_boxs = item_boxs
        self.__item_box_ids = item_box_ids
        self.__res_boxs = res_boxs
        self.__res_types = res_types
        self.__success = success

        self.__w = None
        self.__h = None
        self.__img_name = None
        self.__item_keys = None
        self.__items_type = None
        self.__img_photo_cv2 = None
        self.__img_photo = None
        self.__save_id = None
        self.__boxs_list_4p = None
        self.__boxs_list_2p = None
        self.__boxs_list_np = None
        self.__shushi_boxs = None
        self.__shushi_regs = None
    def set_success(self, success):
        self.__success = success
    def is_success(self):
        return self.__success
    @staticmethod
    def from_dict(boxs_json):
        try:
            # 获取答题区域的信息，答案题目id，答案区域box，答案区域id, 每个答案位置对应的作答默认为空
            ad_pre = ADPrepub()
            for bb in boxs_json.keys():
                box_id = bb
                item_id = box_id.split('_')[0]

                ad_pre.get_item_ids().append(item_id)
                ad_pre.get_item_boxs().append(boxs_json[bb])
                ad_pre.get_item_box_ids().append(box_id)
                ad_pre.get_res_boxs()[bb] = []
                ad_pre.get_res_types()[bb] = -1
            log.debug("extract answer info done")
            ad_pre.set_success(True)
            return ad_pre
        except:
            log.error(f"error in extract_boxs!, due to {traceback.format_exc()}")
            ad_pre.set_success(False)
            return ad_pre


    def get_item_ids(self):
        return self.__item_ids

    def get_item_boxs(self):
        return self.__item_boxs

    def get_item_box_ids(self):
        return self.__item_box_ids

    def get_res_boxs(self):
        return self.__res_boxs

    def get_res_types(self):
        return self.__res_types

    def set_item_ids(self, item_ids):
        self.__item_ids = item_ids

    def set_item_boxs(self, item_boxs):
        self.__item_boxs = item_boxs

    def set_item_box_ids(self, item_box_ids):
        self.__item_box_ids = item_box_ids

    def set_res_boxs(self, res_boxs):
        self.__res_boxs = res_boxs

    def set_res_types(self, res_types):
        self.__res_types = res_types    
    
    def set_w(self, w):
        self.__w = w
    def set_h(self, h):
        self.__h = h
    def set_item_keys(self, item_keys):
        self.__item_keys = item_keys
    def set_img_name(self, img_name):
        self.__img_name = img_name
    def set_items_type(self, items_type):
        self.__items_type = items_type
    def set_img_photo_cv2(self, img_photo_cv2):
        self.__img_photo_cv2 = img_photo_cv2
    def set_img_photo(self, img_photo):
        self.__img_photo = img_photo
    def set_save_id(self, save_id):
        self.__save_id = save_id
    def set_boxs_list_4p(self, boxs_list_4p):
        self.__boxs_list_4p = boxs_list_4p
    def set_boxs_list_2p(self, boxs_list_2p):
        self.__boxs_list_2p = boxs_list_2p
    def set_boxs_list_np(self, boxs_list_np):
        self.__boxs_list_np = boxs_list_np
    def set_shushi_boxs(self, shushi_boxs):
        self.__shushi_boxs = shushi_boxs
    def set_shushi_regs(self, shushi_regs):
        self.__shushi_regs = shushi_regs

    def get_w(self):
        return self.__w
    def get_h(self):
        return self.__h
    def get_img_name(self):
        return self.__img_name
    def get_item_keys(self):
        return self.__item_keys
    def get_items_type(self):
        return self.__items_type
    def get_img_photo_cv2(self):
        return self.__img_photo_cv2
    def get_img_photo(self):
        return self.__img_photo
    def get_save_id(self):
        return self.__save_id
    def get_boxs_list_4p(self):
        return self.__boxs_list_4p
    def get_boxs_list_2p(self):
        return self.__boxs_list_2p
    def get_boxs_list_np(self):
        return self.__boxs_list_np
    def get_shushi_boxs(self):
        return self.__shushi_boxs
    def get_shushi_regs(self):
        return self.__shushi_regs