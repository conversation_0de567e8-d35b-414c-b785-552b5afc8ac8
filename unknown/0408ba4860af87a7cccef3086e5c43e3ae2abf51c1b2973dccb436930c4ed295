# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/9/26 10:49 
# @Description  : start_task.py

import subprocess
if __name__ == '__main__':
    try:
        # 执行 shell 脚本
        result = subprocess.run(['./scripts/task.sh'], capture_output=True, text=True, shell=True)
        # 获取输出和返回码
        output = result.stdout
        error = result.stderr
        return_code = result.returncode

        print("Output:", output)
        print("Error:", error)
        print("Return Code:", return_code)
    except:
        print("启动脚本失败")
    exit(0)