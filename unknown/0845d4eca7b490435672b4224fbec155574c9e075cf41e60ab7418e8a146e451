# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 16:44 
# @Description  : server_wrapper.py

import atexit
import tornado.ioloop
import tornado.httpserver

class ParamLoader:
    def __init__(self, params):
        self.__success = True
        if len(params) == 1:
            self.__port = params[0]
            self.__gpu_num = '0'
            self.__stop_time = 0
        elif len(params) == 2:
            self.__port = params[0]
            self.__gpu_num = params[1]
            self.__stop_time = 0
        elif len(params) == 3:
            self.__port = params[0]
            self.__gpu_num = params[1]
            self.__stop_time = params[2]
        else:
            self.__success = False

    def get_port(self): return self.__port
    def get_gpu_num(self): return self.__gpu_num
    def get_stop_time(self): return self.__stop_time
    def is_success(self): return self.__success

class ServerWrapper:
    def __init__(self, logger, server_name: str, server_port: int):
        self.__server_name = server_name
        self.__server_port = server_port
        self.__server_app = None
        self.__logger = logger

    def __shutdown(self, server_name, server_app):
        self.__logger.info(f"{server_name}正在终止...")
        if server_app is not None:
            server_app.stop()
        tornado.ioloop.IOLoop.current().stop()
        self.__logger.info(f"{server_name}已终止.Bye.")

    def start_server(self, make_app, setup):
        self.__logger.info(f"{self.__server_name}正在启动...")
        setup()
        application = make_app()

        try:
            server_app = tornado.httpserver.HTTPServer(application)
            server_app.bind(self.__server_port)
            server_app.start()
            self.__logger.info(f"{self.__server_name}已启动.")
            atexit.register(self.__shutdown, self.__server_name, server_app)
            tornado.ioloop.IOLoop.current().start()
        except KeyboardInterrupt:
            self.__logger.info(f"请求终止{self.__server_name}")
