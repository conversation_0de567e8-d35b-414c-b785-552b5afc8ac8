# -*- coding: utf-8 -*-
# @Time    : 2019/8/23 21:56
# <AUTHOR> <PERSON><PERSON><PERSON>
from torch import nn
import torch

from answer_det_service.model.models.losses.basic_loss import BalanceCrossEntropyLoss, MaskL1Loss, DiceLoss


class DBLoss(nn.Module):
    def __init__(self, alpha=1.0, beta=10, ohem_ratio=3,loss2=False,focal_flag=False, reduction='mean', eps=1e-6):
        """
        Implement PSE Loss.
        :param alpha: binary_map loss 前面的系数
        :param beta: threshold_map loss 前面的系数
        :param ohem_ratio: OHEM的比例
        :param reduction: 'mean' or 'sum'对 batch里的loss 算均值或求和
        """
        super().__init__()
        assert reduction in ['mean', 'sum'], " reduction must in ['mean','sum']"
        self.alpha = alpha
        self.beta = beta
        self.loss2 = loss2
        self.bce_loss = BalanceCrossEntropyLoss(negative_ratio=ohem_ratio,focal_flag=focal_flag)
        self.dice_loss = Di<PERSON>Loss(eps=eps)
        self.l1_loss = MaskL1Loss(eps=eps)
        self.ohem_ratio = ohem_ratio
        self.reduction = reduction

    def forward(self, preds, batch, cls_list,cls_weight):
        metrics = {}
        loss_shrink = torch.tensor(0.).cuda()
        loss_threshold = torch.tensor(0.).cuda()
        loss_binary = torch.tensor(0.).cuda()
        loss = torch.tensor(0.).cuda()
        big_loss_record = {}
        for i in range(len(cls_list)):
            
            weight = cls_weight[i]
            cls_ = cls_list[i]
            pred = preds[i]
            big_loss_record[cls_]=[]
            shrink_maps = pred[:, 0, :, :]
            threshold_maps = pred[:, 1, :, :]
            binary_maps = pred[:, 0, :, :]
            
            if 'small_weight_'+cls_ in batch.keys():
                loss_shrink_maps,loss_n = self.bce_loss(shrink_maps, batch['shrink_map_'+cls_], batch['shrink_mask_'+cls_],batch['small_weight_'+cls_])
            else:
                loss_shrink_maps,loss_n = self.bce_loss(shrink_maps, batch['shrink_map_'+cls_], batch['shrink_mask_'+cls_])
            for n,ll in enumerate(loss_n):
                if ll > 0.15:
                    big_loss_record[cls_].append(n)
                
                
            loss_shrink += loss_shrink_maps*weight
            
            if 'big_weight_'+cls_ in batch.keys():
                loss_threshold_maps = self.l1_loss(threshold_maps, batch['threshold_map_'+cls_], batch['threshold_mask_'+cls_],batch['big_weight_'+cls_])
            else:
                loss_threshold_maps = self.l1_loss(threshold_maps, batch['threshold_map_'+cls_], batch['threshold_mask_'+cls_])
            loss_threshold += loss_threshold_maps*weight
        
            if pred.size()[1] >= 2:
                loss_binary_maps = self.dice_loss(binary_maps, batch['shrink_map_'+cls_], batch['shrink_mask_'+cls_])
                loss_binary += loss_binary_maps*weight
                if self.loss2:
                    loss_all = self.alpha * loss_shrink_maps + self.beta * loss_threshold_maps
                else:
                    loss_all = self.alpha * loss_shrink_maps + self.beta * loss_threshold_maps + loss_binary_maps
                loss += loss_all*weight
                
#                 loss = loss_shrink_maps*weight
                metrics['loss_'+cls_] = loss_all*weight
            else:
                loss_all = loss_shrink_maps
                loss += loss_all*weight
                metrics['loss_'+cls_] = loss_all*weight
                
        
        metrics['loss_shrink_maps'] = loss_shrink
        metrics['loss_threshold_maps'] = loss_threshold
        metrics['loss_binary_maps'] = loss_binary
        metrics['loss'] = loss
        metrics['big_loss_record'] = big_loss_record
        
        return metrics
