import numpy as np
import cv2

def putGaussianMaps(center, weight_map,sigma=5):
    grid_y,grid_x = weight_map.shape
    
    y_range = [i for i in range(int(grid_y))]
    x_range = [i for i in range(int(grid_x))]
    xx, yy = np.meshgrid(x_range, y_range)
    d2 = (xx - center[0]) ** 2 + (yy - center[1]) ** 2
    exponent = d2 / 2.0 / sigma / sigma
    mask = exponent <= 4.6052
    cofid_map = np.exp(-exponent)
    # print(cofid_map.max())
    cofid_map = np.multiply(mask, cofid_map)
#     print(cofid_map.shape)
    weight_map += cofid_map
    weight_map[weight_map > 1.0] = 1.0
    
def shrink_polygon_py(polygon, shrink_ratio,big_weight,small_weight):
    """
    对框进行缩放，返回去的比例为1/shrink_ratio 即可
    """
    cx = polygon[:, 0].mean()
    cy = polygon[:, 1].mean()
    polygon[:, 0] = cx + (polygon[:, 0] - cx) * shrink_ratio
    polygon[:, 1] = cy + (polygon[:, 1] - cy) * shrink_ratio
    return polygon


def shrink_polygon_pyclipper(polygon, shrink_ratio,big_weight,small_weight):
    from shapely.geometry import Polygon
    import pyclipper
    polygon_shape = Polygon(polygon)
    distance = polygon_shape.area * (1 - np.power(shrink_ratio, 2)) / polygon_shape.length
#     distance = 4
    subject = [tuple(l) for l in polygon]
    
    padding = pyclipper.PyclipperOffset()
    padding.AddPath(subject, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)
    shrinked = padding.Execute(-distance)
    if shrinked == []:
        shrinked = np.array(shrinked)
    else:
        shrinked = np.array(shrinked[0]).reshape(-1, 2)
        
    return shrinked

def shrink_polygon_pyclipper_weight(polygon, shrink_ratio,big_weight,small_weight):
    from shapely.geometry import Polygon
    import pyclipper
    polygon_shape = Polygon(polygon)
#     distance = polygon_shape.area * (1 - np.power(shrink_ratio, 2)) / polygon_shape.length
    distance = 4
    subject = [tuple(l) for l in polygon]
    
    padding = pyclipper.PyclipperOffset()
    padding.AddPath(subject, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)
    shrinked = padding.Execute(-distance)
    if shrinked == []:
        shrinked = np.array(shrinked)
    else:
        shrinked = np.array(shrinked[0]).reshape(-1, 2)
        
    for l in subject:
        putGaussianMaps(l,big_weight,distance/3)
    for pp in shrinked:
        putGaussianMaps(pp,small_weight,distance/3)
        
    return shrinked

class MakeShrinkMap():
    r'''
    Making binary mask from detection data with ICDAR format.
    Typically following the process of class `MakeICDARData`.
    '''

    def __init__(self, min_text_size=8, shrink_ratio=0.4, shrink_type='pyclipper_weight'):
        shrink_func_dict = {'py': shrink_polygon_py, 'pyclipper': shrink_polygon_pyclipper,'pyclipper_weight':shrink_polygon_pyclipper_weight}
        self.shrink_func = shrink_func_dict[shrink_type]
        self.min_text_size = min_text_size
        self.shrink_ratio = shrink_ratio
        self.shrink_type = shrink_type

    def __call__(self, data: dict,cls_list:list) -> dict:
        """
        从scales中随机选择一个尺度，对图片和文本框进行缩放
        :param data: {'img':,'text_polys':,'texts':,'ignore_tags':}
        :return:
        """
        image = data['img']
        text_polys = data['text_polys']
        texts = data['texts']
        ignore_tags = data['ignore_tags']

        h, w = image.shape[:2]
        text_polys, ignore_tags = self.validate_polygons(text_polys, ignore_tags, h, w)#ignore 面积小于0的标签
        
        
        for l_cls in cls_list:
            
            gt = np.zeros((h, w), dtype=np.float32)
            mask = np.ones((h, w), dtype=np.float32)
            
            gt_full = np.zeros((h, w), dtype=np.float32)
            mask_full = np.ones((h, w), dtype=np.float32)
            
            big_weight = np.zeros((h, w), dtype=np.float32)
            small_weight = np.zeros((h, w), dtype=np.float32)
            for i in range(len(text_polys)):
                text = texts[i]
                assert text in cls_list,'text must in cls_list'
                if text == l_cls:
                    polygon = np.array(text_polys[i])
                    height = max(polygon[:, 1]) - min(polygon[:, 1])
                    width = max(polygon[:, 0]) - min(polygon[:, 0])
                    if ignore_tags[i] or min(height, width) < self.min_text_size:#如果目标的外接正矩形的宽高低于设置值，该区域mask设置为0，即不计算loss，且ignore标签设置为True
                        cv2.fillPoly(mask, polygon.astype(np.int32)[np.newaxis, :, :], 0)
                        ignore_tags[i] = True
                    else:
                        shrinked = self.shrink_func(polygon, self.shrink_ratio,big_weight,small_weight)
                        if shrinked.size == 0:#如果thrink之后，没有得到多边形坐标，该区域mask设置为0，即不计算loss，且ignore标签设置为True
                            cv2.fillPoly(mask, polygon.astype(np.int32)[np.newaxis, :, :], 0)
                            ignore_tags[i] = True
                            continue
                        cv2.fillPoly(gt,[shrinked.astype(np.int32)], 1)#gt坐标是根据shrink_ratio向内进行了收缩
                            
                    if ignore_tags[i] or min(height, width) < self.min_text_size:
                        cv2.fillPoly(mask_full, polygon.astype(np.int32)[np.newaxis, :, :], 0)
                        ignore_tags[i] = True
                    else:
                        cv2.fillPoly(gt_full, polygon.astype(np.int32)[np.newaxis, :, :], 1)
                        
            data['shrink_map_'+l_cls] = gt
            data['shrink_mask_'+l_cls] = mask
            
            data['full_map_'+l_cls] = gt_full
            data['full_mask_'+l_cls] = mask_full
            if self.shrink_type == 'pyclipper_weight':
                data['small_weight_'+l_cls] = small_weight
                data['big_weight_'+l_cls] = big_weight
            
        return data

    def validate_polygons(self, polygons, ignore_tags, h, w):
        '''
        polygons (numpy.array, required): of shape (num_instances, num_points, 2)
        '''
        polygons_list = []
        if len(polygons) == 0:
            return polygons, ignore_tags
        assert len(polygons) == len(ignore_tags)
        for polygon in polygons:
            polygon = np.array(polygon)
            polygon[:, 0] = np.clip(polygon[:, 0], 0, w - 1)
            polygon[:, 1] = np.clip(polygon[:, 1], 0, h - 1)
            polygons_list.append(polygon)

        for i in range(len(polygons_list)):
            area = self.polygon_area(polygons_list[i])
            if abs(area) < 1:
                ignore_tags[i] = True
            if area > 0:
                polygons_list[i] = polygons_list[i][::-1, :]
        return polygons_list, ignore_tags

    def polygon_area(self, polygon):
        return cv2.contourArea(np.array(polygon,dtype=np.float32))
        # edge = 0
        # for i in range(polygon.shape[0]):
        #     next_index = (i + 1) % polygon.shape[0]
        #     edge += (polygon[next_index, 0] - polygon[i, 0]) * (polygon[next_index, 1] - polygon[i, 1])
        #
        # return edge / 2.


if __name__ == '__main__':
    from shapely.geometry import Polygon
    import pyclipper

    polygon = np.array([[0, 0], [100, 10], [100, 100], [10, 90]])
    a = shrink_polygon_py(polygon, 0.4)
    print(a)
    print(shrink_polygon_py(a, 1 / 0.4))
    b = shrink_polygon_pyclipper(polygon, 0.4)
    print(b)
    poly = Polygon(b)
    distance = poly.area * 1.5 / poly.length
    offset = pyclipper.PyclipperOffset()
    offset.AddPath(b, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)
    expanded = np.array(offset.Execute(distance))
    bounding_box = cv2.minAreaRect(expanded)
    points = cv2.boxPoints(bounding_box)
    print(points)
