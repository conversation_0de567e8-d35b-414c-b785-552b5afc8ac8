# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 13:37 
# @Description  : data_of_answer_response.py
import time
from base_common import BaseResponse


class AnswerResponse(BaseResponse):
    def __init__(self, st=None, et=time.time(), message=None, error_code=None, success=False, response=None,
                 all_ai_oral_calc=False, answer_item=None, align_info=None, surf_mat=None):
        super().__init__(st, et, message, error_code, success, response)
        self._all_ai_oral_calc = all_ai_oral_calc
        self._answer_item = answer_item
        if align_info is None:
            align_info = []
        self._align_info = align_info
        self._surf_mat = surf_mat

    def get_align_info(self):
        if self._align_info is None:
            self._align_info = []
        return self._align_info
    def set_align_info(self, align_info=None):
        if align_info is None:
            align_info = []
        self._align_info = align_info
        return self
    def is_all_ai_oral_calc(self): return self._all_ai_oral_calc
    def get_answer_item(self): return self._answer_item
    def get_surf_mat(self): return self._surf_mat
    def set_surf_mat(self, surf_mat): self._surf_mat = surf_mat
    def set_all_ai_oral_calc(self, all_ai_oral_calc):
        self._all_ai_oral_calc = all_ai_oral_calc
        return self
    def set_answer_item(self, answer_item):
        self._answer_item = answer_item
        return self
