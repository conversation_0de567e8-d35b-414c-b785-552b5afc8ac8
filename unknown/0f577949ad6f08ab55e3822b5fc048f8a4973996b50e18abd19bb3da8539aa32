# -*- coding: utf-8 -*-
# @Time    : 2019/8/23 21:52
# <AUTHOR> zhoujun
import copy

import PIL
import numpy as np
import torch
from torchvision import transforms

def get_transforms(transforms_config):
    tr_list = []
    for item in transforms_config:
        if 'args' not in item:
            args = {}
        else:
            args = item['args']
        cls = getattr(transforms, item['type'])(**args)
        tr_list.append(cls)
    tr_list = transforms.Compose(tr_list)
    return tr_list
