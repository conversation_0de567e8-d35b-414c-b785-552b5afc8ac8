import cv2
import torch

from base_common import Constants,LoggerFactory
log = LoggerFactory.get_logger('QuickcalReg')
from quick_calc_rec_service.model.utils import load_config, load_checkpoint
from quick_calc_rec_service.model.models.infer_model import Inference
from quick_calc_rec_service.model.dataset import Words

class CanPredictor:
    def __init__(self, model_path):
        """加载config文件"""
        params = load_config(f"{model_path}/config.yaml")
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        params['device'] = self.device

        word_path = f'{model_path}/chars.txt'
        self.words = Words(word_path)
        params['word_num'] = len(self.words)
        params['use_label_mask'] = False
        params['word_path'] = word_path
        params['hw_code'] = self.words.encode(['$'])[0]
        self.model = Inference(params, draw_map=False)
        self.model = self.model.to(self.device)
        load_checkpoint(self.model, None, f"{model_path}/quickcal_can.pth")
        self.model.eval()

    def predict(self,imgs,labels=None):
        if labels is None:
            return self.predict_ori(imgs)
        else:
            return self.predict_lbl(imgs,labels)

    def predict_lbl(self,imgs,labels):
        encode_labels = []
        try:
            for lbl in labels:
                encode_labels.append(self.words.encode(lbl))
        except:
            return self.predict_ori(imgs)

        new_imgs = []
        for img in imgs:
            ratio = 64/img.shape[0]
            img = cv2.resize(img, (0, 0), fx=ratio, fy=ratio, interpolation=cv2.INTER_NEAREST)
            img = torch.Tensor(255-img) / 255
            img = img.permute(2, 0, 1).unsqueeze(0)
            new_imgs.append(img)
        new_w = 0
        for new_img in new_imgs:
            _,_,_,w = new_img.size()
            if w > new_w:
                new_w = w
        
        inputs = torch.zeros((len(imgs),3,64,new_w))
        
        for i,new_img in enumerate(new_imgs):
            _,_,_,w = new_img.size()
            inputs[i,:,:,:w] = new_img

        inputs = inputs.to(self.device)
            
        results= self.model(inputs,encode_labels)#[True,False,False,True...]
        #log.info(f'labels: {labels}; results:{results}')
        return results
        
    def predict_ori(self,imgs):
        new_imgs = []
        for img in imgs:
            ratio = 64/img.shape[0]
            img = cv2.resize(img, (0, 0), fx=ratio, fy=ratio, interpolation=cv2.INTER_NEAREST)
            img = torch.Tensor(255-img) / 255
            img = img.permute(2, 0, 1).unsqueeze(0)
            new_imgs.append(img)
        new_w = 0
        for new_img in new_imgs:
            _,_,_,w = new_img.size()
            if w > new_w:
                new_w = w
        
        inputs = torch.zeros((len(imgs),3,64,new_w))
        
        for i,new_img in enumerate(new_imgs):
            _,_,_,w = new_img.size()
            inputs[i,:,:,:w] = new_img

        inputs = inputs.to(self.device)
        probs= self.model(inputs)
        
        predictions = []
        for prob in probs:
            prediction = self.words.decode(prob)
            prediction = prediction.replace(' ','')
            predictions.append([prediction])

        return predictions
