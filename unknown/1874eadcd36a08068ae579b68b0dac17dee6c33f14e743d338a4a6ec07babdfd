# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/8/24 17:29 
# @Description  : mission_mode.py

class MissionAlisa:
    ANSWER_ALIGN_SERVICE = 'zoneAlign'
    DETECTION_SERVICE = 'detection'
    DIRECTION_SERVICE = 'direction'
    VERTICAL_DET_SERVICE = 'vertical_det'
    VERTICAL_REC_SERVICE = 'vertical_rec'
    EMBEDDING2_SERVICE = 'embedding2'
    LINE_SERVICE = 'line'
    ANSWER_DETECT_SERVICE = 'answerDet'
    GRAPHICS_SERVICE = 'graphics'
    QUICK_DET_SERVICE = 'quickDet'
    QUICK_REC_SERVICE = 'quickRec'
    CH_HW_SERVICE = 'chocr'
    EN_HW_SERVICE = 'enocr'
    FORMULA_HW_SERVICE = 'forocr'
    PRINT_DET_SERVICE = 'printDet'
    PRINT_REC_SERVICE = 'printRec'
    SURF_SERVICE = 'alignImage'
    JUDGE = 'judge'

    __NAME_MAP = {
        DETECTION_SERVICE : '图像框选模型',
        DIRECTION_SERVICE : '图像旋转模型',
        VERTICAL_DET_SERVICE : '竖式计算检测模型',
        VERTICAL_REC_SERVICE : '竖式计算识别模型',
        EMBEDDING2_SERVICE : '图像特征',
        LINE_SERVICE : '连线题模型',
        ANSWER_DETECT_SERVICE : '答案检测模型',
        GRAPHICS_SERVICE : '作图题模型',
        QUICK_DET_SERVICE : '智能口算检测',
        QUICK_REC_SERVICE : '智能口算识别',
        CH_HW_SERVICE : '中文识别',
        EN_HW_SERVICE : '英文识别',
        FORMULA_HW_SERVICE : '公式识别',
        PRINT_DET_SERVICE : '印刷体检测',
        PRINT_REC_SERVICE : '印刷体识别',
        SURF_SERVICE : '图像对齐模型',
        JUDGE : '批改接口',
        ANSWER_ALIGN_SERVICE : '答案位置匹配模型'
    }
    @staticmethod
    def exists(alisa):
        return alisa in MissionAlisa.__NAME_MAP
    @staticmethod
    def get_service_name(alisa):
        return MissionAlisa.__NAME_MAP.get(alisa, alisa)

class MissionMode:
    LINE_CALC_MISSION = 'line_model_mission'
    GRAPHICS_CALC_MISSION = 'graphics_model_mission'

    VERTICAL_CALC_DET_MISSION = 'vertical_model_det_mission'
    VERTICAL_CALC_REC_MISSION = 'vertical_model_rec_mission'

    QUICK_CALC_DET_MISSION = 'quick_model_det_mission'
    QUICK_CALC_REC_MISSION = 'quick_model_rec_mission'

    PRINT_OCR_DET_MISSION = 'print_ocr_det_model_mission'
    PRINT_OCR_REC_MISSION = 'print_ocr_rec_model_mission'

    CH_HW_OCR_MISSION = 'ch_hw_ocr_model_mission'
    EN_HW_OCR_MISSION = 'en_hw_ocr_model_mission'
    FORMULAR_HW_OCR_MISSION = 'formular_hw_ocr_model_mission'

    EMBEDDING_MISSION2 = 'embedding_model_mission2'
    DETECTION_MISSION = 'detection_model_mission'
    DIRECTION_MISSION = 'direction_model_mission'
    ANSWER_DETECT_MISSION = 'answer_detect_model_mission'
    SURF_MISSION = 'surf_model_mission'
    ZONE_ALIGN_MISSION = 'zone_align_model_mission'

    __BUSY_TIMES_COUNT = {
        LINE_CALC_MISSION : 10,
        VERTICAL_CALC_DET_MISSION : 10,
        VERTICAL_CALC_REC_MISSION : 10,
        GRAPHICS_CALC_MISSION : 10,

        QUICK_CALC_DET_MISSION : 20,
        QUICK_CALC_REC_MISSION : 20,

        CH_HW_OCR_MISSION : 20,
        EN_HW_OCR_MISSION : 10,
        FORMULAR_HW_OCR_MISSION : 10,
        EMBEDDING_MISSION2 : 10,
        DETECTION_MISSION : 20,
        DIRECTION_MISSION : 20,
        PRINT_OCR_DET_MISSION : 20,
        PRINT_OCR_REC_MISSION : 20,
        ANSWER_DETECT_MISSION : 20,
        SURF_MISSION : 10,
        ZONE_ALIGN_MISSION : 10
    }
    __NAMES_MAP = {
        LINE_CALC_MISSION: "连线题模型",
        VERTICAL_CALC_DET_MISSION: "竖式计算题检测模型",
        VERTICAL_CALC_REC_MISSION: "竖式计算题识别模型",
        GRAPHICS_CALC_MISSION: "做图题模型",
        QUICK_CALC_DET_MISSION: "智能口算题检测模型",
        QUICK_CALC_REC_MISSION: "智能口算题识别模型",
        CH_HW_OCR_MISSION: "中文手写识别模型",
        EN_HW_OCR_MISSION: "英文手写识别模型",
        FORMULAR_HW_OCR_MISSION: "公式手写识别模型",
        EMBEDDING_MISSION2: "图像特征模型",
        DETECTION_MISSION: "框图模型",
        DIRECTION_MISSION: "旋转模型",
        PRINT_OCR_DET_MISSION: "印刷体检测模型",
        PRINT_OCR_REC_MISSION: "印刷体识别模型",
        ANSWER_DETECT_MISSION: "答案检测模型",
        SURF_MISSION: "surf",
        ZONE_ALIGN_MISSION: '答案位置匹配模型'
    }

    __MISSION_NAMES = [
        LINE_CALC_MISSION,
        VERTICAL_CALC_DET_MISSION,
        VERTICAL_CALC_REC_MISSION,
        GRAPHICS_CALC_MISSION,
        QUICK_CALC_DET_MISSION,
        QUICK_CALC_REC_MISSION,
        CH_HW_OCR_MISSION,
        EN_HW_OCR_MISSION,
        FORMULAR_HW_OCR_MISSION,
        EMBEDDING_MISSION2,
        DETECTION_MISSION,
        DIRECTION_MISSION,
        PRINT_OCR_DET_MISSION,
        PRINT_OCR_REC_MISSION,
        ANSWER_DETECT_MISSION,
        SURF_MISSION,
        ZONE_ALIGN_MISSION
    ]

    __SERVICE_PATHS = {
        LINE_CALC_MISSION: 'line_calc_service',
        VERTICAL_CALC_DET_MISSION: 'vertical_calc_det_service',
        VERTICAL_CALC_REC_MISSION: 'vertical_calc_rec_service',
        GRAPHICS_CALC_MISSION: 'graphics_calc_service',
        QUICK_CALC_DET_MISSION: 'quick_calc_det_service',
        QUICK_CALC_REC_MISSION: 'quick_calc_rec_service',
        CH_HW_OCR_MISSION: 'ch_hw_ocr_service',
        EN_HW_OCR_MISSION: 'en_hw_ocr_service',
        FORMULAR_HW_OCR_MISSION: 'formula_hw_ocr_service',
        EMBEDDING_MISSION2: 'embedding_service',
        DETECTION_MISSION: 'detection_service',
        DIRECTION_MISSION: 'direction_service',
        PRINT_OCR_DET_MISSION: 'print_ocr_det_service',
        PRINT_OCR_REC_MISSION: 'print_ocr_rec_service',
        ANSWER_DETECT_MISSION: 'answer_det_service',
        SURF_MISSION: 'surf_service',
        ZONE_ALIGN_MISSION: 'zone_align_service'
    }

    __SERVICE_NAMES = {
        LINE_CALC_MISSION: MissionAlisa.LINE_SERVICE,
        VERTICAL_CALC_DET_MISSION: MissionAlisa.VERTICAL_DET_SERVICE,
        VERTICAL_CALC_REC_MISSION: MissionAlisa.VERTICAL_REC_SERVICE,
        GRAPHICS_CALC_MISSION: MissionAlisa.GRAPHICS_SERVICE,
        QUICK_CALC_DET_MISSION: MissionAlisa.QUICK_DET_SERVICE,
        QUICK_CALC_REC_MISSION: MissionAlisa.QUICK_REC_SERVICE,
        CH_HW_OCR_MISSION: MissionAlisa.CH_HW_SERVICE,
        EN_HW_OCR_MISSION: MissionAlisa.EN_HW_SERVICE,
        FORMULAR_HW_OCR_MISSION: MissionAlisa.FORMULA_HW_SERVICE,
        EMBEDDING_MISSION2: MissionAlisa.EMBEDDING2_SERVICE,
        DETECTION_MISSION: MissionAlisa.DETECTION_SERVICE,
        DIRECTION_MISSION: MissionAlisa.DIRECTION_SERVICE,
        PRINT_OCR_DET_MISSION: MissionAlisa.PRINT_DET_SERVICE,
        PRINT_OCR_REC_MISSION: MissionAlisa.PRINT_REC_SERVICE,
        ANSWER_DETECT_MISSION: MissionAlisa.ANSWER_DETECT_SERVICE,
        SURF_MISSION: MissionAlisa.SURF_SERVICE,
        ZONE_ALIGN_MISSION: MissionAlisa.ANSWER_ALIGN_SERVICE
    }

    @staticmethod
    def keys():
        return MissionMode.__MISSION_NAMES
    @staticmethod
    def get_service_path(mission_type):
        return MissionMode.__SERVICE_PATHS.get(mission_type, mission_type)
    @staticmethod
    def get_service_name(mission_type):
        return MissionMode.__SERVICE_NAMES.get(mission_type, mission_type)
    @staticmethod
    def get_mission_name(mission_mode):
        return MissionMode.__NAMES_MAP.get(mission_mode, mission_mode)
    @staticmethod
    def get_busy_times_count(mission_mode):
        return MissionMode.__BUSY_TIMES_COUNT.get(mission_mode, 10)
