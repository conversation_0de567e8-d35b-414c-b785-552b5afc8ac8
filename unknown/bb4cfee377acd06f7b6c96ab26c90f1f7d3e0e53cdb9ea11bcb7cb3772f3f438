# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/4 11:16

import logging
import os

from nb_log import get_logger as np_get_logger
from .constants.constants import Constants
logging.getLogger("tornado.access").disabled = True

class FixedLengthFormatter(logging.Formatter):
    def __init__(self, fmt=None, datefmt=None, style='%', validate=True):
        super().__init__(fmt, datefmt, style, validate)

    def format(self, record):
        # 固定长度配置
        max_logger_name_length = 25
        max_asctime_length = 23   # 例如 '2024-06-21 12:34:56,789' 长度
        max_lineno_length = 4     # 假设最大行号为999

        # 格式化每个字段
        asctime = self.formatTime(record, self.datefmt)
        asctime = asctime.rjust(max_asctime_length)

        levelname = record.levelname
        if record.levelname == 'CRITICAL':
            levelname = 'CRITI'
        elif record.levelname == 'INFO':
            levelname = 'LINFO'
        elif 'WARN' in record.levelname:
            levelname = 'LWARN'

        lineno = str(record.lineno).rjust(max_lineno_length)
        logger_name = record.name.ljust(max_logger_name_length)
        prefix = f"[{asctime}] [{logger_name}:{lineno}][{levelname}]"
        message = f"{prefix} - {record.msg % record.args if record.args else record.msg}"

        return message

class Logger:
    def __init__(self, file_logger):
        self.file_logger = file_logger
    def _log(self, level, msg, *args):
        self.file_logger.log(level, msg, *args, stacklevel=3)
    def log(self, level, msg, *args):
        self._log(level, msg, *args)
    def info(self, msg, *args):
        self._log(logging.INFO, msg, *args)
    def debug(self, msg, *args):
        self._log(logging.DEBUG, msg, *args)
    def warn(self, msg, *args):
        self._log(logging.WARNING, msg, *args)
    def warning(self, msg, *args):
        self._log(logging.WARNING, msg, *args)
    def error(self, msg, *args):
        self._log(logging.ERROR, msg, *args)
    def exception(self, msg, *args):
        self._log(logging.CRITICAL, msg, *args)
    def critical(self, msg, *args):
        self._log(logging.CRITICAL, msg, *args)

class LoggerFactory:

    @classmethod
    def get_logger(cls, logger_name, log_filename=None):
        if '.' in logger_name:
            logger_name = logger_name[logger_name.rfind('.') + 1:]

        name = log_filename if log_filename is not None else os.getenv('CURRENT_LOG_NAME', 'correction_common')
        cls.log_filename = name + '.log'

        nb_logger = np_get_logger(logger_name,
                             log_level_int=logging.INFO,
                             do_not_use_color_handler=True,
                             log_file_handler_type=2,
                             log_path=Constants.LOGGER_PATH,
                             log_filename=cls.log_filename,
                             formatter_template=FixedLengthFormatter(f'[%(asctime)s] [{logger_name}:%(lineno)d][%(levelname)s] - %(message)s'))
        return Logger(nb_logger)
