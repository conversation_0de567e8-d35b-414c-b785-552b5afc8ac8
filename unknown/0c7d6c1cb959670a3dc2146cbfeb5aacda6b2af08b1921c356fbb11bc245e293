import os
import cv2
import random

train_f = open('train.txt','w')
test_f = open('test_.txt','w')

gt_dirs = ['/home/<USER>/data/detect/answer_detect/labels_v0_v5_cls1_checked/',
           '/home/<USER>/data/detect/answer_detect/labels_v7_v8_cls1_checked/']

img_dirs = ['/home/<USER>/data/detect/answer_detect/images_v0_v5_cls1_checked/',
           '/home/<USER>/data/detect/answer_detect/images_v7_v8_cls1_checked/']

for i,gt_dir in enumerate(gt_dirs):
    gts = os.listdirs(gt_dir)
    for gt in gts:
        img_path = img_dirs[i] + gt[3:-3]+'jpg'
        try:
            img = cv2.imread(img_path)
            ff = train_f if random.random()<0.9 else test_f
            
            ff.write(img_path + '\t' + gt_dir+gt + '\n')
        except:
            continue
            
            
train_f.close()
test_f.close()
