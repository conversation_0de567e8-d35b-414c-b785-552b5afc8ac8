# -*- coding: utf-8 -*-
# @Time    : 2019/12/4 14:39
# <AUTHOR> zhoujun
import torch
import torch.nn as nn


class BCEFocalLoss(torch.nn.Module):
    """
    二分类的Focalloss alpha 固定
    """
    def __init__(self, gamma=2, alpha=0.25, reduction='elementwise_mean'):
        super().__init__()
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = reduction
 
    def forward(self, _input, target,mask):
        pt = torch.sigmoid(_input)
        alpha = self.alpha
        loss = - alpha * (1 - pt) ** self.gamma * target * torch.log(pt) - \
               (1 - alpha) * pt ** self.gamma * (1 - target) * torch.log(1 - pt)
        loss = loss*mask
        loss_mean = torch.sum(loss)/torch.sum(mask)
        loss_n = []
        for i in range(len(loss)):
            loss_n.append(torch.sum(loss[i])/torch.sum(mask[i]))
        
        return loss_mean,loss_n

class BalanceCrossEntropyLoss(nn.Module):
    '''
    Balanced cross entropy loss.
    Shape:
        - Input: :math:`(N, 1, H, W)`
        - GT: :math:`(N, 1, H, W)`, same shape as the input
        - Mask: :math:`(N, H, W)`, same spatial shape as the input
        - Output: scalar.

    Examples::

        >>> m = nn.Sigmoid()
        >>> loss = nn.BCELoss()
        >>> input = torch.randn(3, requires_grad=True)
        >>> target = torch.empty(3).random_(2)
        >>> output = loss(m(input), target)
        >>> output.backward()
    '''

    def __init__(self, negative_ratio=3.0, eps=1e-6,focal_flag=False):
        super(BalanceCrossEntropyLoss, self).__init__()
        self.negative_ratio = negative_ratio
        self.eps = eps
        if focal_flag:
            self.focal_loss = BCEFocalLoss()
        self.focal_flag = focal_flag

    def forward(self,
                pred: torch.Tensor,
                gt: torch.Tensor,
                mask: torch.Tensor,
                weight=None,
                return_origin=False):
        '''
        Args:
            pred: shape :math:`(N, 1, H, W)`, the prediction of network
            gt: shape :math:`(N, 1, H, W)`, the target
            mask: shape :math:`(N, H, W)`, the mask indicates positive regions
        '''
        positive = (gt * mask).byte()
        negative = ((1 - gt) * mask).byte()
        positive_count = int(positive.float().sum())
        negative_count = max(min(int(negative.float().sum()), int(positive_count * self.negative_ratio)),int(negative.float().sum())//50)
        if self.focal_flag:
            loss,loss_n = self.focal_loss(pred, gt , mask)
            return loss,loss_n
        else:
            loss = nn.functional.binary_cross_entropy(pred, gt, reduction='none')
            
        if not weight is None:
            loss *=(weight+1)
        positive_loss = loss * positive.float()
        negative_loss = loss * negative.float()
        # negative_loss, _ = torch.topk(negative_loss.view(-1).contiguous(), negative_count)
        negative_loss, _ = negative_loss.view(-1).topk(negative_count)

        balance_loss = (positive_loss.sum() + negative_loss.sum()) / (positive_count + negative_count + self.eps)
        
        loss_n = []
        for n in range(len(gt)):
            positive_loss_n = loss[n] * positive[n].float()
            negative_loss_n = loss[n] * negative[n].float()

            positive_count_n = int(positive[n].float().sum())
            negative_count_n = min(int(negative[n].float().sum()), int(positive_count_n * self.negative_ratio))
        
            negative_loss_n, _ = negative_loss_n.view(-1).topk(negative_count_n)

            balance_loss_n = (positive_loss_n.sum() + negative_loss_n.sum()) / (positive_count_n + negative_count_n + self.eps)
            loss_n.append(balance_loss_n)
        
        
        if return_origin:
            return balance_loss, loss
        return balance_loss,loss_n


class DiceLoss(nn.Module):
    '''
    Loss function from https://arxiv.org/abs/1707.03237,
    where iou computation is introduced heatmap manner to measure the
    diversity bwtween tow heatmaps.
    '''

    def __init__(self, eps=1e-6):
        super(DiceLoss, self).__init__()
        self.eps = eps

    def forward(self, pred: torch.Tensor, gt, mask, weight=None):
        '''
        pred: one or two heatmaps of shape (N, 1, H, W),
            the losses of tow heatmaps are added together.
        gt: (N, 1, H, W)
        mask: (N, H, W)
        '''
        return self._compute(pred, gt, mask, weight)

    def _compute(self, pred, gt, mask, weight):
        if pred.dim() == 4:
            pred = pred[:, 0, :, :]
            gt = gt[:, 0, :, :]
        assert pred.shape == gt.shape
        assert pred.shape == mask.shape
        if weight is not None:
            assert weight.shape == mask.shape
            mask = (weight+1) * mask
        intersection = (pred * gt * mask).sum()

        union = (pred * mask).sum() + (gt * mask).sum() + self.eps
        loss = 1 - 2.0 * intersection / union
        assert loss <= 1
        return loss


class MaskL1Loss(nn.Module):
    def __init__(self, eps=1e-6):
        super(MaskL1Loss, self).__init__()
        self.eps = eps

    def forward(self, pred: torch.Tensor, gt, mask,weight=None):
        if weight is not None:
            loss = (torch.abs(pred - gt)*(1+weight) * mask).sum() / (mask.sum() + self.eps)
        else:
            loss = (torch.abs(pred - gt) * mask).sum() / (mask.sum() + self.eps)
        return loss
