import gc
import os
import shutil

import numpy as np
import cv2
from PIL import Image
from tqdm import tqdm
import time

from base_common import Constants, LoggerFactory
from line_calc_service.model.network import deeplabv3plus_resnet50

import torch
import torchvision.transforms.functional as F
import torch.nn.functional as FF
import line_calc_service.model.util as util
log = LoggerFactory.get_logger('line_interface')
OUTPUT_STRIDE = 8
NUM_CLASSES = 1

SIZE = (640,640)
DIST_THR= 1.1 #1. #两个特征的欧式举例小于多少时认定为连线成立

POINT_THR = 0.3 #0.2
R = 1
RANGE = 3 #5

MEAN=[0.485, 0.456, 0.406]
STD=[0.229, 0.224, 0.225]

class Inference():
    def __init__(self, model_path):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = deeplabv3plus_resnet50(num_classes=NUM_CLASSES, output_stride=OUTPUT_STRIDE)
        checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
        self.model.load_state_dict(checkpoint["model_state"],strict=True)
        self.model.to(self.device)
#         self.model.cpu()
        self.model.eval()
    
        #self.session = onnxruntime.InferenceSession('./line_interface/test.onnx')#, providers=onnxruntime.get_available_providers())
#        self.session.set_providers(['CUDAExecutionProvider'], [ {'device_id': 0}])
        del checkpoint
        gc.collect()
    def to_numpy(self,tensor):
        return tensor.detach().cpu().numpy()
    def infer_image(self,image,flip_h=False,flip_w=False):
        t0 = time.time()
        if isinstance(image,str):
            image = Image.open(image).convert('RGB')
            
        w,h = image.size
        image = F.resize(image, SIZE, Image.BILINEAR)
        
        scales = (w/SIZE[0],h/SIZE[1])
        
        image = F.to_tensor(image)
        image = F.normalize(image, MEAN, STD)
        image = image.to(self.device, dtype=torch.float32).unsqueeze(0)
#         image = image.cpu().unsqueeze(0)
        if flip_h:
            image = torch.flip(image, dims=[2])
        elif flip_w:
            image = torch.flip(image, dims=[3])
        with torch.no_grad():
            outputs,embeddings = self.model(image)
#            inputs = {self.session.get_inputs()[0].name:self.to_numpy(image)}
#            outputs,embeddings = self.session.run(None,inputs)
#            outputs = torch.tensor(outputs)
#            embeddings = torch.tensor(embeddings)
            if flip_h:
                outputs = torch.flip(outputs, dims=[2])
                embeddings = torch.flip(embeddings, dims=[2])
            elif flip_w:
                outputs = torch.flip(outputs, dims=[3])
                embeddings = torch.flip(embeddings, dims=[3])
            outputs = torch.sigmoid(outputs)
            _,_,h_e,w_e =embeddings.size()
            embeddings = FF.normalize(embeddings)
            embeddings = embeddings.permute(0, 2, 3, 1).contiguous()
        
        #res = (outputs[0][0].detach().cpu().numpy()*255).astype(np.uint8)
        #cv2.imwrite('0_mask.jpg',res)
        points,features = util.parse_result(outputs,embeddings,scales)
#         t2 = time.time()
#         print(t2 - t1)
#         points = []
#         features = []
#         while(True):
#             point,outputs = util.get_point(outputs)
#             if point==None:
#                 break
#             points.append((int(point[0]*scales[0]*4),int(point[1]*scales[1]*4)))
# #             res = cv2.circle(res,(point[0],point[1]),6,(255,0,0),-1)
            
# #             x = min(w_e-1,round((point[0]-4//2)/4))
# #             y = min(h_e-1,round((point[1]-4//2)/4))
            
#             x = point[0]
#             y = point[1]
# #             features.append(embeddings[0,y,x,:].detach().cpu().numpy())
            
#             #############################
#             #可以尝试不同的周边特征融合方式
#             #############################
#             N,H,W,C = embeddings.size()
#             embs = []
#             weights = []
#             for k in [-1,0,1]:
#                 for h in [-1,0,1]:
#                     pp = [x+k,y+h]
#                     flag = False
#                     if pp[1]<0 or pp[1]>=H:
#                         flag = True
#                     if pp[0]<0 or pp[0]>=W:
#                         flag = True
#                     if flag:
#                         continue
                        
#                     emb = embeddings[0,pp[1],pp[0],:]
# #                     embs.append(emb.detach().cpu().numpy() )
#                     embs.append(emb)
#             feat = torch.stack(embs).mean(dim=0).detach().cpu().numpy() 
# #             feat = torch.tensor(embs).view(-1).detach().cpu().numpy() 
#             features.append(feat)
#        print('points: ',points)
#        cv2.imwrite('0_mask.jpg',res)
        return points,features
        
if __name__ == '__main__':
    net = Inference()
    image_paths = [os.path.join('./test_imgs_all',i) for i in os.listdir('./test_imgs_all')]
    box_json = load_box('./test_imgs_all/boxs_all.txt')
    answers = load_answer('./test_imgs_all/test_imgs_all.txt')
    
    save_dir = './result/'
    if os.path.exists(save_dir):
        shutil.rmtree(save_dir)
    os.mkdir(save_dir)
    
    right_line = 0
    wrong_line = 0
    right_item = 0
    wrong_item = 0
    right_point = 0
    wrong_point = 0
    for image_path in tqdm(image_paths):
        if not image_path.endswith('.jpg'):
            continue 
        points,features = net.infer_image(image_path)
        pred_answers,gt_point_num,pred_point_num,img = get_user_answers(points,features,box_json,image_path)
        
        if gt_point_num == pred_point_num:
            right_point += 1
        else:
            wrong_point += 1
        
        answer = answers[image_path.split('/')[-1]]
        
        flag =  True
        for an in answer:
            if an in pred_answers:
                right_line += 1
            else:
                flag = False
                wrong_line += 1
        for an in pred_answers:
            if not an in answer:
                flag = False  
        if flag:
            right_item += 1
#             cv2.imwrite(save_dir + image_path.split('/')[-1],img)
        else:
            wrong_item += 1
            cv2.imwrite(save_dir + image_path.split('/')[-1],img)
        