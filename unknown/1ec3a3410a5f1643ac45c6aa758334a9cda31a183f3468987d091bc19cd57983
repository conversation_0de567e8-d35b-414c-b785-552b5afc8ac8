# -*- coding: utf-8 -*-
# @Time    : 2018/6/11 15:54
# <AUTHOR> zhoujun
import os
import cv2
import sys
import pathlib
__dir__ = pathlib.Path(os.path.abspath(__file__))
sys.path.append(str(__dir__))
sys.path.append(str(__dir__.parent.parent))
# project = 'DBNet.pytorch'  # 工作项目根目录
# sys.path.append(os.getcwd().split(project)[0] + project)

import argparse
import time
import shutil
import torch
from tqdm.auto import tqdm
import numpy as np
from models import build_model
from data_loader import get_dataloader
from post_processing import get_post_processing
from utils import get_metric,Denormalize,draw_result

class EVAL():
    def __init__(self, model_path, gpu_id=0):

        self.vis_dir = './vis_eval/'
        if os.path.exists(self.vis_dir):
            shutil.rmtree(self.vis_dir)
        os.mkdir(self.vis_dir)
        
        self.denorm = Denormalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        
        self.gpu_id = gpu_id
        if self.gpu_id is not None and isinstance(self.gpu_id, int) and torch.cuda.is_available():
            self.device = torch.device("cuda:%s" % self.gpu_id)
            torch.backends.cudnn.benchmark = True
        else:
            self.device = torch.device("cpu")
        checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
        config = checkpoint['config']
        config['arch']['backbone']['pretrained'] = False

        self.validate_loader = get_dataloader(config['dataset']['validate'], config['distributed'])

        self.model = build_model(config['arch'])
        self.model.load_state_dict(checkpoint['state_dict'])
        self.model.to(self.device)

        config['post_processing']['args']['thresh'] = 0.1
        config['post_processing']['args']['box_thresh'] = 0.5
        self.post_process = get_post_processing(config['post_processing'])
        
#         config['metric']['args']['is_output_polygon'] = False
        self.metric_cls = get_metric(config['metric'])

    def eval(self):
        self.model.eval()
        # torch.cuda.empty_cache()  # speed up evaluating after training finished
        raw_metrics = []
        total_frame = 0.0
        total_time = 0.0
        for i, batch in tqdm(enumerate(self.validate_loader), total=len(self.validate_loader), desc='test model'):
            with torch.no_grad():
                # 数据进行转换和丢到gpu
                for key, value in batch.items():
                    if value is not None:
                        if isinstance(value, torch.Tensor):
                            batch[key] = value.to(self.device)
                start = time.time()
                preds = self.model(batch['img'])
#                 image_input = (self.denorm(batch['img'][0]) * 255).transpose(1, 2, 0).astype(np.uint8).copy()
                image_input = cv2.imread(batch['img_path'][0])
                boxes, scores = self.post_process(batch, preds,is_output_polygon=self.metric_cls.is_output_polygon)
                draw_result(self.vis_dir,batch['img_name'][0],image_input,boxes[0],scores[0])#可视化结果
                total_frame += batch['img'].size()[0]
                total_time += time.time() - start
                raw_metric = self.metric_cls.validate_measure(batch, (boxes, scores))
                raw_metrics.append(raw_metric)
        metrics = self.metric_cls.gather_measure(raw_metrics)
        print('FPS:{}'.format(total_frame / total_time))
        return metrics['recall'].avg, metrics['precision'].avg, metrics['fmeasure'].avg


def init_args():
    parser = argparse.ArgumentParser(description='DBNet.pytorch')
    parser.add_argument('--model_path', required=False,default='output_v1/DBNet_resnet18_FPN_DBHead/checkpoint/model_latest.pth', type=str)
    args = parser.parse_args()
    return args


if __name__ == '__main__':

    args = init_args()
    eval = EVAL(args.model_path)
    result = eval.eval()
    print(result)
