name: DBNet
base: ['config/open_dataset.yaml']
arch:
  type: Model
  backbone:
    type: resnet18
    pretrained: true
  neck:
    type: FPN
    inner_channels: 256
  head:
    type: DBHead
    out_channels: 2
    k: 50
post_processing:
  type: SegDetectorRepresenter
  args:
    thresh: 0.3
    box_thresh: 0.7
    max_candidates: 1000
    unclip_ratio: 1.5 # from paper
metric:
  type: QuadMetric
  args:
    is_output_polygon: false
loss:
  type: DBLoss
  alpha: 1
  beta: 10
  ohem_ratio: 3
optimizer:
  type: Adam
  args:
    lr: 0.001
    weight_decay: 0
    amsgrad: true
lr_scheduler:
  type: WarmupPolyLR
  args:
    warmup_epoch: 3
trainer:
  seed: 2
  epochs: 1200
  log_iter: 1
  show_images_iter: 1
  resume_checkpoint: ''
  finetune_checkpoint: ''
  output_dir: output
  tensorboard: true
dataset:
  train:
    dataset:
      args:
        data_path:
          - ./datasets/train.json
        transforms: # 对图片进行的变换方式
          - type: ToTensor
            args: {}
          - type: Normalize
            args:
              mean: [0.485, 0.456, 0.406]
              std: [0.229, 0.224, 0.225]
        img_mode: RGB
        load_char_annotation: false
        expand_one_char: false
    loader:
      batch_size: 2
      shuffle: true
      pin_memory: true
      num_workers: 6
      collate_fn: ''
  validate:
    dataset:
      args:
        data_path:
          - ./datasets/test.json
        pre_processes:
          - type: ResizeShortSize
            args:
              short_size: 736
              resize_text_polys: false
        img_mode: RGB
        load_char_annotation: false
        expand_one_char: false
    loader:
      batch_size: 1
      shuffle: true
      pin_memory: false
      num_workers: 6
      collate_fn: ICDARCollectFN
