from base_common import Constants

config_swin_640 = {
    "annotation_path"   : '/home/<USER>/data/detect/paint_detect/train_yolov4_231111.txt',#训练标注文件
    "anchors_path"      : f'{Constants.WORK_BASE_PATH}/graphics_calc_service/model/model_data/anchor.txt',
    "classes_path"      : f'{Constants.WORK_BASE_PATH}/graphics_calc_service/model/model_data/answer_classes.txt',
    "backbone"          : 'swintransformer',#swintransformer，mobilenetv1,mobilenetv2,mobilenetv3
    "cuda"              : True,
    "letterbox_image"   : True,#False

    "model_train_path"  : '',
    "input_shape"       : (640,640),#(640,640), #h,w
    "save_dir"          : '', #训练权重保存文件夹
    "save_step"         : 10,
    "Batch_size_1"      : 6,#6,12,24
    "Init_Epoch"        : 0,
    "Freeze_Epoch"      : 75,
    "lr_freeze"         : 1e-4,
    "Cosine_lr"         : True,
    "mosaic"            : False,
    "smoooth_label"     : 0,#normal give 0,0.001,0.005
    "Unfreeze_Epoch"    : 150,
    "lr_unfreeze"       : 1e-5,
    "Batch_size_2"      : 3,#3,4,8
    "start_save_epoch"  : 70,
    
    "model_test_path"   : f'{Constants.MODEL_WEIGHT_PATH}/answer_det/paint_item_yolo4/paint.pth',
    "model_image_size"  : (640, 640, 3),#h,w,c
    "confidence"        : 0.1,
    "iou"               : 0.5,
    "test_txt"          : '/home/<USER>/data/detect/paint_detect/test_yolov4_231111.txt'
}
