import network
import utils
import os
import shutil
import numpy as np
import cv2
from PIL import Image
import re
import time

from utils import ext_transforms as et
from network import deeplabv3plus_mobilenet,deeplabv3plus_resnet50

import torch
import torch.nn as nn
import torchvision.transforms as trans
import torchvision.transforms.functional as F
import torch.nn.functional as FF

OUTPUT_STRIDE = 8
NUM_CLASSES = 1
# CKPT = './weights/deeplabv3plus_resnet50_448_s8_v4/./latest_deeplabv3plus_resnet50_os8_30000.pth'
# CKPT = './checkpoints_templess_640_v1/latest_deeplabv3plus_mobilenet_os8_60000.pth'
# CKPT = './checkpoints_templess_448_v3/latest_deeplabv3plus_mobilenet_os8_30000.pth'
CKPT = './weights/deeplabv3plus_resnet50_640_s8_v9/latest_deeplabv3plus_resnet50_os8_90000.pth'
SIZE = (640,640)
THR=0.05#从热度图上提取端点的半径内均值的阈值
R = 1#从热度图上提取端点的半径
RANGE = 5#热度图上提取一个端点后，将附近RANGE*R的值赋值为0

MEAN=[0.485, 0.456, 0.406]
STD=[0.229, 0.224, 0.225]

def get_point(heatmap):
#     print('finding point')
    #heatmap: torch.tensor (1,1,H,W)
    _,_,H,W = heatmap.size()
    thr = THR
    r = R
    conv = torch.nn.AvgPool2d(2*r+1, stride=1, padding=r)
    out = conv(heatmap).squeeze()
    
    out_ = out.view(-1)
    value,index = torch.max(out_,0)
    if value < thr:
        return None,None
    index_x = index%W
    index_y = index//H
    point = [index_x.item(),index_y.item()]
    out[index_y-RANGE*r:index_y+RANGE*r,index_x-RANGE*r:index_x+RANGE*r]=0
    
    return point,out.unsqueeze(0).unsqueeze(1)
    
class Inference():
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = deeplabv3plus_resnet50(num_classes=NUM_CLASSES, output_stride=OUTPUT_STRIDE)
        checkpoint = torch.load(CKPT, map_location=torch.device('cpu'))
        self.model.load_state_dict(checkpoint["model_state"],strict=True)
        self.model.to(self.device)
        self.model.eval()
        
    def infer_image(self,image_path):
        if isinstance(image_path,str):
            image = Image.open(image_path).convert('RGB')
            
        w,h = image.size
        image_ori = cv2.imread(image_path)
        image = F.resize(image, SIZE, Image.BILINEAR)
        scales = (w/SIZE[0],h/SIZE[1])
        
        image = F.to_tensor(image)
        image = F.normalize(image, MEAN, STD)
        image = image.to(self.device, dtype=torch.float32).unsqueeze(0)
        with torch.no_grad():
            outputs,embeddings = self.model(image)
#             print(outputs.size(),embeddings.size())
            outputs = torch.sigmoid(outputs)
            _,_,h_e,w_e =embeddings.size()
            embeddings = FF.normalize(embeddings)
            embeddings = embeddings.permute(0, 2, 3, 1).contiguous()
        
        res = (outputs[0][0].detach().cpu().numpy()*255).astype(np.uint8)
        
        points = []
        features = []
        while(True):
            point,outputs = get_point(outputs)
            if point==None:
                break
            points.append((int(point[0]*scales[0]),int(point[1]*scales[1])))
#             res = cv2.circle(res,(point[0],point[1]),2,(255,0,0),-1)
#             print(image_ori.shape,(point[0]*scales[0]*4,point[1]*scales[1]*4))
            image_ori = cv2.circle(image_ori,(int(point[0]*scales[0]*4),int(point[1]*scales[1]*4)),1,(255,0,0),-1)
            
#             x = min(w_e-1,round((point[0]-4//2)/4))
#             y = min(h_e-1,round((point[1]-4//2)/4))
            
            x = point[0]
            y = point[1]
#             print(embeddings.size(),point)
            features.append(embeddings[0,y,x,:].detach().cpu().numpy())
            
        
#         cv2.imwrite('./result/'+image_path.split('/')[-1].split('.')[0] + '_mask.jpg',res)
        cv2.imwrite('./result/'+image_path.split('/')[-1],image_ori)
        
        return points,features
        
if __name__ == '__main__':
    start = time.time()
    net = Inference()
    
    save_dir = './result/'
    if os.path.exists(save_dir):
        shutil.rmtree(save_dir)
    os.mkdir(save_dir)
    
    with open('/home/<USER>/data/detect/answer_detect/line/train_all.txt','r') as f:
        lines = f.readlines()
        
    right_acc = 0
    total_acc = 0
    
    right_recall = 0
    total_recall = 0
    for n,line in enumerate(lines):
        img_path = line.strip().split(' ')[0]
        pred_points,features = net.infer_image(img_path)
        
#         gt_points = []
#         for content in line.strip().split(' ')[1:]:
#             boxs = content.split(',')
#             gt_points.append([int(boxs[0]),int(boxs[1])])
#             gt_points.append([int(boxs[2]),int(boxs[3])])
           
#         acc_dist = []
        
#         for gt in gt_points:
#             dists = []
#             for pred in pred_points:
#                 dist = ((pred[0]-(gt[0]-2)/4)**2 + (pred[1]-(gt[1]-2)/4)**2)**0.5
#                 dists.append(dist)
#             min_dist = np.array(dists).min()
# #             print(min_dist)
#             if min_dist < 9:
#                 right_recall += 1
                
#             total_recall += 1
            
#         for pred in pred_points:
#             dists = []
#             for gt in gt_points:
#                 dist = ((pred[0]-(gt[0]-2)/4)**2 + (pred[1]-(gt[1]-2)/4)**2)**0.5
#                 dists.append(dist)
#             min_dist = np.array(dists).min()
# #             print(min_dist)
#             if min_dist < 9:
#                 right_acc += 1
                
#             total_acc += 1
# #         if n >= 10:
# #             break   
#     print('acc : {}'.format(right_acc/total_acc))
#     print('recall : {}'.format(right_recall/total_recall))
    
#     end = time.time()
#     print(' {} s per item'.format((end-start)/len(lines)))