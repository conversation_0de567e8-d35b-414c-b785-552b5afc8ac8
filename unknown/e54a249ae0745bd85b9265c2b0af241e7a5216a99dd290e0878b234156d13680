# -*- coding: utf-8 -*-
# @Time    : 2019/8/23 21:57
# <AUTHOR> <PERSON><PERSON>jun
import torch
from addict import Dict
from torch import nn
import torch.nn.functional as F

from quick_calc_det_service.model.models.backbone import build_backbone
from quick_calc_det_service.model.models.neck import build_neck
from quick_calc_det_service.model.models.head import build_head


class Model(nn.Module):
    def __init__(self, model_config: dict):
        """
        PANnet
        :param model_config: 模型配置
        """
        super().__init__()
        model_config = Dict(model_config)
        backbone_type = model_config.backbone.pop('type')
        neck_type = model_config.neck.pop('type')
        head_type = model_config.head.pop('type')
        self.backbone = build_backbone(backbone_type, **model_config.backbone)
        self.cls_num = len(model_config.cls_list)
        if self.cls_num == 3:
            self.neck0 = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
            self.head0 = build_head(head_type, in_channels=self.neck0.out_channels, **model_config.head)

            self.neck1 = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
            self.head1 = build_head(head_type, in_channels=self.neck0.out_channels, **model_config.head)

            self.neck2 = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
            self.head2 = build_head(head_type, in_channels=self.neck0.out_channels, **model_config.head)
        elif self.cls_num == 2:
            self.neck0 = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
            self.head0 = build_head(head_type, in_channels=self.neck0.out_channels, **model_config.head)

            self.neck1 = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
            self.head1 = build_head(head_type, in_channels=self.neck0.out_channels, **model_config.head)

#             self.neck2 = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
#             self.head2 = build_head(head_type, in_channels=self.neck0.out_channels, **model_config.head)

#             self.neck3 = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
#             self.head3 = build_head(head_type, in_channels=self.neck0.out_channels, **model_config.head)
        elif self.cls_num == 1:
            self.neck0 = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
            self.head0 = build_head(head_type, in_channels=self.neck0.out_channels, **model_config.head)
        else:
            raise
            
        self.name = f'{backbone_type}_{neck_type}_{head_type}'

    def forward(self, x):
        _, _, H, W = x.size()
        backbone_out = self.backbone(x)

        if self.cls_num == 3:
            neck_out0 = self.neck0(backbone_out)
            y0 = self.head0(neck_out0)
            y0 = F.interpolate(y0, size=(H, W), mode='bilinear', align_corners=True)

            neck_out1 = self.neck1(backbone_out)
            y1 = self.head1(neck_out1)
            y1 = F.interpolate(y1, size=(H, W), mode='bilinear', align_corners=True)

            neck_out2 = self.neck2(backbone_out)
            y2 = self.head2(neck_out2)
            y2 = F.interpolate(y2, size=(H, W), mode='bilinear', align_corners=True)

#             neck_out3 = self.neck3(backbone_out)
#             y3 = self.head3(neck_out3)
#             y3 = F.interpolate(y3, size=(H, W), mode='bilinear', align_corners=True)
#             return [y0,y1,y2]
            return torch.stack([y0,y1,y2],0).permute([1, 2, 0, 3,4]).squeeze(0)
            
        elif self.cls_num == 2:
            neck_out0 = self.neck0(backbone_out)
            y0 = self.head0(neck_out0)
            y0 = F.interpolate(y0, size=(H, W), mode='bilinear', align_corners=True)

            neck_out1 = self.neck1(backbone_out)
            y1 = self.head1(neck_out1)
            y1 = F.interpolate(y1, size=(H, W), mode='bilinear', align_corners=True)
#             return [y0,y1]
            return torch.stack([y0,y1],0).permute([1, 2, 0, 3,4]).squeeze(0)
            
        elif self.cls_num == 1:
            neck_out0 = self.neck0(backbone_out)
            y0 = self.head0(neck_out0)
            y0 = F.interpolate(y0, size=(H, W), mode='bilinear', align_corners=True)
#             return [y0]
            return torch.stack([y0],0).permute([1, 2, 0, 3,4])#.squeeze(0)
        else:
            raise
class Model_Imgtable_For(nn.Module):
    def __init__(self, model_config: dict):
        """
        PANnet
        :param model_config: 模型配置
        """
        super().__init__()
        model_config = Dict(model_config)
        backbone_type = model_config.backbone.pop('type')
        neck_type = model_config.neck.pop('type')
        head_type = model_config.head.pop('type')
        self.backbone = build_backbone(backbone_type, **model_config.backbone)
        self.cls_num = len(model_config.cls_list)
        self.neck = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
        
        self.head0 = build_head(head_type, in_channels=self.neck.out_channels, **model_config.head)
        self.head1 = build_head(head_type, in_channels=self.neck.out_channels, **model_config.head)
        
        self.neck2 = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
        self.head2 = build_head(head_type, in_channels=self.neck.out_channels, **model_config.head)
        
            
        self.name = f'{backbone_type}_{neck_type}_{head_type}'

    def forward(self, x):
        _, _, H, W = x.size()
        backbone_out = self.backbone(x)
        
        neck_out0 = self.neck(backbone_out)
        y0 = self.head0(neck_out0)
        y0 = F.interpolate(y0, size=(H, W), mode='bilinear', align_corners=True)
        y1 = self.head1(neck_out0)
        y1 = F.interpolate(y1, size=(H, W), mode='bilinear', align_corners=True)
        
        neck_out2 = self.neck2(backbone_out)
        y2 = self.head2(neck_out2)
        y2 = F.interpolate(y2, size=(H, W), mode='bilinear', align_corners=True)

        return [y0,y1,y2] # img,table,for
    
class Model_Light(nn.Module):
    def __init__(self, model_config: dict):
        """
        PANnet
        :param model_config: 模型配置
        """
        
        super().__init__()
        model_config = Dict(model_config)
        backbone_type = model_config.backbone.pop('type')
        neck_type = model_config.neck.pop('type')
        head_type = model_config.head.pop('type')
        self.backbone = build_backbone(backbone_type, **model_config.backbone)
        self.cls_num = len(model_config.cls_list)
        self.neck = build_neck(neck_type, in_channels=self.backbone.out_channels, **model_config.neck)
        
        if self.cls_num == 2:
            self.head0 = build_head(head_type, in_channels=self.neck.out_channels, **model_config.head)
            self.head1 = build_head(head_type, in_channels=self.neck.out_channels, **model_config.head)
#             self.head2 = build_head(head_type, in_channels=self.neck.out_channels, **model_config.head)
            
        elif self.cls_num == 1:
            self.head0 = build_head(head_type, in_channels=self.neck.out_channels, **model_config.head)
        else:
            raise
            
        self.name = f'{backbone_type}_{neck_type}_{head_type}'

    def forward(self, x):
        _, _, H, W = x.size()
        backbone_out = self.backbone(x)
        neck_out = self.neck(backbone_out)

        if self.cls_num == 2:
            y0 = self.head0(neck_out)
            y0 = F.interpolate(y0, size=(H, W), mode='bilinear', align_corners=True)

            y1 = self.head1(neck_out)
            y1 = F.interpolate(y1, size=(H, W), mode='bilinear', align_corners=True)

#             y2 = self.head2(neck_out)
#             y2 = F.interpolate(y2, size=(H, W), mode='bilinear', align_corners=True)

#             return [y0,y1]
            return torch.stack([y0,y1],0).permute([1, 2, 0, 3,4]).squeeze(0)
        elif self.cls_num == 1:
            y0 = self.head0(neck_out)
            y0 = F.interpolate(y0, size=(H, W), mode='bilinear', align_corners=True)
            return [y0]
#             return torch.stack([y0],0).permute([1, 2, 0, 3,4]).squeeze(0)
        else:
            raise
            
            
class Hrnet(nn.Module):
    def __init__(self, model_config: dict):
        """
        PANnet
        :param model_config: 模型配置
        """
        super().__init__()
        model_config = Dict(model_config)
        backbone_type = model_config.backbone.pop('type')
        head_type = model_config.head.pop('type')
        self.backbone = build_backbone(backbone_type, **model_config.backbone)
        self.cls_num = len(model_config.cls_list)
        if self.cls_num == 4:
            self.head0 = build_head(head_type, **model_config.head)

            self.head1 = build_head(head_type, **model_config.head)

            self.head2 = build_head(head_type, **model_config.head)

            self.head3 = build_head(head_type, **model_config.head)
        elif self.cls_num == 1:
            self.head0 = build_head(head_type, **model_config.head)
        else:
            raise
            
        self.name = f'{backbone_type}_{head_type}'

    def forward(self, x):
        _, _, H, W = x.size()
        backbone_out = self.backbone(x)
        
        if self.cls_num == 4:
            y0 = self.head0(backbone_out)
            y0 = F.interpolate(y0, size=(H, W), mode='bilinear', align_corners=True)

            y1 = self.head1(backbone_out)
            y1 = F.interpolate(y1, size=(H, W), mode='bilinear', align_corners=True)

            y2 = self.head2(backbone_out)
            y2 = F.interpolate(y2, size=(H, W), mode='bilinear', align_corners=True)

            y3 = self.head3(backbone_out)
            y3 = F.interpolate(y3, size=(H, W), mode='bilinear', align_corners=True)

            return [y0,y1,y2,y3]
        elif self.cls_num == 1:
            y0 = self.head0(backbone_out)
            y0 = F.interpolate(y0, size=(H, W), mode='bilinear', align_corners=True)
            return [y0]
        else:
            raise
            

if __name__ == '__main__':
    import torch

    device = torch.device('cpu')
    x = torch.zeros(2, 3, 640, 640).to(device)

    model_config = {
        'backbone': {'type': 'resnest50', 'pretrained': True, "in_channels": 3},
        'neck': {'type': 'FPN', 'inner_channels': 256},  # 分割头，FPN or FPEM_FFM
        'head': {'type': 'DBHead', 'out_channels': 2, 'k': 50},
    }
    model = Model(model_config=model_config).to(device)
    import time

    tic = time.time()
    y = model(x)
    print(time.time() - tic)
    print(y.shape)
    print(model.name)
    print(model)
    # torch.save(model.state_dict(), 'PAN.pth')
