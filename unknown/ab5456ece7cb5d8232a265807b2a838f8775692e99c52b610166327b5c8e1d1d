#!/bin/bash
chmod +x /usr/servering/correction_runtime/base_common/scripts/get_sys_conf.sh

BASE_PATH=/usr/servering/correction_runtime
CORRECTION_PATH=$BASE_PATH/correction_service
QUICK_CORRECTION_PATH=$BASE_PATH/quick_correction_service
ANSWER_DETECT_PATH=$BASE_PATH/answer_det_service
DETECTION_PATH=$BASE_PATH/detection_service
DIRECTION_PATH=$BASE_PATH/direction_service
FORMULA_PATH=$BASE_PATH/formula_hw_ocr_service
EMBEDDING_PATH=$BASE_PATH/embedding_service
CH_PATH=$BASE_PATH/ch_hw_ocr_service
EN_PATH=$BASE_PATH/en_hw_ocr_service
QUICK_DET_PATH=$BASE_PATH/quick_calc_det_service
QUICK_REC_PATH=$BASE_PATH/quick_calc_rec_service
VERTICAL_DET_PATH=$BASE_PATH/vertical_calc_det_service
VERTICAL_REC_PATH=$BASE_PATH/vertical_calc_rec_service
GRAPHICS_PATH=$BASE_PATH/graphics_calc_service
LINE_PATH=$BASE_PATH/line_calc_service
PRINT_DET_PATH=$BASE_PATH/print_ocr_det_service
PRINT_REC_PATH=$BASE_PATH/print_ocr_rec_service
SURF_PATH=$BASE_PATH/surf_service
ZONE_ALIGN_PATH=$BASE_PATH/zone_align_service

stop_server() {
  local service_dir="$1"
  cd "$service_dir"
  echo "Current dir: $(pwd)"
  bash scripts/mission.sh stop
}

cd "$CORRECTION_PATH"
bash scripts/task.sh stop
bash scripts/server.sh stop

cd "$QUICK_CORRECTION_PATH"
bash scripts/task.sh stop
bash scripts/server.sh stop

stop_server "$DETECTION_PATH"
stop_server "$DIRECTION_PATH"
stop_server "$ANSWER_DETECT_PATH"
stop_server "$CH_PATH"
stop_server "$EN_PATH"
stop_server "$EMBEDDING_PATH"
stop_server "$FORMULA_PATH"
stop_server "$PRINT_DET_PATH"
stop_server "$PRINT_REC_PATH"
stop_server "$QUICK_DET_PATH"
stop_server "$QUICK_REC_PATH"
stop_server "$VERTICAL_DET_PATH"
stop_server "$VERTICAL_REC_PATH"
stop_server "$LINE_PATH"
stop_server "$GRAPHICS_PATH"
stop_server "$SURF_PATH"
stop_server "$ZONE_ALIGN_PATH"

ps -ef|grep python
exit 0