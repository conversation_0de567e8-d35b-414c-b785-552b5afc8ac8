from vertical_calc_det_service.model.shushi_dbnet import dbnet_interface

from base_common import LoggerFactory

log = LoggerFactory.get_logger('VerticalCalculationModel')

class Shushi_Auto:
    def __init__(self, model_path):
        self.dbnet_detector = dbnet_interface.Dbnet_Interface(model_path)

    def run(self, img):
        h,w,c = img.shape
        boxes_list = self.dbnet_detector.detect_img(img)
        
        # 对分割的框进行左右适当扩充，提高识别的精度，系数0.95和0.05可以调整
        new_boxes_list = []
        for bb in boxes_list:
            xmin = 100000
            xmax = 0
            for p in bb:
                if p[0] < xmin:
                    xmin = p[0]
                if p[0] > xmax:
                    xmax = p[0]
            width = xmax - xmin

            new_bb = []
            for p in bb:
                if p[0] - xmin > 0.97 * width:
                    new_bb.append([min(w, int(p[0] + p[0] - xmin - 0.97 * width)), int(p[1])])
                elif p[0] - xmin < 0.03 * width:
                    new_bb.append([max(0, int(p[0] - (0.03 * width - p[0] + xmin))), int(p[1])])
                else:
                    new_bb.append([int(p_p) for p_p in p])
            new_boxes_list.append(new_bb)
            
        #items = BoxUtil.crop_items(img, new_boxes_list)

        return new_boxes_list
