# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2025/3/24 10:08 
# @Description  : server_search.py

import asyncio
import tornado.web
from tornado.platform.asyncio import AnyThreadEventLoopPolicy
from correction_service.common.logo import <PERSON><PERSON><PERSON>, RESET

from base_common import Constants, Context, Server<PERSON>rapper, RedisRequestor, OssUtil, LoggerFactory
from correction_service.pool import ServicePool
from correction_service.common import CorrectionContext
from correction_service.controller import SearchController

log = LoggerFactory.get_logger('SearchController')
def make_app():
    handlers = [(r'/search', SearchController)]
    SearchController.initialize_executor(Constants.CORRECTION_MISSION_NUMBER * 2)
    return tornado.web.Application(handlers, max_body_size=100 * 1024 * 1024, max_buffer_size=100 * 1024 * 1024)

def setup():
    Context.setup()
    CorrectionContext.setup()
    Constants.setup(Context.is_product())
    OssUtil.load_oss_config()
    ServicePool.initialize(Constants.CORRECTION_MISSION_NUMBER * 2)
    RedisRequestor.init_producers()

if __name__ == '__main__':
    print(f"{GREEN}系统已初始化.{RESET}")
    asyncio.set_event_loop_policy(AnyThreadEventLoopPolicy())
    server_warapper = ServerWrapper(log, '拍照批改搜页', 8887)
    log.info(f"拍照批改搜页监听http://0.0.0.0:8887/search")
    server_warapper.start_server(make_app, setup)
