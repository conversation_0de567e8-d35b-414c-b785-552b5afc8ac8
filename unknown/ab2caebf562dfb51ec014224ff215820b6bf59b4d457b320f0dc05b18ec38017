import tornado.ioloop
import tornado.web
import tornado.escape
import json
import os
import cv2
from PIL import Image
import time
import copy
import shutil
import requests
import asyncio
import numpy as np
import base64
from io import BytesIO

import can_predict

def base64_to_cv2(base64_code):
    img_data = base64.b64decode(base64_code)
    img_array = np.fromstring(img_data, np.uint8)
    img = cv2.imdecode(img_array, cv2.COLOR_RGB2BGR)

    return img
    
def base64_to_pil(base64_str):
    image = base64.b64decode(base64_str)
    image = BytesIO(image)
    image = Image.open(image)

    return image

class OcrFormular(tornado.web.RequestHandler):
    def post(self):
        '''
        data的格式：
        data:{
        'imgs':[base64(img),...]
        }
        '''
        time0 = time.time()
        final_result = {'flag':1000,'result':[]}
            
        try:
            data=self.request.body
            data_json=json.loads(data)
            imgs = [base64_to_cv2(bb) for bb in data_json['imgs']]
        except:
            print('error in download data: \n')
            final_result['flag'] = 1001 #输入数据异常
            self.write(json.dumps(final_result, ensure_ascii=False))
            return

        #try:
        pred_txts = ocr_predictor.predict(imgs)    
        final_result['result'] = pred_txts
        self.write(json.dumps(final_result, ensure_ascii=False))
        time1 = time.time()
        '''  
        except:
            final_result['flag'] = 1002 #识别异常
            self.write(json.dumps(final_result, ensure_ascii=False))
        print(final_result)
        '''
def make_app():
    return tornado.web.Application([
        (r"/OcrFormular", OcrFormular)
    ])

if __name__ == '__main__':
    pass
    #ocr_batch = 6
    #ocr_predictor = can_predict.CanPredictor()
    #print('ocr text model Loaded!')
    
    #app = make_app()
    #app.listen(8887)
    #tornado.ioloop.IOLoop.current().start()