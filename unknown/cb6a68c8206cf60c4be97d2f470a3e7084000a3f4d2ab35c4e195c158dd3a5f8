import os
import random
import cv2
import numpy as np
from base_common import LoggerFactory
log = LoggerFactory.get_logger('ZoneAlign')
       
def draw_result(vis_dir, photo,photo_boxes,pdf,pdf_boxs):
    for i,box in enumerate(photo_boxes):
        cv2.rectangle(photo, (box[0],box[1]),(box[2],box[3]), color=(0), thickness=1)
    #log.info(f'pdf_boxs: {pdf_boxs}')
    for i,box in enumerate(pdf_boxs):
        cv2.rectangle(pdf, (int(box[0][0]),int(box[0][1])),(int(box[2][0]),int(box[2][1])), color=(0), thickness=1)
    h,w = photo.shape
    pdf = cv2.resize(pdf,(w,h))
    
    new_img = np.zeros((h,w*2),dtype=np.uint8)

    new_img[:,:w] = pdf
    new_img[:,w:] = photo
    
    cv2.imwrite(vis_dir+f'{random.randint(0,1000)}.jpg',new_img)
    
    
       
def draw_result_tmp(vis_dir,pdf,pdf_boxs):
    for i,box in enumerate(pdf_boxs):
        #cv2.rectangle(pdf, (box[0],box[1]),(box[2],box[3]), color=(0), thickness=1)
        cv2.rectangle(pdf, (int(box[0][0]),int(box[0][1])),(int(box[2][0]),int(box[2][1])), color=(0), thickness=1)
    
    cv2.imwrite(vis_dir+f'{random.randint(0,1000)}_tmp.jpg',pdf)
    