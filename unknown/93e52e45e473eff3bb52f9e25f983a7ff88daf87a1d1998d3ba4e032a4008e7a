# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/7 14:16 
# @Description  : data_of_book_response.py
import time
from base_common import BaseResponse


class BookResponse(BaseResponse):
    def __init__(self, st=None, et=time.time(), message=None, error_code=None, success=False, response=None,
                 base_path=None, search_data=None, page_data=None):
        super().__init__(st, et, message, error_code, success, response)
        self._base_path = base_path
        self._search_data = search_data
        self._page_data = page_data

    def set_base_path(self, base_path=None):
        self._base_path = base_path
        return self
    def get_base_path(self): return self._base_path
    def set_search_data(self, search_data=None):
        self._search_data = search_data
        return self
    def get_search_data(self): return self._search_data
    def set_page_data(self, page_data=None):
        self._page_data = page_data
        return self
    def get_page_data(self): return self._page_data
