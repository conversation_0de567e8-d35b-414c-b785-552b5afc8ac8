
import re
from quick_calc_rec_service.model import util
from quick_calc_rec_service.model import maths
from base_common import LoggerFactory

log = LoggerFactory.get_logger('QuickCalculationModel')

from asteval import Interpreter
aeval = Interpreter()

def quick_correcting(pred_str):
    if pred_str.find('P') != -1:
        return False
    pred_str = pred_str.replace('$', '')
    pred_str = pred_str.replace('^2', 'W').replace('^3', 'E')  # 替换平方和立方

    #     F{}{} 转换成{/}的样式; ((2/3) / (1/2)) 的情况会出现异常
    pred_str = pred_str.replace('F{', '((').replace('}{', ')/(').replace('}', '))')
    if pred_str.find('=') == -1 and pred_str.find('>') == -1 and pred_str.find('<') == -1:
        return False
    pred_str = util.quick_change_unit(pred_str)  # 将单位换算成*100等形式
    pred_str = pred_str.replace('$', '')
    if pred_str.find('=') != -1:
        pred_str = pred_str.replace('==', '=')  # 避免识别问题
        item_lists = pred_str.split('=')
        if len(item_lists) > 2:
            return False
        try:
            if abs(aeval.eval(item_lists[0]) - aeval.eval(item_lists[1])) < 1e-6:
                return True
            else:
                return False
        except Exception as e:
            return False

    if pred_str.find('<') != -1:
        item_lists = pred_str.split('<')
        if len(item_lists) > 2:
            return False
        try:
            if aeval.eval(item_lists[0]) - aeval.eval(item_lists[1]) < -1e-6:
                return True
            else:
                return False
        except:
            return False

    if pred_str.find('>') != -1:
        item_lists = pred_str.split('>')

        if len(item_lists) > 2:
            return False
        try:
            if aeval.eval(item_lists[0]) - aeval.eval(item_lists[1]) > 1e-6:
                return True
            else:
                return False
        except:
            return False

    return False

def judge_item(pred_str):
    '''
    final = {
    'flag':True/False,
    'reg_answers':['',''],
    'std_answers':['',''],
    }
    '''
    final = {'flag': None, 'reg_answers': [], 'std_answers': []}

    pred_str = util.fraud_frac_replace(pred_str)  # 一又二分之一改成“（1+1/2）”
    ori_line = str(pred_str)
    
#     pred_str = '8t56kg=($85.5$)t'
    if pred_str is None or len(pred_str) <= 2:
        final['flag'] = True
        return final
    # 目前暂时只支持>,<,=,≈的情况
    symbles = ['>', '<', '=', '@', '≈']
    disable = True
    for ss in symbles:
        if pred_str.find(ss) != -1:
            disable = False
            break
    if disable:
        return final
    #     初步判断式子是否正确，如果是正确的，可以简化返回处理。如果无法判断或者错误，再走后续流程

    if pred_str.find('$<$') == -1 and pred_str.find('<') != -1:
        pass
    elif pred_str.find('$>$') == -1 and pred_str.find('>') != -1:
        pass
    else:
        quick_pred = quick_correcting(pred_str)
        if quick_pred:
            final['flag'] = True
            return final

    # 印刷体打？的处理检查是否是389790000≈400000000的情况
    pattern = re.compile(r'(\d+)≈(\d+)')
    m = pattern.search(pred_str)
    if not m is None:
        final['flag'] = True
        return final

    # 处理印刷体<,>,=的情况,例如800千克<8000千克转换为800千克$<$8000千克
    # 将单位列表拼接成一个正则表达式字符串
    unit_pattern = '(' + '|'.join(re.escape(key) for key in util.units.keys()) + ')'
    # 编译正则表达式
    pattern = re.compile(r'(\d+)' + unit_pattern + r'([<>=])(\d+)' + unit_pattern)
    m = pattern.search(pred_str)
    if not m is None:
        final['flag'] = True
        return final

    # 匹配印刷体F{5}{10}-F{1}{10}=F{4}{10}
    # 匹配 F{n}{m} 格式的加减法表达式
    pattern = re.compile(r'(F\{\d+\}\{\d+\}\s*[+\-]\s*F\{\d+\}\{\d+\})\s*=\s*(F\{\d+\}\{\d+\})')
    m = pattern.search(pred_str)
    if not m is None:
        final['flag'] = True
        return final

    # 匹配印刷体5.1*0.8≈(4.1)的格式
    pattern = re.compile(r'\d+\.\d+\*\d+\.\d+\s*≈\s*\(\d+\.\d+\)')
    m = pattern.search(pred_str)
    if not m is None:
        final['flag'] = True
        return final

    # 匹配印刷体 30*(5)<270 和 (30)*5<270
    pattern = re.compile(r'(\d+\*\(\d+\)|\(\d+\)\*\d+)\s*<\s*\d+')
    m = pattern.search(pred_str)
    if m is not None:
        final['flag'] = True
        return final

    # 处理未作答的样例，当没找到$时，将第一个数字替换成手写，然后比较推理的答案，从而得出题干是否成立，本身就成立的话就是例题公式。
    if '$' not in pred_str:
        result = ""
        i = 0
        wrapped = False
        # 正则表达式匹配 F{n}{m} 的模式
        fraction_pattern = re.compile(r'F\{\d+\}\{\d+\}')
        m = fraction_pattern.search(pred_str)
        if m is None:
            while i < len(pred_str):
                # 匹配数字
                if pred_str[i].isdigit() and not wrapped:
                    start = i
                    while i < len(pred_str) and pred_str[i].isdigit():
                        i += 1
                    result += f"${pred_str[start:i]}$"
                    wrapped = True
                else:
                    result += pred_str[i]
                    i += 1

            pred_str = result

    # 替换其中的美元符号 $
    new_str, answer_list = util.replace_dollar(pred_str)
    # 没有识别到手写答案
    if len(answer_list) == 0:
        return final
        
    # 定义正则表达式以匹配等号左边的任意复杂的表达式
    left_pattern = re.compile(r'[\d+\-*/()]+')
    # 定义正则表达式以匹配等号右边的数字B数字格式
    right_pattern = re.compile(r'B(\d+)')
    # 分割字符串为等号左边和右边的部分
    parts = new_str.split('=')
    if len(parts) == 2:
        left_part = parts[0]
        right_part = parts[1]

        # 检查左边部分是否匹配表达式，右边部分是否匹配数字B数字格式
        if left_pattern.match(left_part) and right_pattern.match(right_part) and len(answer_list) == 1:
            left_value = util.find_best_round(aeval.eval(left_part), 2)
            # 提取右边表达式中的数字部分
            match = right_pattern.match(right_part)
            if match:
                num_after_B = int(match.group(1))

                # 计算B的值，考虑结果的长度
                left_length = len(str(left_value))
                right_length = len(str(num_after_B))
                B_value_length = left_length - right_length

                # 计算B的值
                std_ans = left_value // (10 ** B_value_length)  # 得到B的值

                if std_ans == answer_list[0]:
                    final['flag'] = True
                    return final
                else:
                    final['flag'] = False
                    final['reg_answers'] = [answer_list[0]]
                    final['std_answers'] = [std_ans]
                    return final
    
    # 1.2元+1.3元=$2.5元$ 题型
    for kk in util.units.keys():
        pattern = re.compile('[0-9.]+{}[\+\-\*/][0-9.]+{}=[(B)]'.format(kk, kk))
        m = pattern.match(new_str)
        if not m is None and len(answer_list) == 1:
            ans_num = util.find_best_round(aeval.eval(new_str.split('=')[0].replace(kk, '')))

            # 根据用户回答是否包含单位来决定标准答案是否包含单位
            #ans_str = str(ans_num) if new_str.split('=')[1].find(kk) != -1 else f"{ans_num}{kk}"
            
            user_ans = new_str.split('=')[1]
            if kk not in user_ans:
                # 如果用户作答的单位是跨单位的情况
                last_unit = util.find_unit_in_string(new_str)
                if last_unit is not None:
                    point_value = util.units[last_unit]
                    kk_value = util.units[kk]
                    ans_str = util.find_best_round(aeval.eval(f"{ans_num}{kk_value}/{point_value.replace('*', '')}"), 2)
                else:
                    ans_str = f"{ans_num}{kk}"
            else:
                ans_str = ans_num
            
            
            dist_gap = aeval.eval(new_str.split('=')[0].replace(kk,'') + '-' + answer_list[0].replace(kk,''))
            
            if answer_list[0].find(kk) != -1 and abs(dist_gap) < 1e-6:
                final['flag'] = True
                return final
            else:
                final['flag'] = False
                final['reg_answers'] = [answer_list[0]]
                if '%' in new_str:
                    if new_str.find('+') != -1 or new_str.find('-') != -1:
                        if answer_list[0].find('%') != -1:
                            ans_str = f"{ans_num}%"
                        else:
                            ans_num = ans_num / 100
                            if ans_num.is_integer():
                                ans_str = str(int(ans_num))
                            else:
                                ans_str = str(ans_num)
                    elif new_str.find('*') != -1:
                        if answer_list[0].find('%') != -1:
                            ans_num = ans_num / 100
                            if ans_num.is_integer():
                                ans_str = f"{int(ans_num)}%"
                            else:
                                ans_str = f"{ans_num}%"
                        else:
                            ans_str = str(round(ans_num / 10000, 5))
                    else:
                        ans_str = str(ans_num)
                final['std_answers'] = [ans_str]
                return final

    # 4702300092≈(B)亿  题型
    pattern = re.compile(r'(\d+)≈\(?\$(\d+(\.\d+)?[万亿])\$\)?')
    m = pattern.match(pred_str)
    if not m is None and len(answer_list) == 1:
        if '万' in answer_list[0]:
            unit_times = 10000
        if '亿' in answer_list[0]:
            unit_times = 100000000

        if '.' not in answer_list[0]:
            decimal_len = 0
        else:
            decimal_len = len(answer_list[0][:-1]) - answer_list[0].find('.') - 1
            
        an_str = round(float(new_str.split('≈')[0])/unit_times,decimal_len)
        an_str = str(an_str) if decimal_len!=0 else str(int(an_str))
        
        if an_str == answer_list[0][:-1]:
            final['flag'] = True
            return final
        else:
            final['flag'] = False
            final['reg_answers'] = [answer_list[0]]
            if '万' in answer_list[0] or '亿' in answer_list[0]:
                an_str = f"{an_str}{answer_list[0][-1]}"
            final['std_answers'] = [an_str]
            return final

    # 1$+$2$-$2=1题型
    pattern = re.compile('[0-9.]+B[0-9.]+B[0-9.]+=[0-9.]+')
    m = pattern.match(new_str)
    #if not m is None:
    if m is not None:
        top, bottom = new_str.split('=')
        bodies = top.split('B')
        for first in ['+', '-', '*', '/']:
            for second in ['+', '-', '*', '/']:
                if abs(aeval.eval(bodies[0] + first + bodies[1] + second + bodies[2]) - aeval.eval(bottom)) < 1e-6:
                    if answer_list[0] == first and answer_list[1] == second:
                        final['flag'] = True
                        return final
                    else:
                        final['flag'] = False
                        final['reg_answers'] = [answer_list[0], answer_list[1]]
                        final['std_answers'] = [first, second]
                        return final
                    break

    answer_list = [an.replace('F', '\\frac') for an in answer_list]

    new_str = new_str.replace('^2', 'W').replace('^3', 'E')  # 替换平方和立方

    new_str = new_str.replace('张', '*')

    #     F{}{} 转换成{/}的样式; ((2/3) / (1/2)) 的情况会出现异常
    new_str = new_str.replace('F{', '((').replace('}{', ')/(').replace('}', '))')
    new_str = new_str.replace('[', '(').replace(']', ')')

    #     约等号转换成@
    new_str = new_str.replace('≈', '@')

    # 处理B<3.5<B的题型
    pattern = re.compile('B<[0-9]+.[0-9]+<B')
    m = pattern.match(new_str)
    if not m is None:
        if len(answer_list) == 2:

            ans = aeval.eval(new_str.split('<')[1])
            an_1 = str(int(ans))
            an_2 = str(int(ans) + 1)
            if answer_list[0] == an_1 and answer_list[1] == an_2:
                final['flag'] = True
            else:
                final['flag'] = False
                final['reg_answers'] = [answer_list[0], answer_list[1]]
                final['std_answers'] = [an_1, an_1]
            return final
    # 处理B>3.5>B的题型
    pattern = re.compile('B>[0-9]+.[0-9]+>B')
    m = pattern.match(new_str)
    if not m is None:
        if len(answer_list) == 2:
            ans = aeval.eval(new_str.split('>')[1])
            an_1 = str(int(ans) + 1)
            an_2 = str(int(ans))
            if answer_list[0] == an_1 and answer_list[1] == an_2:
                final['flag'] = True
            else:
                final['flag'] = False
                final['reg_answers'] = [answer_list[0], answer_list[1]]
                final['std_answers'] = [an_1, an_2]
            return final

    # 处理只是比较大小的题型
    compare_line = util.quick_change_unit(new_str)
    try:
        if len(answer_list) == 1 and answer_list[0] in ['>', '<', '=']:
            pieces = compare_line.split('B')
            if len(pieces) == 2 and len(pieces[0]) > 0 and len(pieces[1]) > 0:
                left = aeval.eval(pieces[0])
                right = aeval.eval(pieces[1])
                if answer_list[0] == '>':
                    if left - right > 1e-9:
                        final['flag'] = True
                    elif abs(left - right) < 1e-9:
                        final['flag'] = False
                        final['reg_answers'] = ['>']
                        final['std_answers'] = ['=']
                    else:
                        final['flag'] = False
                        final['reg_answers'] = ['>']
                        final['std_answers'] = ['<']
                if answer_list[0] == '<':
                    if left - right < -1 * 1e-9:
                        final['flag'] = True
                    elif abs(left - right) < 1e-9:
                        final['flag'] = False
                        final['reg_answers'] = ['<']
                        final['std_answers'] = ['=']
                    else:
                        final['flag'] = False
                        final['reg_answers'] = ['<']
                        final['std_answers'] = ['>']
                if answer_list[0] == '=':
                    if left - right < -1 * 1e-9:
                        final['flag'] = False
                        final['reg_answers'] = ['=']
                        final['std_answers'] = ['<']
                    elif left - right > 1e-9:
                        final['flag'] = False
                        final['reg_answers'] = ['=']
                        final['std_answers'] = ['>']
                    else:
                        final['flag'] = True
                return final
    except:
        pass

    new_str = new_str.replace('(B)', 'B')  # '(B)' 替换成 'B'
    if len(answer_list) == 1 and answer_list[0].find('P') != -1:
        new_str = new_str.replace('B', 'BPB')  # 针对'$BPB$'的情况，这周情况下不用B直接代替
    # print('#######################################')

    try:
        # print('new_str: ',new_str)
        answer = maths.maths(new_str, answer_list)  # 答案格式为float，或者list
        # print('answer_0: ',answer)
        answer = util.ana_result(ori_line, answer)  # 重点处理多个答案
        # print('answer_1: ',answer)
    except:
        answer = None
    if answer is None:
        return final

    if len(answer_list) == 1:
        first_judge = util.judge_answer_blur(answer, answer_list[0])
        if first_judge:
            final['flag'] = True
            return final

    # 处理0.2:0.4=$$的情况
    if new_str.find(':') != -1 and new_str[-2] == '=' and new_str[-1] == 'B':
        if len(answer_list) != 1:
            return final
        tail = pred_str[pred_str.find('=') + 1:]  # 兼容:识别成了=的情况
        tail = tail.replace('$', '')
        if (tail.find(':') == -1 or tail.find('}{') == -1) and tail.find('=') != -1:
            answer_list[0] = tail.replace('=', ':')
        if answer_list[0].find('frac') != -1:  # 答案是分数
            if answer == answer_list[0]:
                final['flag'] = True
            else:
                final['flag'] = False
                final['reg_answers'] = [answer_list[0]]
                final['std_answers'] = [answer]
            return final

        elif answer_list[0].find(':') != -1:  # 答案是':'
            num0 = answer[answer.find('{') + 1:answer.find('}')]
            num1 = answer[answer.rfind('{') + 1:answer.rfind('}')]
            answer_str = str(num0) + ':' + str(num1)
            if answer_str == answer_list[0]:
                final['flag'] = True
            else:
                final['flag'] = False
                final['reg_answers'] = [answer_list[0]]
                final['std_answers'] = [answer_str]
            return final

        elif answer_list[0].find('.') != -1:  # 答案是小数
            # print('#################################',answer_list[0],answer)
            try:
                if answer == answer_list[0]:
                    final['flag'] = True
                    return final
                num0 = answer[answer.find('{') + 1:answer.find('}')]
                num1 = answer[answer.rfind('{') + 1:answer.rfind('}')]
                std_answer_str = str(num0) + ':' + str(num1)  # 覆盖将手写':'识别成'.',
                reg_answer_str = answer_list[0].split('.')[0] + ':' + answer_list[0].split('.')[1]
                # print(std_answer_str,reg_answer_str)
                if std_answer_str == reg_answer_str:
                    final['flag'] = True
                    return final

                answer_p = float(num0) / float(num1)
                if abs(answer_p - float(answer_list[0])) < 1e-5:
                    final['flag'] = True
                else:
                    final['flag'] = False
                    final['reg_answers'] = [answer_list[0]]
                    final['std_answers'] = [str(util.find_best_round(answer_p))]
                return final
            except:
                return final

        elif answer_list[0].find('%') != -1:  # 答案是百分数
            try:
                num0 = answer[answer.find('{') + 1:answer.find('}')]
                num1 = answer[answer.rfind('{') + 1:answer.rfind('}')]
                answer_p = float(num0) / float(num1)
                if abs(answer_p - float(answer_list[:-1]) / 100) < 1e-5:
                    final['flag'] = True
                else:
                    final['flag'] = False
                    final['reg_answers'] = [answer_list[0]]
                    final['std_answers'] = [str(util.find_best_round(answer_p * 100)) + '%']
                return final
            except:
                return final
        else:  # 答案是整数
            try:
                num0 = answer[answer.find('{') + 1:answer.find('}')]
                num1 = answer[answer.rfind('{') + 1:answer.rfind('}')]
                answer_tmp = int(num0) / int(num1)
                if str(answer_tmp) == answer_list[0]:
                    final['flag'] = True
                else:
                    final['flag'] = False
                    final['reg_answers'] = [answer_list[0]]
                    if int(num0) % int(num1) == 0:
                        final['std_answers'] = [str(int(answer_tmp))]  # 确保整除时去掉小数部分
                    else:
                        answer_str = str(num0) + ':' + str(num1)
                        final['std_answers'] = [answer_str]
                return final
            except:
                return final

    # 仅仅支持"1212313@" 和"123256@B万/亿"的情况
    if re.match('\d{4,}@', new_str) and len(answer_list) == 1:
        inds = re.match('\d{4,}@', new_str).span()
        if inds[0] != 0:  # 如果不是一开头就是上面的格式，就不处理
            pass
        elif isinstance(answer, str):  # 答案是“d万”的情况，只支持d为整数
            if answer == answer_list[0]:
                final['flag'] = True
            else:
                final['flag'] = False
                final['reg_answers'] = [answer_list[0]]
                final['std_answers'] = [answer]
            return final
        elif isinstance(answer, list):
            pass
        else:
            try:
                if answer_list[0].find('.') == -1:  # 答案没有小数点时，结果约到整数
                    gap = abs(int(answer) - aeval.eval(answer_list[0]))
                else:  # 答案有小数点，默认仅支持保留两位小数
                    gap = abs(answer - aeval.eval(answer_list[0]))

                if gap < 1e-5:
                    final['flag'] = True
                else:
                    final['flag'] = False
                    final['reg_answers'] = [answer_list[0]]
                    final['std_answers'] = [answer]

                return final
            except:
                return final
    # 5/2=$2P2$的情况
    if len(answer_list) == 1 and answer_list[0].find('P') != -1:
        if isinstance(answer, list) and len(answer) == 2:
            result_str = str(answer[0]) + 'P' + str(answer[1])
            if result_str == answer_list[0]:
                final['flag'] = True
            else:
                final['flag'] = False
                final['reg_answers'] = [answer_list[0]]
                final['std_answers'] = [result_str]
        else:
            return final

    elif len(answer_list) == 2 and new_str.find('P') != -1:
        if isinstance(answer, list) and len(answer) == 2:
            if answer_list[0] == str(answer[0]) and answer_list[1] == str(answer[1]):
                final['flag'] = True
            else:
                final['flag'] = False
                final['reg_answers'] = [answer_list[0], answer_list[1]]
                final['std_answers'] = [answer[0], answer[1]]
        else:
            return final


    elif len(answer_list) == 0:
        return final

    # 答案有多个，是一个列表的情况
    elif isinstance(answer, list):
        if len(answer_list) != len(answer):
            return final

        flag = True
        for i in range(len(answer_list)):
            if answer[i] == answer_list[i]:
                continue
            try:
                if abs(aeval.eval(str(answer[i])) - aeval.eval(answer_list[i])) > 1e-5:
                    flag = False
                    break
            except:
                flag = False
                break
        if flag:
            final['flag'] = True
        else:
            for i in range(len(answer_list)):
                final['flag'] = False
                final['reg_answers'].append(answer_list[i])
                final['std_answers'].append(answer[i])
        return final
    # 答案是一个字符串
    elif isinstance(answer, str):
        if answer == answer_list[0]:
            final['flag'] = True
        else:
            final['flag'] = False
            final['reg_answers'] = [answer_list[0]]
            final['std_answers'] = [answer]
    else:  # 答案是一个值
        try:
            python_expr = answer_list[0]
            # 如果是 LaTeX 的 \frac{}{} 格式，转换为 (a/b) 的格式
            if re.match(r'\\frac\{[^\}]+\}\{[^\}]+\}', answer_list[0]):
                python_expr = re.sub(r'\\frac\{([^\}]+)\}\{([^\}]+)\}', r'(\1/\2)', answer_list[0])

            if abs(answer - aeval.eval(python_expr)) < 1e-5:
                final['flag'] = True
            else:
                final['flag'] = False
                final['reg_answers'] = [answer_list[0]]
                final['std_answers'] = [answer]
        except:
            return final
    return final
