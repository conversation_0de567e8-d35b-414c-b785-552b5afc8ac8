# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/29 9:12 
# @Description  : server_surf.py
import cv2
# 下面两行位置最好不要改，测试发现这两行需和import cv2放在开头才能检测到可用gpu
device_id = 0
cv2.cuda.setDevice(device_id)
import sys

from base_common import Context, LoggerFactory, Constants, ParamLoader
from surf_service.service.service_of_surf import SurfService
log = LoggerFactory.get_logger('SurfService')
if __name__ == '__main__':
    args = sys.argv[1:]
    params = ParamLoader(args)
    if not params.is_success():
        log.error('Usage: python xx.py <port> <gpu_num>')
        exit(1)

    Context.setup()
    Constants.setup(Context.is_product())
    Context.set_gpu_num("0")
    service = SurfService()
    service.set_stop_time(params.get_stop_time())
    service.start(params.get_port(), True)