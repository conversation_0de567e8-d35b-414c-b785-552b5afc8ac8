# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 11:45 
# @Description  : data_of_answer_detect_request.py
import json
from base_common import BaseData


class AnswerDetectRequest(BaseData):
    def __init__(self, url_ori=None, url_photo=None, items=None, items_type=None, origin_image_url=None, pdf_path=None, surf_mat=None,
                 items_answer=None, boxs=None, photo_crop=None, temp_crop=None, verify_cal=None, img_base64=None):
        self._url_ori = url_ori
        self._url_photo = url_photo
        self._items = items
        self._items_type = items_type
        self._origin_image_url = origin_image_url
        self._items_answer = items_answer
        self._boxs = boxs
        self._photo_crop = photo_crop
        self._temp_crop = temp_crop
        self._verify_cal = verify_cal
        self._img_base64 = img_base64
        self._pdf_path = pdf_path
        self._surf_mat = surf_mat
        if surf_mat is None:
            self._surf_mat = -1000

    def get_pdf_path(self): return self._pdf_path
    def set_pdf_path(self, pdf_path): self._pdf_path = pdf_path
    def get_surf_mat(self): return self._surf_mat
    def set_surf_mat(self, surf_mat): self._surf_mat = surf_mat
    def get_url_ori(self): return self._url_ori

    def get_url_photo(self): return self._url_photo

    def get_items(self): return self._items

    def get_items_type(self): return self._items_type

    def get_origin_image_url(self): return self._origin_image_url

    def get_items_answer(self): return self._items_answer

    def get_boxs(self): return self._boxs

    def get_photo_crop(self): return self._photo_crop

    def get_temp_crop(self): return self._temp_crop

    def get_verify_cal(self): return self._verify_cal

    def set_url_ori(self, url_ori):
        self._url_ori = url_ori
        return self

    def set_url_photo(self, url_photo):
        self._url_photo = url_photo
        return self

    def set_items(self, items):
        self._items = items
        return self

    def set_items_type(self, items_type):
        self._items_type = items_type
        return self

    def set_origin_image_url(self, origin_image_url):
        self._origin_image_url = origin_image_url
        return self

    def set_items_answer(self, items_answer):
        self._items_answer = items_answer
        return self

    def set_boxs(self, boxs):
        self._boxs = boxs
        return self

    def set_photo_crop(self, photo_crop):
        self._photo_crop = photo_crop
        return self

    def set_temp_crop(self, temp_crop):
        self._temp_crop = temp_crop
        return self

    def set_verify_cal(self, verify_cal):
        self._verify_cal = verify_cal
        return self

    def set_img_base64(self, img_base64):
        self._img_base64 = img_base64
        return self

    def get_img_base64(self): return self._img_base64
    def to_dict(self):
        return {
            "url_ori": self._url_ori,
            "url_photo": self._url_photo,
            "items": self._items,
            "items_type": self._items_type,
            "originImageUrl": self._origin_image_url,
            "items_answer": self._items_answer,
            "boxs": self._boxs,
            "photo_crop": self._photo_crop,
            "temp_crop": self._temp_crop,
            "verifyCal": self._verify_cal
        }
    def to_json(self):
        return json.dumps(self.to_dict())
