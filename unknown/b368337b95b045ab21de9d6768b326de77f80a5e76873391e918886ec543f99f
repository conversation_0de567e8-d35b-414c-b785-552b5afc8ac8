# -*- coding: utf-8 -*-
# @Time    : 2020/6/5 11:34
# <AUTHOR> <PERSON><PERSON><PERSON>
from .FPN import FPN
from .FPEM_FFM import FPEM_FFM
from .FPN_ASF import FPN_ASF

__all__ = ['build_neck']
support_neck = ['FPN', 'FPEM_FFM','FPN_ASF']


def build_neck(neck_name, **kwargs):
    assert neck_name in support_neck, f'all support neck is {support_neck}'
    neck = eval(neck_name)(**kwargs)
    return neck
