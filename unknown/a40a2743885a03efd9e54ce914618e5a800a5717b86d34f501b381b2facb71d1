# -*- coding: utf-8 -*-
# @Time    : 2019/8/23 21:54
# <AUTHOR> <PERSON><PERSON><PERSON>

from answer_det_service.model.models.backbone.swin_trainsformer import *


__all__ = ['build_backbone']

support_backbone = ['resnet18', 'deformable_resnet18', 'deformable_resnet50',
                    'resnet50', 'resnet34', 'resnet101', 'resnet152',
                    'resnest50', 'resnest101', 'resnest200', 'resnest269',
                    'shufflenet_v2_x0_5', 'shufflenet_v2_x1_0', 'shufflenet_v2_x1_5', 'shufflenet_v2_x2_0',
                    'MobileNetV3','swin_t','swin_b','hrnet_18']


def build_backbone(backbone_name, **kwargs):
    assert backbone_name in support_backbone, f'all support backbone is {support_backbone}'
    backbone = eval(backbone_name)(**kwargs)
    return backbone
