"""
公式识别模型的预处理的脚本，主要是对图片进行操作。
"""
import cv2
import numpy

class FormulaUtil:
    @staticmethod
    def truncate_end(list_of_ids, id_end):
        list_trunc = []
        for idx in list_of_ids:
            if idx == id_end:
                break
            else:
                list_trunc.append(idx)

        return list_trunc

    @staticmethod
    def ids_to_str(ids, rev_vocab, id_end):
        ids = FormulaUtil.truncate_end(ids, id_end)
        s = [rev_vocab[idx] for idx in ids]
        return "".join(s)

    @staticmethod
    def get_new_size(old_size, buckets):
        if buckets is None:
            return old_size,old_size
        else:
            w, h = old_size
            for i in range(3):
                if w > 800 or h > 256:
                    w = int(w / 2)
                    h = int(h / 2)
                else:
                    break
            for (w_b, h_b) in buckets:
                if w_b >= w and h_b >= h:
                    return [w_b, h_b],(w, h)

            return old_size,old_size

    @staticmethod
    def pad_image(img, pad_size=None, buckets=None):
        if pad_size is None:
            pad_size = [0, 0, 0, 0]
        top, left, bottom, right = pad_size
        h, w, _ = img.shape
        old_size = (w + left + right, h + top + bottom)
        new_size,size_ = FormulaUtil.get_new_size(old_size, buckets)
        if size_ != (w,h):
            img = cv2.resize(img,size_)
        new_im = numpy.ones((new_size[1],new_size[0],3), dtype=numpy.float32)*255./2
        new_im[top:size_[1]+top,left:left+size_[0]] = img
        return new_im