import os
import cv2
import torch
import torch.nn as nn
import torch.nn.functional as F
import time
import math

from quick_calc_rec_service.model.models.densenet import DenseNet
from quick_calc_rec_service.model.models.attention import Attention
from quick_calc_rec_service.model.models.decoder import PositionEmbeddingSine
from quick_calc_rec_service.model.models.counting import CountingDecoder as counting_decoder
from quick_calc_rec_service.model.counting_utils import gen_counting_label
from quick_calc_rec_service.model.utils import draw_attention_map, draw_counting_map
from base_common import Constants,LoggerFactory
log = LoggerFactory.get_logger('QuickcalReg')

class Inference(nn.Module):
    def __init__(self, params=None, draw_map=False):
        super(Inference, self).__init__()
        self.params = params
        self.draw_map = draw_map
        self.use_label_mask = params['use_label_mask']
        self.encoder = DenseNet(params=self.params)
        self.in_channel = params['counting_decoder']['in_channel']
        self.out_channel = params['counting_decoder']['out_channel']
        self.counting_decoder1 = counting_decoder(self.in_channel, self.out_channel, 3)
        self.counting_decoder2 = counting_decoder(self.in_channel, self.out_channel, 5)
        self.device = params['device']
        self.decoder = decoder_dict[params['decoder']['net']](params=self.params)
        """经过cnn后 长宽与原始尺寸比缩小的比例"""
        self.ratio = params['densenet']['ratio']

        with open(params['word_path']) as f:
            words = f.readlines()
            print(f'共 {len(words)} 类符号。')
        self.words_index_dict = {i: words[i].strip() for i in range(len(words))}
        self.cal_mae = nn.L1Loss(reduction='mean')
        self.cal_mse = nn.MSELoss(reduction='mean') 

    def forward(self, images,lalels=None):
        cnn_features = self.encoder(images)
        batch_size, _, height, width = cnn_features.shape
        counting_preds1, counting_maps1 = self.counting_decoder1(cnn_features, None)
        counting_preds2, counting_maps2 = self.counting_decoder2(cnn_features, None)
        counting_preds = (counting_preds1 + counting_preds2) / 2
        counting_maps = (counting_maps1 + counting_maps2) / 2
        
        return self.decoder(cnn_features, counting_preds, lalels=lalels)

class AttDecoder(nn.Module):
    def __init__(self, params):
        super(AttDecoder, self).__init__()
        self.params = params
        self.input_size = params['decoder']['input_size']
        self.hidden_size = params['decoder']['hidden_size']
        self.out_channel = params['encoder']['out_channel']
        self.attention_dim = params['attention']['attention_dim']
        self.dropout_prob = params['dropout']
        self.device = params['device']
        self.word_num = params['word_num']
        self.ratio = params['densenet']['ratio']

        self.init_weight = nn.Linear(self.out_channel, self.hidden_size)
        self.embedding = nn.Embedding(self.word_num, self.input_size)
        self.word_input_gru = nn.GRUCell(self.input_size, self.hidden_size)
        self.encoder_feature_conv = nn.Conv2d(self.out_channel, self.attention_dim, kernel_size=1)
        self.word_attention = Attention(params)

        self.word_state_weight = nn.Linear(self.hidden_size, self.hidden_size)
        self.word_embedding_weight = nn.Linear(self.input_size, self.hidden_size)
        self.word_context_weight = nn.Linear(self.out_channel, self.hidden_size)
        self.counting_context_weight = nn.Linear(112, self.hidden_size)
        self.word_convert = nn.Linear(self.hidden_size, self.word_num)
        self.hw_code = params['hw_code']
        if params['dropout']:
            self.dropout = nn.Dropout(params['dropout_ratio'])
            
    def forward(self, cnn_features, counting_preds,lalels=None):
        if lalels is None:
            return self.forward_ori(cnn_features, counting_preds)
        else:
            return self.forward_lbl(cnn_features, counting_preds,lalels)
        
    def forward_lbl(self, cnn_features, counting_preds, lalels):
        batch_size, _, height, width = cnn_features.shape
        image_mask = torch.ones((batch_size, 1, height, width)).to(self.device)
        
        cnn_features_trans = self.encoder_feature_conv(cnn_features)
        position_embedding = PositionEmbeddingSine(256, normalize=True)
        pos = position_embedding(cnn_features_trans, image_mask[:,0,:,:])
        cnn_features_trans = cnn_features_trans + pos

        word_alpha_sum = torch.zeros((batch_size, 1, height, width)).to(device=self.device)
        hidden = self.init_hidden(cnn_features, image_mask)
        word_embedding = self.embedding(torch.ones([batch_size]).long().to(device=self.device))
        counting_context_weighted = self.counting_context_weight(counting_preds)
  
        word_scores = []
        hw_flags = []
        stop_flags = []
        for j in range(batch_size):
            word_scores.append([])
            stop_flags.append(0)
            hw_flags.append(False)

        i = 0
        
        while i < 200:
            hidden = self.word_input_gru(word_embedding, hidden)
            word_context_vec, word_alpha, word_alpha_sum = self.word_attention(cnn_features, cnn_features_trans, hidden,
                                                                               word_alpha_sum, image_mask)

            current_state = self.word_state_weight(hidden)
            word_weighted_embedding = self.word_embedding_weight(word_embedding)
            word_context_weighted = self.word_context_weight(word_context_vec)
            
            if self.params['dropout']:
                word_out_state = self.dropout(current_state + word_weighted_embedding + word_context_weighted + counting_context_weighted) 
            else:
                word_out_state = current_state + word_weighted_embedding + word_context_weighted + counting_context_weighted

            word_record = torch.zeros([batch_size]).to(device=self.device)
            
            for j in range(batch_size):
                if stop_flags[j] == 1:
                    continue
                word_prob = self.word_convert(word_out_state[j].unsqueeze(0))
                word_prob = torch.nn.functional.softmax(word_prob,1)
                score = word_prob[0][lalels[j][i]]
                word_scores[j].append(score)
                '''
                if lalels[j][i] == self.hw_code:
                    hw_flags[j] = False if hw_flags[j] else True
                else:
                    if hw_flags[j]:
                        word_scores[j].append(score)
                '''  
                word = [lalels[j][i]]
                word_record[j] = torch.tensor(word).to(device=self.device)
                if i == len(lalels[j])-1:
                    stop_flags[j] = 1
                
            if sum(stop_flags) == batch_size:
                break
            i+=1
            word_embedding = self.embedding(word_record.long())
        #log.info(f'word_scores: {word_scores}')
        judge_flags = []
        for i in range(len(word_scores)):
            if torch.tensor(word_scores[i]).min() > 0.1:
                judge_flags.append(True)
            else:
                judge_flags.append(False)
        
        return judge_flags

    def forward_ori(self, cnn_features, counting_preds):
        batch_size, _, height, width = cnn_features.shape
        image_mask = torch.ones((batch_size, 1, height, width)).to(self.device)
        
        cnn_features_trans = self.encoder_feature_conv(cnn_features)
        position_embedding = PositionEmbeddingSine(256, normalize=True)
        pos = position_embedding(cnn_features_trans, image_mask[:,0,:,:])
        cnn_features_trans = cnn_features_trans + pos

        word_alpha_sum = torch.zeros((batch_size, 1, height, width)).to(device=self.device)
        hidden = self.init_hidden(cnn_features, image_mask)
        word_embedding = self.embedding(torch.ones([batch_size]).long().to(device=self.device))
        counting_context_weighted = self.counting_context_weight(counting_preds)
  
        word_probs = []
        word_alphas = []
        stop_flags = []
        for j in range(batch_size):
            word_probs.append([])
            word_alphas.append([])
            stop_flags.append(0)

        i = 0
        while i < 200:
            hidden = self.word_input_gru(word_embedding, hidden)
            word_context_vec, word_alpha, word_alpha_sum = self.word_attention(cnn_features, cnn_features_trans, hidden,
                                                                               word_alpha_sum, image_mask)

            current_state = self.word_state_weight(hidden)
            word_weighted_embedding = self.word_embedding_weight(word_embedding)
            word_context_weighted = self.word_context_weight(word_context_vec)
            
            if self.params['dropout']:
                word_out_state = self.dropout(current_state + word_weighted_embedding + word_context_weighted + counting_context_weighted) 
            else:
                word_out_state = current_state + word_weighted_embedding + word_context_weighted + counting_context_weighted

            word_record = torch.zeros([batch_size]).to(device=self.device)
            
            for j in range(batch_size):
                if stop_flags[j] == 1:
                    continue
                word_prob = self.word_convert(word_out_state[j].unsqueeze(0))
                word_prob = torch.nn.functional.softmax(word_prob,1)
                ind, word = word_prob.max(1)
                word_record[j] = word
                if word.item() == 0:
                    stop_flags[j] = 1
                else:
                    word_alphas[j].append(word_alpha)
                    word_probs[j].append(word)
                
                if sum(stop_flags) == batch_size:
                    return word_probs
            i+=1
            word_embedding = self.embedding(word_record.long())
        return word_probs

    def init_hidden(self, features, feature_mask):
        average = (features * feature_mask).sum(-1).sum(-1) / feature_mask.sum(-1).sum(-1)
        average = self.init_weight(average)
        return torch.tanh(average)

decoder_dict = {
    'AttDecoder': AttDecoder
}