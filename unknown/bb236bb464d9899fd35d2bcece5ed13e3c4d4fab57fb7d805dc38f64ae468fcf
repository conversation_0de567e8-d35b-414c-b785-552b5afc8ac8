# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/12/6 10:18 
# @Description  : server_zone_align.py
import sys
from base_common import Context, LoggerFactory, Constants, ParamLoader
from zone_align_service.service.service_of_zone_align import ZoneAlignService
log = LoggerFactory.get_logger('ZoneAlignService')

if __name__ == '__main__':
    args = sys.argv[1:]
    params = ParamLoader(args)
    if not params.is_success():
        log.error('Usage: python xx.py <port> <gpu_num>')
        exit(1)

    Context.setup()
    Constants.setup(Context.is_product())
    Context.set_gpu_num(params.get_gpu_num())
    service = ZoneAlignService(int(params.get_gpu_num()))
    service.set_stop_time(params.get_stop_time())
    service.start(params.get_port())