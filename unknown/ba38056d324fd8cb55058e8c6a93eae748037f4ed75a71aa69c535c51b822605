import os
import cv2
import torch
import numpy as np
from vertical_calc_rec_service.model.CAN.utils import load_config, load_checkpoint
from vertical_calc_rec_service.model.CAN.models.infer_model import Inference
from vertical_calc_rec_service.model.CAN.dataset import Words

class CanPredictor():
    def __init__(self, model_path):
        """加载config文件"""
        params = load_config(os.path.abspath(f"{model_path}/config.yaml"))
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
#         self.device = torch.device('cpu')
        params['device'] = self.device

        word_path = f'{model_path}/char.txt'
        self.words = Words(word_path)
        params['word_num'] = len(self.words)
        params['use_label_mask'] = False
        params['word_path'] = word_path

        self.model = Inference(params, draw_map=False)
        self.model = self.model.to(self.device)
        load_checkpoint(self.model, None, f'{model_path}/0101.pth')
        self.model.eval()
        
    def predict(self,img):
        ratio = 160/img.shape[0]
        img = cv2.resize(img, (0, 0), fx=ratio, fy=ratio, interpolation=cv2.INTER_NEAREST)

        img = torch.Tensor(255-img) / 255
        img = img.permute(2, 0, 1).unsqueeze(0)
        img = img.to(self.device)
        probs,scores = self.model(img)
        prediction = self.words.decode(probs)
#         print(prediction,scores)
        return [prediction.replace(' ','')],np.array(scores).min()
