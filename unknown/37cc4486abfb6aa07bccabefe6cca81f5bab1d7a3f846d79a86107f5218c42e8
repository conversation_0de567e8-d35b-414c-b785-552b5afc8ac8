# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/7 16:03
# @Description  : 批改中题逻辑判断

from base_common import Constants as CC, TopicType as TT, TopicMode as TM

class TopicUtil:
    @staticmethod
    def adjust_coords(coords: dict):
        for item, coord in coords.items():
            if 0 == len(coord):
                continue
            if isinstance(coord[0], int):
                new_coord = [pos if pos > 0 else 0 for pos in coord]
                coords[item] = new_coord
            elif isinstance(coord[0], list):
                new_pos_list = []
                for pos_list in coord:
                    # update by dewen
                    if isinstance(pos_list[0], list):
                        new_sub_coord_list = []
                        for sub_pos_list in pos_list:
                            new_sub_coord_list.append([pos if pos > 0 else 0 for pos in sub_pos_list])
                        new_pos_list.append(new_sub_coord_list)
                    else:
                        new_coord_list = [pos if pos > 0 else 0 for pos in pos_list]
                        new_pos_list.append(new_coord_list)
                    # update by dewen
                    #new_coord_list = [pos if pos > 0 else 0 for pos in pos_list]
                    #new_pos_list.append(new_coord_list)
                coords[item] = new_pos_list

    # 根据答案文本计算不同的模式，方便后续控制文本识别
    @staticmethod
    def get_name(answer):
        answer_set = set(answer)
        if answer_set.issubset(CC.CHOICE_CHS) and len(answer) == 1:
            # 'choise'
            return TM.CHOICE
        elif answer_set.issubset(CC.JUDGMENT_CHS):
            # 'judgment'
            return TM.JUDGMENT
        elif answer_set.issubset(CC.NUMBER_CHS):
            # 'number'
            return TM.NUMBER
        elif answer_set.issubset(CC.OPERATOR_CHS):
            # 'operator'
            return TM.OPERATOR
        elif answer_set.issubset(CC.NUMBOROP_CHS):
            # 'numborop'
            return TM.NUMBOROP
        elif len(answer_set & CC.NORMAL_CHS) > 0:
            # 'normal'
            return TM.NORMAL
        elif (answer.replace('，', ',').replace('。', '.').isascii() and
              len(set(answer.replace(' ', '').replace('，', '').replace('。', '')) & CC.ALPHABET_CHS) > 0 and
              not (answer[0] == '&' and answer[-1] == ';')):
            if len(answer_set & CC.OPERATOR_CHS) > 0 and len(answer) < 20:
                return TM.NORMAL
            # 'english'
            return TM.ENGLISH
        elif answer_set.issubset(CC.HANZI_CHS):
            # 'hanzi'
            return TM.HANZI
        # 'normal'
        return TM.NORMAL


    # 根据题目答案和题型计算文本识别模式。
    @staticmethod
    def get_mode_name(answer, itemtype):
        if itemtype == TT.WORD_PROBLEM:
            for ans in answer:
                for an in ans:
                    if an['type'] == TM.LATEX:
                        return TM.LATEX
            return TM.NORMAL

        if itemtype == TT.OFF_COMPUTE:
            for ans in answer:
                if isinstance(ans, list):
                    if ans[0]['type'] == TM.LATEX:
                        return TM.LATEX
                elif isinstance(ans, dict):
                    if ans['type'] == TM.LATEX:
                        return TM.LATEX
            return TM.NORMAL
        if type(answer) != list:
            return TopicUtil.get_name(answer)
        else:
            if type(answer[0]) != list:
                if len(answer) == 1 and answer[0]['type'] == TM.LATEX:
                    return TM.LATEX

                ans = [s['value'] for s in answer if s['type'] == TM.LATEX]
                if len(ans) > 0:
                    return TM.LATEX

                names = []
                ans = [s['value'] for s in answer if s['type'] == TM.TEXT]
                for an in ans:
                    names.append(TopicUtil.get_name(an))
                if len(set(names)) == 1:
                    return names[0]
                else:
                    return TM.NORMAL
            else:
                jgsp = ['√', '×']
                jssb = False
                names = []
                fullnames = []
                for ans in answer:
                    for an in ans:
                        if an['type'] == TM.TEXT:
                            names.append(TopicUtil.get_name(an['value']))
                            for q in jgsp:
                                if q in an['value']:
                                    jssb = True
                        fullnames.append(an['type'])

                if len(set(names)) == 1:
                    return names[0]

                if TM.LATEX in set(fullnames):
                    return TM.LATEX

                if {TM.ENGLISH, TM.CHOICE} == set(names):
                    return TM.ENGLISH

                if ({TM.ENGLISH, TM.CHOICE, TM.JUDGMENT} & set(names) ==
                        {TM.ENGLISH, TM.CHOICE, TM.JUDGMENT} and not jssb):
                    return TM.ENGLISH

                return TM.NORMAL
