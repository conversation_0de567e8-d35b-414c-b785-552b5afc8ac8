#!/bin/bash

export CURRENT_LOG_NAME=quick_correction

set_and_export_vals(){
  APPLICATION_NAME="server_$CURRENT_LOG_NAME"
  SCRIPT_PATH="$APPLICATION_NAME.py"
  CURRENT_DIR=$(pwd)
  WORK_HOME=$($CURRENT_DIR/../base_common/scripts/get_sys_conf.sh "WorkHome")
  ENVTYPE=$($CURRENT_DIR/../base_common/scripts/get_sys_conf.sh "EnvType")
  CONDA_ENV=$($CURRENT_DIR/../base_common/scripts/get_sys_conf.sh "Conda" $APPLICATION_NAME)
  PID_FILE="$WORK_HOME/pids/$APPLICATION_NAME.pid"

  export PYTHONPATH=$WORK_HOME
  export EnvType=$ENVTYPE

  source ~/anaconda3/etc/profile.d/conda.sh
  conda activate $CONDA_ENV

  echo "python working dir: $WORK_HOME"
  echo "conda env: $CONDA_ENV"
  echo "log name: $CURRENT_LOG_NAME"
  echo "env type: $EnvType"
}

start_server(){
  if [ -f "$PID_FILE" ]; then
    pids=$(cat "$PID_FILE")
    if ps -p $pids > /dev/null; then
      echo "Server already running with PID(s):"
      echo $pids
      return
    else
      echo "Removing stale PID file."
      rm -f "$PID_FILE"
    fi
  fi

  echo "Starting server..."
  nohup python -u $SCRIPT_PATH &> /dev/null &
  echo $! > "$PID_FILE"
  echo "Server started with PID(s):"
  cat "$PID_FILE"
  sleep 2
  ps -ef|grep $SCRIPT_PATH
}

stop_server() {
  if [ ! -f "$PID_FILE" ]; then
    echo "No server process found."
  else
    pids=$(cat "$PID_FILE")
    if [ -z "$pids" ]; then
      echo "No server process found."
    else
      if ps -p $pids > /dev/null; then
        echo "Stopping server..."
        for pid in $pids; do
          echo "Killing process ID $pid"
          kill -9 $pid
        done
        rm -f "$PID_FILE"
        echo "Server stopped."
      else
        echo "No server process found."
        rm -f "$PID_FILE"
      fi
    fi
  fi
}

restart_server() {
    stop_server
    sleep 1
    start_server
    exit 0
}

handle_command() {
  case "$1" in
      start)
          start_server
          ;;
      stop)
          stop_server
          ;;
      *)
          restart_server
          ;;
  esac
}

set_and_export_vals
handle_command "$1"