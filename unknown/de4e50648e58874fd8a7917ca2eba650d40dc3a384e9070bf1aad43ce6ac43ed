import os

with open('../data/mask_821.txt','r') as f:
    lines = f.readlines()

img_names = []
lbls = []

for line in lines:
    img_names.append(line.strip().split(' ')[0])
    lbls.append(line.strip().split(' ')[1])

with open('errors.txt','w') as ef:
    with open('errors_record.txt','r') as ff:
        lines = ff.readlines()
    for line in lines:
        img_name = line.strip().split('\t')[0]
        
        write_line = img_name + '\t' + lbls[img_names.index(img_name)] + '\n'
        
        ef.write(write_line)