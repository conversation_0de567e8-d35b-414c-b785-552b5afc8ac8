# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 16:03 
# @Description  : controller_of_conrrection.py
import cv2
import json
import uuid
import asyncio
import traceback
import tornado.web

from correction_service.common import CorrectionContext
from correction_service.service.service_of_correction import CorrectionService
from correction_service.pool import ServicePool
from correction_service.data import CorrectRequest

from base_common import Context, FileUtil, LoggerFactory, ThreadExecutor, ErrorCode as EC

log = LoggerFactory.get_logger("CorrectionController")

def exec_mission(req_data: CorrectRequest, correct_ser: CorrectionService):
    """
    处理任务
    :param req_data: 请求参数
    :param correct_ser: 批改服务
    :return: 批改结果json str
    """
    mission_id = req_data.get_mission_id()
    try:
        correction_data = CorrectionContext.get_correct_data(mission_id)
        correction_data.set_origin_image_url(req_data.get_image_url())
        correction_data.set_correction(req_data.is_correction())

        response_data = correct_ser.do_correction(req_data)
        if (not response_data.is_success() and req_data.get_biz_type() == 3
                and response_data.get_error_code() == EC.SEARCH_PAGE_ERROR):
            response_data = correct_ser.do_oral_calc(req_data, response_data.get_align_info())
            Context.exculde_error(mission_id)

        results_dict = response_data.to_json()
        results_dict['bizType'] = req_data.get_biz_type()
        results_dict['recordId'] = req_data.get_record_id()
        return results_dict
    except:
        exec_info = traceback.format_exc()
        log.error(f"(mission_id: {mission_id}) Oops!!! execute request exception, stack as {exec_info}")
        algo_resp = {'message': '-----!do_post error!', 'errorCode': EC.CORRECTION_ERROR,
                     'originImageUrl': req_data.get_image_url()}
        response_data = correct_ser.do_finally_correction(req_data, algo_resp)
        return response_data.to_json()
    finally:
        CorrectionContext.pop_correct_data(mission_id)

async def do_post(req_data: CorrectRequest):
    mission_id = req_data.get_mission_id()
    img_path = None
    try:
        mission_param = req_data.to_string()
        Context.set_request(mission_id, mission_param)
        log.info(f"(mission_id: {mission_id})(record_id:{req_data.get_record_id()}) 任务开始 批改请求参数: {mission_param}")
        release, correct_ser = ServicePool.get_service()
        loop = asyncio.get_event_loop()
        img_url, img_path, success = await loop.run_in_executor(
            CorrectionController.thread_executor,
            correct_ser.chk_img_url,
            req_data.get_record_id(),
            req_data.get_image_url(),
            req_data.get_env()
        )

        req_data.set_image_url(img_url)
        if not success:
            resp = {
                "message": f'message: There is something wrong with your URL {req_data.get_image_url()}',
                "errorCode": EC.SEARCH_PAGE_ERROR,
                "originImageUrl": req_data.get_image_url()
            }
            str_resp = json.dumps(resp, ensure_ascii=False)
            log.info(f"(mission_id: {mission_id}) 批改结果响应: {str_resp}")
            return str_resp
        user_image = cv2.imread(img_path, cv2.IMREAD_COLOR)
        req_data.set_user_image(user_image)
        #req_data.set_img_base64(ImageUtil.numpy_2_base64(user_image))
        loop = asyncio.get_event_loop()
        resp = await loop.run_in_executor(CorrectionController.thread_executor, exec_mission, req_data, correct_ser)
        str_resp = json.dumps(resp, ensure_ascii=False)
        log.info(f"(mission_id: {mission_id}) 批改结果响应: {str_resp}")
        return str_resp
    except:
        log.error(f"(mission_id: {mission_id}) Oops!!! execute request exception, stack as {traceback.format_exc()}")
        resp = {
            'message': '-----!do_post exception!',
            'errorCode': EC.CORRECTION_ERROR,
            'originImageUrl': req_data.get_image_url()
        }
        str_resp = json.dumps(resp, ensure_ascii=False)
        log.info(f"(mission_id: {mission_id}) 批改结果响应: {str_resp}")
        return str_resp
    finally:
        #log.debug(f"(mission_id: {mission_id}) 移除用户图片零时文件 {img_path}")
        FileUtil.remove_file(img_path)
        record, record1 = Context.correct_end(mission_id, resp)
        Context.del_record_id(req_data.get_mission_id())
        if release:
            del correct_ser
        else:
            ServicePool.release_service(correct_ser)
        if record:
            log.info(record)
        if record1:
            log.info(record1)


class CorrectionController(tornado.web.RequestHandler):
    thread_executor = None  # 用于存储线程池，每个子进程创建一个新的线程池
    @classmethod
    def initialize_executor(cls, threads_number):
        if cls.thread_executor is None:
            cls.thread_executor = ThreadExecutor(threads_number)

    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Methods", "POST")
        self.set_header("Access-Control-Allow-Headers", "token, content-type, user-token")
        self.set_header("Access-Control-Allow-Credentials", 'true')

    def build_correct_data(self) -> CorrectRequest:
        """
        从请求中构建批改请求体
        :return: 批改请求体
        """
        try:
            content_type = self.request.headers.get('Content-Type', None)
            log.debug(f"content type as {content_type, type(content_type)}")
            if content_type == "application/x-www-form-urlencoded":
                data_json = {arg: self.get_body_argument(arg) for arg in self.request.arguments}
                coords = [int(x) for x in data_json['coord'].strip('[').strip(']').split(',')]
                data_json['coord'] = coords
                data_json['bizType'] = int(data_json['bizType'])
                data_json["isOriginImg"] = data_json["isOriginImg"].lower() == "true"
                data_json["isNewCorrect"] = data_json.get("isNewCorrect", True)
                data_json["bookId"] = int(data_json["bookId"])
                req_data = CorrectRequest.fromDict(data_json)
            else:
                req_data = CorrectRequest.fromDict(json.loads(self.request.body))
            req_data.set_mission_id(str(uuid.uuid4()).replace('-', '').upper())
            return req_data
        except:
            log.error(f"Oops!!! build request exception, stack as {traceback.format_exc()}")
            return None

    def get(self):
        self.write(json.dumps({'result': 'Correction Server is OK.', 'message': 0}))

    async def post(self):
        """
        处理批改请求
        :return:
        """
        origin_url = self.request.headers.get('Origin')
        if origin_url is not None:
            self.set_header("Access-Control-Allow-Origin", origin_url)
        req_data = self.build_correct_data()
        if req_data is not None:
            Context.set_record_id(req_data.get_mission_id(), req_data.get_record_id())
            Context.correct_start(req_data.get_mission_id(), req_data.get_task_create())
            resp = await do_post(req_data)
            self.write(resp)
        else:
            self.write(json.dumps({'message': '-----!do_post error!', 'errorMessage': 'bad request param!', 'errorCode': EC.CORRECTION_ERROR}))
