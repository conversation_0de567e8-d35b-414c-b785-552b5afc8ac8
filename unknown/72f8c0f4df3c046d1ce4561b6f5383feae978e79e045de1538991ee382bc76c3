import json
import os
import cv2
import time
import shutil
import traceback
import numpy as np
from PIL import Image

from base_common import Constants, LoggerFactory, ImageUtil
from line_calc_service.model.runner import Inference
import line_calc_service.model.util as util

log = LoggerFactory.get_logger("LineModel")
class Line:
    def __init__(self, model_path, vis_line=False):
        self.net = Inference(model_path)
        self.vis_line = vis_line
        if self.vis_line:
            self.save_dir = f'{Constants.VIS_PATH}/vis_line/'
            if len(self.save_dir) != 0:
                if os.path.exists(self.save_dir):
                    shutil.rmtree(self.save_dir)
                os.mkdir(self.save_dir) 
        
    def run(self,data,img_name):   
        # log.info(f'#### line #### data:{data}') 
        result = {'boxs':{},'types':{}}
        img = data['image']
#         ori = data['ori']
        items = data['items_type'].keys()
        boxs_keys = data['boxs'].keys()
        item_lines = []
        for item in items:
            if data['items_type'][item] == 9:
                item_lines.append(item)
        if len(item_lines) == 0:
            log.info('没有需要连线的题目')
            return result
#         print('flag1..........')

        for j,item in enumerate(item_lines):
            try:
                
                item_box = data['items'][item]
                item_ans = data['items_answer'][item]
                #log.info(f'{item_ans}')
                item_x1 = item_box[0] #max(0,item_box[0])
                item_y1 = item_box[1] #max(0,item_box[1])
                item_x2 = item_box[2] #min(w,item_box[2])
                item_y2 = item_box[3] #min(h,item_box[3])
                w_i,h_i = item_x2-item_x1,item_y2-item_y1

                box_json,answers,answer_region = util.load_box(data['items_answer'][item],(item_x1,item_y1),(w_i,h_i),boxs_keys)
                #log.info(f'answers: {answers}')
                x1,y1,x2,y2 = answer_region
                padding_x = int((x2-x1)*0.2)
                padding_y = int((y2-y1)*0.2)
                gap_x1 = int(x1-item_x1-padding_x) if (x1-item_x1-padding_x>0) else 0
                gap_y1 = int(y1-item_y1-padding_y) if (y1-item_y1-padding_y>0) else 0
                gap_x2 = int(item_x2-padding_x-x2) if (item_x2-padding_x-x2>0) else 0
                gap_y2 = int(item_y2-padding_y-y2) if (item_y2-padding_y-y2>0) else 0



                img_item_vis = img.crop((item_x1,item_y1,item_x2,item_y2))
                img_item = img.crop((item_x1+gap_x1,item_y1+gap_y1,item_x2-gap_x2,item_y2-gap_y2))

    #                 print(img_item.size)
                points,features = self.net.infer_image(img_item)
    #                 print(points)
                points = np.array(points,dtype=np.int32)
                for point in points:
                    point[0]+=gap_x1
                    point[1]+=gap_y1

    #             ori_points = util.find_point(ori[item_y1:item_y2,item_x1:item_x2,:])
    #             ori_points,_ = self.net.infer_image(ori_item)
    #             print('points: ',points)
    #             if len(ori_points) != 0:
    #                 new_points = []
    #                 for pp in points:
    #                     flag = True
    #                     for opp in ori_points:
    #                         dist = ((opp[0]-pp[0])**2 + (opp[1]-pp[1])**2)**0.5
    #                         print('dist: ',dist)
    #                         if dist < 20:
    #                             flag = False
    #                             break
    #                     if flag:
    #                         new_points.append(pp)

    #                 points = new_points.copy()
    #             print('points: ',points)
                pred_answers,img_ = util.get_user_answers(points,features,box_json,img_item_vis, self.vis_line)
                #log.info(f'pred_answers: {pred_answers}')
                if self.vis_line:
                    cv2.imwrite(self.save_dir+'{}_{}.jpg'.format(img_name.split('.')[0],j),img_)
                for i,key in enumerate(box_json['group_top']['labels']):
                    result['boxs'][box_json['group_top']['blockName'][i]] = box_json['group_top']['region'][i]
                    #if len(pred_answers) == 0:
                    #    result['types'][box_json['group_top']['blockName'][i]] = -2
                    #    continue
                    gt_answers = []
                    for an in answers:
                        if an.split('-')[0] == str(key):
                            gt_answers.append(an)
                    p_answers = []
                    for an in pred_answers:
                        if an.split('-')[0] == str(key):
                            p_answers.append(an)

                    # 判断录入答案不需要连线的情况
                    no_answer_line = str(key) + '-@'
                    if len(gt_answers) == 1 and gt_answers[0] == no_answer_line:
                        #log.info(f'gt_answers: {gt_answers}')
                        if len(p_answers) == 0:
                            result['types'][box_json['group_top']['blockName'][i]] = 10
                        else:
                            result['types'][box_json['group_top']['blockName'][i]] = 11
                        continue
                    
                    if len(pred_answers) == 0:
                        if len(gt_answers) != 0:
                            result['types'][box_json['group_top']['blockName'][i]] = -2
                        else:
                            result['types'][box_json['group_top']['blockName'][i]] = -1
                        continue

                    if len(p_answers) == 0 and len(gt_answers) != 0:
                        result['types'][box_json['group_top']['blockName'][i]] = -2  # 答案需要连线，实际没有连线
                    elif len(p_answers) == 0 and len(gt_answers) == 0:
                        # result['boxs'].pop(box_json['group_top']['blockName'][i])
                        result['types'][box_json['group_top']['blockName'][i]] = -1  # 答案不需要连线，实际没有连线
                    elif len(gt_answers) == 0:
                        result['types'][box_json['group_top']['blockName'][i]] = 11#wrong，
                    elif len(p_answers) == 0:
                        result['types'][box_json['group_top']['blockName'][i]] = 11#wrong，
                    elif gt_answers == p_answers:
                        result['types'][box_json['group_top']['blockName'][i]] = 10#right
                    else:
                        flag_gt = True #记录是否全部标准答案都被包含在预测答案里面
                        flag_p = True #记录是否全部作答都是被包含在标准答案里面
                        flag_all = False #规则记录某一个答案框是否有一根连线正确
                        
                        #print(gt_answers,p_answers)
                        #['1.0-A', '1.0-C', '1.0-E', '1.0-F'] ['1.0-C', '1.0-A', '1.0-E']
                        #['2.0-B', '2.0-D', '2.0-G'] ['2.0-G', '2.0-D', '2.0-B', '2.0-F']
                        
                        #log.info(f'{gt_answers}')
                        #log.info(f'{p_answers}')
                        for gt in gt_answers:
                            if gt not in p_answers:
                                flag_gt = False
                                continue
                            else:
                                flag_all = True
                                p_answers.remove(gt)
                        if len(p_answers) != 0:
                            flag_p = False
                        
                        if flag_p and flag_gt:
                            result['types'][box_json['group_top']['blockName'][i]] = 10 #全对
                        elif flag_p:
                            result['types'][box_json['group_top']['blockName'][i]] = 12 #半对
                        else:
                            result['types'][box_json['group_top']['blockName'][i]] = 11 #错
            except:
               log.error(f"line exception {traceback.format_exc()}")
               continue
        #log.info(f'#### line #### result:{result}')
        return result
            
