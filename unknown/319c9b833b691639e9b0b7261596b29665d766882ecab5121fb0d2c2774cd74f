import re
from quick_calc_rec_service.model import util
from asteval import Interpreter

aeval = Interpreter()


def maths(line, answer_list):
    for ind, l in enumerate(line):  # 处理E100等异常情况
        if ind == len(line) - 1:
            break
        if l == 'E':
            if line[ind + 1] in ['一', '1', '二', '2', '三', '3', '四', '4', '五', '5', '六', '6', '七', '7', '八', '8',
                                 '九', '9', '零', '0', '十', '百', '千', '万', '亿']:
                return None

    for ind, l in enumerate(line):  # 处理*接单位而形成**100的异常情况
        if ind == len(line) - 1:
            continue
        if l == '*':
            for key in util.units.keys():
                if line[ind + 1:].startswith(key):
                    return None

    if line.find('**') != -1:  # 处理**100等异常情况
        return None

    line_ori = line
    #     line = line.replace('==','=')
    line = util.replace_cheng(line)  # 将“几成几”转换成数字，仅支持替换 “六成五” 或“六成”这种情况
    line = util.replace_zhe(line)  # 将“一三折，四折,”转换成数字，仅支持替换 “一三折” 或“四折”这种情况
    line = util.repalce_ge(line)  # 兼容个的两种情况
    # print('1: ',line)
    ##### 小数转分数，只支持‘1.25=’格式
    trans_d_f = util.trans_de_frac(line)
    if not trans_d_f is None:
        return trans_d_f

    ##### 小数转分数，只支持'0.05=((B)/(B))'格式
    trans_d_f1 = util.trans_de_frac1(line)
    if not trans_d_f1 is None:
        return trans_d_f1

        #####分数转小数，只支持‘{1/2}=’格式
    trans_f_d = util.trans_frac_de(line)
    if not trans_f_d is None:
        return trans_f_d

        ##### 处理 78/29 = F{($78$)}{($29$)}的情况
    trans_di_fr = util.trans_divide_frac(line)
    if not trans_di_fr is None:
        return trans_di_fr

    ##### 处理 120kg = 120/1000t 的情况
    trans_un_fr = util.trans_unit_frac(line)
    if not trans_un_fr is None:
        return trans_un_fr

    ##### 处理（）元（）角=1.2元 的情况
    trans_un_mu = util.trans_unit_multi1(line)
    if not trans_un_mu is None:
        return trans_un_mu

    trans_un_mu = util.trans_unit_multi2(line)
    if not trans_un_mu is None:
        return trans_un_mu

    # ##### 处理估算 128*11的情况+兼容约等于
    # gs = util.gusuan(line)
    # if not gs is None:
    #     return gs


    #line = line.replace('个', '')
    line = line.replace('零', '')
    proportion_answer_flag = False
    if line.find(':') != -1 and line[-2] == '=' and line[-1] == 'B':  # 如果在算式中发现了':',且'='是在最右侧。默认输出分数！
        proportion_answer_flag = True
    line = line.replace('D', '/').replace(':', '/')  # 使用python数学运算符代替文本字符。
    # print('2: ',line)
    if re.match('\d{4,}=', line) and line[-1] == '=':  # 1000000000=类似题型
        return util.change_unit(line)
    if re.match('\d{4,}@', line):  # 16810333@类似题型
        return util.change_unit_environ(line, answer_list)
    if util.find_space(line) == 0 and line.find('=') == -1 and line.find('@') == -1:  # 1+3类题型
        line += '='
    if line.find('P') != -1:  # 余数计算，仅支持5种情况：1.计算被除数；2.计算除数；3.计算商；4.计算余数；5.计算商+余数
        return util.cal_remainder(line)  # 计算余数的情况，答案可能为int或者[int,int]
    if re.match('[一二三四五六七八九]{2}', line) and len(line) <= 4:  # 处理三七二十一这种乘法口诀的情况，仅支持计算'三七（）'的情况
        answer = util.cal_chi(line)
        return answer  # 答案是字符串
    line, environ = util.judge_environ(line)
    line, uncertain = util.judge_uncertain(line)  # 处理含未知数的情况,仅支持'x+2x='等'='在最右侧的情况。
    line, multi_list = util.judge_multi(line)  # 处理'=()元()角'这种情况 TODO
    line, multi_list_4B = util.judge_multi_4B(line)  # 处理'=B*12B*100角'这种情况
    frac_answer_flag = False  # 用来标识字符串的答案只有两个空，且格式为'{()/()}'
    if util.find_space(line) == 2 and re.search('\{[BC()]+/[BC()]+\}', line):
        frac_answer_flag = True
        a = re.search('\{[BC()]+/[BC()]+\}', line).span()
        line = line.replace(line[a[0]:a[1]], 'B')

    # print('3: ',line)
    # 开始处理单位
    # line = line.replace('个','')
    keys = util.units.keys()
    keys = sorted(keys, key=lambda i: len(i), reverse=True)
    complex_keys = util.complex_units.keys()
    for spa in util.spaces:  # 暂时将空格代替成其他字符，避免干扰，单位替换了再更新回来
        line = line.replace(spa, 'B')

    for key in keys:
        replace = ''
        if key in complex_keys:  # 特殊处理在多个度量都会用到的单位，如：分
            cats = util.complex_units[key]['cats']
            replaces = util.complex_units[key]['replaces']
            for k, cat in enumerate(cats):
                for c in cat:
                    if line_ori.find(c) != -1:
                        replace = replaces[k]
                        break
                if replace:
                    break
            if not replace:
                replace = '*1'
        else:
            replace = util.units[key]  # 将单位用数量级替换，此时还没有统一某一类型的单位检查

        if line.find(key) != -1:
            line = line.replace('space', 'B')
            # 避免‘（6千米：20米）’这种情况出现，替代单位的时候需要将‘*10’和前面的数字括起来
            # 将单位和前面数字，空格合在一起，增加一个括号
            while re.search('[^\+\-\*/><=:\{\}\(\)\[\]B]+' + key, line):
                try:
                    # 解决‘6时30分’类似的情况，即度量单位后紧接数字，需要增加一个‘+’
                    if line[line.find(key) + len(key)] in ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '(', '{',
                                                           '[', 'B', 'F']:
                        key_inds = re.search('[^\+\-\*/><=:\{\}\(\)\[\]B]+' + key, line).span()
                        line_cut = line[key_inds[0]:key_inds[1]]
                        line_cut = '(' + line_cut.replace(key, replace + ')+')
                        line = line[:key_inds[0]] + line_cut + line[key_inds[1]:]
                    else:
                        raise
                except:
                    key_inds = re.search('[^\+\-\*/><=:\{\}\(\)\[\]B]+' + key, line).span()
                    line_cut = line[key_inds[0]:key_inds[1]]
                    line_cut = line_cut.replace(key, replace)
                    #                     print(line_cut)
                    line = line[:key_inds[0]] + '(' + line_cut + ')' + line[key_inds[1]:]
            line = line.replace(key, replace)
    # print('4: ',line)
    line = util.parentheses_frac(line)  # 将字符串种的{}替换成()，然后进行计算,同时处理假分数的情况
    if line.find('=') != line.rfind('='):  # 特别处理式子有多个等号的情况，如1+2=（）+0=（）
        if line.find('>') != -1 or line.find('<') != -1:  # 如果有多个等号并且还有<,>号，暂不支持处理
            return None
        items = line.split('=')
        answer_ = None
        for item in items:
            n = util.find_space(item)
            if n > 2:
                return None  # 如果每个计算单元内有多个空，不支持。如：（）+（）=2+（）=8
            if n == 0:
                answer_ = aeval.eval(item)
        if answer_ is None:
            return None  # 没有找到可计算答案的单元
        answers = []
        num = 0
        for item in items:
            n = util.find_space(item)
            if n == 1:
                answers.append(maths(item + '={}'.format(answer_), answer_list[num]))
                num += 1
            if n == 2:
                answers.extend(maths(str(answer_) + '={}'.format(item), [answer_list[num], answer_list[num + 1]]))
                num += 2
        return answers  # 返回一个list

    # print('5: ',line)
    answers_needed = 0
    for space in util.spaces:
        if line.find(space) != -1:
            space_ = space
            answers_needed += 1
    if answers_needed > 1:  # 多个答案空，暂不支持
        return None
    # print(line)
    if line.find('=') != -1:  # 有=号存在的情况
        if line.find('=') != line.rfind('='):
            return None  # 式子中有两个=，暂时不支持
        left = util.ana_equation(line)  # 判断=是否在最右侧。如等式为1+1=，1+1=()的类型,直接返回左侧带计算的部分，否则返回''
        # print('left: ',left)
        if left:
            answer = aeval.eval(left)
        else:
            answer = util.cal_equation(line, space_)  # 计算=不在最右侧的情况
        if frac_answer_flag:  # 判断答案格式是否为'{()/()}'，如果是则需要把小数结果转换再返回
            answer = util.change_float_frac_list(answer)
            return answer

        if proportion_answer_flag:
            answer = util.change_float_proportion_list(answer)
            return answer

        if environ:
            return round(answer)

        if uncertain:
            return str(answer) + uncertain

        if len(multi_list) > 0:
            answer = util.change_answer_multiunit_list(answer, multi_list)
            return answer

        if len(multi_list_4B) > 0:
            answer = util.change_answer_multiunit_list(answer, multi_list_4B)
            return answer

        return answer
    else:  # 如果没有等号，有两种情况，第一种是包含>,<号;第二种是需要填写>,<,=，
        if answers_needed == 0:  # 没有答题区域
            return None
        num_com = 0  # num_com = 1,情况1；num_com = 0情况2;num_com >1,多个符号，不支持；
        for compare in util.compares:
            if line.find(compare) != line.rfind(compare):
                return None  # 暂不支持式子里面有两个>,<符号
            if line.find(compare) != -1:
                num_com += 1
        if num_com > 1:
            return None  # 暂不支持式子里面有两个>,<符号
        elif num_com == 1:  # 情况1，包含>,<号;答案可能为数字或者+-*/，或者填写最大最小数字使得不等式成立
            return util.cal_non_equation_type1(line, space_)
        else:  # 情况2，式子中不包含>,<,=号,答案默认只为>,<,=
            #             print(line)
            return util.cal_non_equation_type2(line, space_)


if __name__ == '__main__':
    a = '((1)/(2))W=B'
    result = maths(a)
    print(result, 'result')
    final = util.ana_result(a, result)
    print(final)