# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/7 11:58 
# @Description  : data_of_ocr_formula.py
from base_common import BaseData
import json

class OcrFormulaData(BaseData):
    def __init__(self, record_id=None, batch_images=None, max_size=None):
        self._record_id = record_id
        self._batch_images = batch_images
        self._max_size = max_size
    def set_record_id(self, record_id):
        self._record_id = record_id
        return self
    def get_record_id(self): return self._record_id
    def set_batch_images(self, batch_images):
        self._batch_images = batch_images
        return self
    def get_batch_images(self): return self._batch_images
    def set_max_size(self, max_size):
        self._max_size = max_size
        return self
    def get_max_size(self): return self._max_size

    def to_json(self):
        return json.dumps({
            "recordId": self._record_id,
            "imgs": self._batch_images
        })
