import asyncio
import json
import os
import time
import traceback

import cv2
import numpy as np

import quick_correction_service.util as util
from base_common import Constants, ImageUtil, LoggerFactory, RedisRequestor
from base_common.context import Context
from base_common.mission_mode import Mission<PERSON>lisa
from base_common.service.service_of_base import BaseService
from base_common.service.service_of_redis import RedisManager
from quick_calc_rec_service.model.util import (
    Eval_X,
    ana_result,
    cal_number,
    replace_percent,
)
from quick_calc_rec_service.service.service_of_quick_rec import judge_item

log = LoggerFactory.get_logger("QuickAnswerDetectService")

unknowns = ["x", "y", "a", "b"]

one_piece_box_size = 4


def default_dump(obj):
    """Convert numpy classes to JSON serializable objects."""
    if isinstance(obj, (np.integer, np.floating, np.bool_)):
        return obj.item()
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj


def dis_compute(p1, p2):
    return np.sqrt(np.sum(np.square(p1 - p2)))


def crop_items(img, boxs):
    result_imgs = []
    for box in boxs:
        pts1 = np.array(box, np.float32).reshape(-1, 2)
        pts1 = cv2.minAreaRect(pts1)
        points = sorted(list(cv2.boxPoints(pts1)), key=lambda x: x[0])
        index_1, index_2, index_3, index_4 = 0, 1, 2, 3
        if points[1][1] > points[0][1]:
            index_1 = 0
            index_4 = 1
        else:
            index_1 = 1
            index_4 = 0
        if points[3][1] > points[2][1]:
            index_2 = 2
            index_3 = 3
        else:
            index_2 = 3
            index_3 = 2
        pts1 = np.float32(
            [
                points[index_1],
                np.array([points[index_2][0] + 3, points[index_2][1]]),
                np.array([points[index_3][0] + 3, points[index_3][1]]),
                points[index_4],
            ]
        )
        h_c = int((dis_compute(pts1[0], pts1[3]) + dis_compute(pts1[1], pts1[2])) // 2)
        w_c = int((dis_compute(pts1[0], pts1[1]) + dis_compute(pts1[2], pts1[3])) // 2)
        pts2 = np.float32([[0, 0], [w_c, 0], [w_c, h_c], [0, h_c]])
        M = cv2.getPerspectiveTransform(pts1, pts2)
        img_crop = cv2.warpPerspective(img, M, (w_c, h_c))
        result_imgs.append(img_crop)
    #         break
    return result_imgs


def shushi_judge_item(pred_str):
    """
    final = {
    'flag':True/False,
    'reg_answers':'',
    'std_answers':'',
    }
    """
    final = {"flag": None, "reg_answers": pred_str, "std_answers": ""}

    if pred_str.find("/") != -1:
        item0 = float(pred_str.split("/")[0])
        item1 = float((pred_str.split("/")[1]).split("S")[0])
        res = float(((pred_str.split("/")[1]).split("S")[1]).split("T")[0])
        if pred_str[-1] == "H":
            mod = 0
        else:
            mod = float(((pred_str.split("/")[1]).split("S")[1]).split("H")[-1])

        item0 = int(item0) if abs(int(item0) - item0) < 1e-6 else item0
        item1 = int(item1) if abs(int(item1) - item1) < 1e-6 else item1
        res = int(res) if abs(int(res) - res) < 1e-6 else res
        mod = int(mod) if abs(int(mod) - mod) < 1e-6 else mod

        if abs((mod + res * item1) - item0) < 1e-5:
            final["flag"] = True
            final["reg_answers"] = (
                str(item0) + "/" + str(item1) + "=" + str(res) + "……" + str(mod)
            )
            final["std_answers"] = (
                str(item0) + "/" + str(item1) + "=" + str(res) + "……" + str(mod)
            )
        else:
            res_ = round(item0 // item1, 2)
            mod_ = round(item0 % item1, 2)

            final["flag"] = False
            final["reg_answers"] = (
                str(item0) + "/" + str(item1) + "=" + str(res) + "……" + str(mod)
            )
            final["std_answers"] = (
                str(item0) + "/" + str(item1) + "=" + str(res_) + "……" + str(mod_)
            )
        return final

    items = pred_str.split("=")
    new_items = []
    for it in items:
        if (it.find("+") != -1 or it.find("-") != -1 or it.find("*") != -1) and it.find(
            "A"
        ) == -1:
            if len(it) > 0:
                new_items.append(it)
    if len(items[-1]) > 0:
        new_items.append(items[-1])
    items = new_items

    if len(items) > 3:
        return final
    if len(items) == 2:
        if abs(eval(items[0]) - eval(items[1])) < 1e-5:
            final["flag"] = True
            final["reg_answers"] = items[0] + "=" + items[1]
            final["std_answers"] = items[0] + "=" + items[1]
        else:
            final["flag"] = False
            final["reg_answers"] = items[0] + "=" + items[1]
            final["std_answers"] = items[0] + "=" + str(round(eval(items[0]), 2))
        return final
    if len(items) == 3:
        if items[1].find("+") != -1:
            title = items[0] + "+" + items[1].split("+")[-1]
            if abs(eval(title) - eval(items[2])) < 1e-5:
                final["flag"] = True
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + items[2]
            else:
                final["flag"] = False
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + str(round(eval(title), 2))
            return final

        if items[1].find("-") != -1:
            title = items[0] + "-" + items[1].split("-")[-1]
            if abs(eval(title) - eval(items[2])) < 1e-5:
                final["flag"] = True
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + items[2]
            else:
                final["flag"] = False
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + str(round(eval(title), 2))
            return final
        if items[1].find("*") != -1:
            if items[0].find("*") != -1:
                title = items[0] + "*" + items[1].split("*")[-1]
            else:
                title = "(" + items[0] + ")" + "*" + items[1].split("*")[-1]
            if abs(eval(title) - eval(items[2])) < 1e-5:
                final["flag"] = True
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + items[2]
            else:
                final["flag"] = False
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + str(round(eval(title), 2))
            return final

    return final


class QuickAnswerDetectService(BaseService):
    def __init__(self):
        super().__init__()
        self.redis_service = RedisManager.get_mission_data_redis()
        self.mission_queue_redis = RedisManager.get_mission_queue_redis()

    async def do_correction(self, data_json, img_photo_cv2, img_key, img_type):
        """处理POST请求
        data_json格式：
        {
            'alignUrl':'',旋转裁切后的用户图片
            'origilImageUrl':'',
            'recordId':'',
        }

        返回数据结构：
        {
            'alignUrl':'',旋转裁切后的用户图片
            'origilImageUrl':'',
            'recordId':'',
            'errorCode:30000,#30000：无异常；30020：解析json或者下载图片失败
            'errorMsg':'',
            'items':{#每一个基本的单元是一个item（口算，竖式，拖式，解方程等）
                '0':{ #编号
                    'answer':{#标准答案
                        'value':'',
                        'type':0,#0:文本；1:latex
                    },
                    'rec':{#识别结果
                        'value':'',
                        'type':0,
                    },
                    'stems':''题干
                    'result':0,#0:错误；1:正确；3:没作答；5:半对
                    'coord':[x1,y1,x2,y2],
                    'itemType':25,#25:智能口算；27：竖式计算；28：竖式计算只有过程,31:智能拖式；32:智能解方程
                }
            }
        }
        """
        try:
            if not os.path.exists("./records/"):
                os.makedirs("./records/")
            with open(
                "./records/record_{}.txt".format(time.strftime("%Y-%m-%d")), "a"
            ) as f:
                f.write(json.dumps(data_json) + "\n")
            img_name = data_json["alignUrl"].split("/")[-1]
            h, w, c = img_photo_cv2.shape
        except:
            log.error(f"error in download data!, due to {traceback.format_exc()}")
            data_json["errorCode"] = 30030
            data_json["errorMsg"] = "error in download data!"
            data_json["items"] = {}
            return data_json

        data_json["errorCode"] = 30000
        data_json["errorMsg"] = "success !"
        data_json["items"] = []
        # MemoryProfiler.start_profiling()
        #################################
        # 处理智能口算和处理竖式计算         #
        #################################
        # img_base64 = ImageUtil.numpy_2_base64(img_photo_cv2)
        futures = [
            self.handle_quick_calc(data_json, img_key, img_type, w, h),
            self.handle_vertical_calc(data_json, img_name, img_key, img_type, w, h),
        ]
        results = await asyncio.gather(*futures)
        # 获取连线题结果
        pred_strs_list, pred_boxs_list = results[0]

        #################################
        # 处理拖式计算及解方程  31、32      #
        #################################
        await self.handle_tuoshi_and_fangchen(
            data_json,
            pred_strs_list,
            pred_boxs_list,
            img_photo_cv2,
            img_key,
            img_type,
            w,
            h,
        )

        #################################
        # 挑选出未作答的题                 #
        #################################
        # 挑选出未作答的题

        done_boxs = []
        items = data_json["items"]
        for item in items:
            box = item["coord"]
            done_boxs.append(
                [int(box[0] * w), int(box[1] * h), int(box[2] * w), int(box[3] * h)]
            )
        if len(pred_boxs_list) != 0:
            if len(done_boxs) > 0:
                ious = util.calc_iou(pred_boxs_list, done_boxs)
                for ind in range(len(ious)):
                    if ious[ind].max() > 0.3:
                        continue
                    pb = pred_boxs_list[ind]
                    if (
                        len(pred_strs_list[ind][0]) >= 2
                        and pred_strs_list[ind][0].find("$") == -1
                    ):
                        data_json["items"].append(
                            {
                                "stems": "",
                                "itemType": 25,
                                "result": 3,
                                "rec": {},
                                "answer": {},
                                "coord": [pb[0] / w, pb[1] / h, pb[2] / w, pb[3] / h],
                            }
                        )
            else:
                for ind in range(len(pred_boxs_list)):
                    pb = pred_boxs_list[ind]
                    if (
                        len(pred_strs_list[ind][0]) >= 2
                        and pred_strs_list[ind][0].find("$") == -1
                    ):
                        data_json["items"].append(
                            {
                                "stems": "",
                                "itemType": 25,
                                "result": 3,
                                "rec": {},
                                "answer": {},
                                "coord": [pb[0] / w, pb[1] / h, pb[2] / w, pb[3] / h],
                            }
                        )

        log.info(f"答案检测结果: {json.dumps(data_json, ensure_ascii=False)}")

        correct_result = data_json["items"]
        test_resp = []
        for index in range(len(correct_result)):
            item_result = correct_result[index]["result"]
            test_resp.append(item_result)
        log.info(f"批改结果: {json.dumps(test_resp)}")
        log.info(f"批改响应: {json.dumps(data_json)}")
        if Constants.SAVE_VIS:
            util.draw_result(
                img_photo_cv2, img_name, data_json, Constants.VIS_QUICK_RESULTS_PATH
            )
        return data_json

    #################################
    # 处理智能口算                   #
    #################################
    async def handle_quick_calc(self, req_data, img_key, img_type, w, h):
        t0 = time.time()
        img = self.get_image({"img_key": img_key, "img_type": img_type})
        recordId = req_data["recordId"]
        iac_params = {
            recordId: {"id": recordId, "box": [0, 0, w, h]},
            "img_key": img_key,
            "img_type": img_type,
        }
        model_response = await RedisRequestor.ai_quick_calc_detect(iac_params, None)
        Context.report_cost(
            req_data["mission_id"], (MissionAlisa.QUICK_DET_SERVICE, time.time() - t0)
        )
        t0 = time.time()
        if model_response.is_success():
            iac_params["rec_boxs"] = model_response.get_json_response()
        rec_boxs = iac_params.get("rec_boxs", {})
        if len(rec_boxs.keys()) == 0:
            return [], []
        rec_boxs = rec_boxs[recordId]
        futures = []
        if len(rec_boxs) == 0:
            return [], []
        data_len = len(rec_boxs)
        start_index = 0
        index = 0
        length = (
            data_len // one_piece_box_size
            if data_len % one_piece_box_size == 0
            else data_len // one_piece_box_size + 1
        )
        while start_index < data_len:
            end_index = min(data_len, start_index + one_piece_box_size)
            quick_params = {
                "item_id": recordId,
                "index": index,
                "box": iac_params[recordId]["box"],
                "img_key": img_key,
                "img_type": img_type,
                "length": length,
                "rec_boxs": rec_boxs[start_index:end_index],
            }
            futures.append(RedisRequestor.ai_quick_calc_rec(quick_params, None))
            start_index += one_piece_box_size
            index += 1
        resps = await asyncio.gather(*futures)
        Context.report_cost(
            req_data["mission_id"], (MissionAlisa.QUICK_REC_SERVICE, time.time() - t0)
        )
        quickcal_results = []
        pred_strs_list = []
        pred_boxs_list = []
        rec_res = []
        for resp in resps:
            if resp.is_success():
                resp_data = resp.get_json_response()
                for item_id in resp_data:
                    q_results = resp_data[item_id]
                    quickcal_results.extend(q_results["final_results"])
                    pred_strs_list.extend(q_results["pred_strs_list"])
                    pred_boxs_list.extend(q_results["pred_boxs_list"])
                    rec_res.extend(q_results["rec_res"])
        if Constants.SAVE_VIS:
            ImageUtil.save_quick_det(img, [{"rec_boxs": rec_boxs, "bb": [0, 0, w, h]}])
            ImageUtil.save_quick_rec(img, rec_res)
        log.info(
            f"智能口算识别结果：{json.dumps(quickcal_results, ensure_ascii=False)}"
        )
        for i_quickcal, item_result in enumerate(quickcal_results):
            try:
                if not item_result["answer_flag"]:
                    continue
                item_quickcal = {
                    "stems": item_result["pred_str"],
                    "itemType": 25,
                    "coord": [
                        item_result["box"][0] / w,
                        item_result["box"][1] / h,
                        item_result["box"][2] / w,
                        item_result["box"][3] / h,
                    ],
                }

                if item_result["flag"]:
                    item_quickcal["result"] = 1  # 正确
                    item_quickcal["answer"] = {}
                    item_quickcal["rec"] = {}
                else:
                    item_quickcal["result"] = 0  # 错误
                    item_answer = item_result["std_answers"][0]
                    item_answer["value"] = item_answer["value"].replace("P", "……")
                    item_quickcal["answer"] = item_answer

                    item_reg = item_result["reg_answers"][0]
                    item_reg["value"] = item_reg["value"].replace("P", "……")
                    item_quickcal["rec"] = item_reg
                req_data["items"].append(item_quickcal)
            except:
                log.error(
                    f"error in decode quickcal item!, due to {traceback.format_exc()}"
                )
                continue
        return pred_strs_list, pred_boxs_list

    # 处理竖式计算 #
    async def handle_vertical_calc(self, data_json, img_name, img_key, img_type, w, h):
        t0 = time.time()
        params = {"img_name": img_name, "img_key": img_key, "img_type": img_type}
        model_response = await RedisRequestor.ai_vertical_det_calc(params, None)
        Context.report_cost(
            data_json["mission_id"],
            (MissionAlisa.VERTICAL_DET_SERVICE, time.time() - t0),
        )
        new_boxes_list = model_response.get_json_response()
        if len(new_boxes_list) == 0:
            log.warn("竖式计算题没有检测到任何box")
            return

        t0 = time.time()
        futures = []
        for i in range(len(new_boxes_list)):
            params = {
                "img_name": img_name,
                "img_key": img_key,
                "img_type": img_type,
                "new_boxes_list": [new_boxes_list[i]],
            }
            futures.append(RedisRequestor.ai_vertical_rec_calc(params, None))
        result = await asyncio.gather(*futures)
        Context.report_cost(
            data_json["mission_id"],
            (MissionAlisa.VERTICAL_REC_SERVICE, time.time() - t0),
        )
        shushi_boxs = []
        shushi_regs = []
        for resp in result:
            if resp.is_success():
                resp_data = resp.get_json_response()
                shushi_boxs.extend(resp_data["shushi_boxs"])
                shushi_regs.extend(resp_data["shushi_regs"])
        for i in range(len(shushi_regs)):
            try:
                result_i = shushi_judge_item(shushi_regs[i])
                if result_i["flag"] is None:
                    continue
                else:
                    item_quickcal = {
                        "itemType": 28,
                        "coord": [
                            shushi_boxs[i][0] / w,
                            shushi_boxs[i][1] / h,
                            shushi_boxs[i][2] / w,
                            shushi_boxs[i][3] / h,
                        ],
                        "answer": {},
                        "rec": {},
                        "result": 1,
                        "stems": "",
                    }
                    if not result_i["flag"]:
                        item_quickcal["result"] = 0  # 错误
                        item_quickcal["stems"] = result_i["reg_answers"].split("=")[
                            0
                        ]  # 有风险 无法处理有分数的情况
                        item_quickcal["answer"].update(
                            {"value": result_i["std_answers"], "type": 0}
                        )
                        item_quickcal["rec"].update(
                            {"value": shushi_regs[i], "type": 0}
                        )
                    data_json["items"].append(item_quickcal)
            except:
                log.error(
                    f"error in shushier item judge!, due to {traceback.format_exc()}"
                )

    async def _exec_detect_async(self, mission_id, img_key, img_type):
        t0 = time.time()
        data_json = {"img_key": img_key, "img_type": img_type}
        response_json = await RedisRequestor.ai_answer_detect(data_json, None)
        response_json = response_json.get_json_response()
        boxs_list_4p = response_json["boxs_list_4p"]
        boxs_list_2p = response_json["boxs_list_2p"]
        Context.report_cost(
            mission_id, (MissionAlisa.ANSWER_DETECT_SERVICE, time.time() - t0)
        )
        return True, boxs_list_4p, boxs_list_2p

    # 处理拖式计算及解方程  31、32       #
    async def handle_tuoshi_and_fangchen(
        self,
        data_json,
        pred_strs_list,
        pred_boxs_list,
        img_photo_cv2,
        img_key,
        img_type,
        w,
        h,
    ):
        tuoshi_titles = []
        tuoshi_boxs = []
        tuoshi_answers = []

        fangchen_titles = []
        fangchen_boxs = []
        fangchen_answers = []

        for ip, pred_str in enumerate(pred_strs_list):  # 遍历所以检测识别到的题干
            pred_str = pred_str[0]
            if pred_str.find("$") == -1:  # 默认拖式计算和解方程没有手写答案
                if (
                    pred_str.count("+")
                    + pred_str.count("-")
                    + pred_str.count("*")
                    + pred_str.count("/")
                    + pred_str.count(":")
                    >= 2
                ):  # 找拖式计算,运算符不少于两个
                    if pred_str.find("=") == -1:  # 不兼容有=号的情况
                        try:
                            final = judge_item(
                                pred_str.replace(":", "/") + "=$1314520$"
                            )
                        except:
                            final = None

                        if final is not None and final["flag"] == "1":
                            # if not final is None and not final['flag'] is None:
                            answer = ana_result(pred_str, final["std_answers"][0])
                            tuoshi_answers.append(answer)

                            simply_str = pred_str.replace("F", "\\frac")
                            simply_str = simply_str.replace("*", "×")
                            simply_str = simply_str.replace("/", "÷")
                            simply_str = simply_str.replace("W", "^2").replace(
                                "E", "^3"
                            )

                            tuoshi_titles.append(simply_str)

                            tuoshi_boxs.append(pred_boxs_list[ip])
                            continue
                unknowns = ["x", "y", "a", "b"]
                num_unknown = 0
                for unk in unknowns:
                    if pred_str.find(unk) != -1:
                        num_unknown += 1
                        unknown = unk
                if (
                    pred_str.find("=") != -1
                    and num_unknown == 1
                    and pred_str[-1] != "="
                ):  # 找解方程，仅支持未知数为x的情况
                    pred_str = pred_str.replace(unknown, "x")
                    pred_str_tmp = pred_str.replace("W", "^2").replace("E", "^3")
                    pred_str_tmp = (
                        pred_str_tmp.replace("F{", "((")
                        .replace("}{", ")/(")
                        .replace("}", "))")
                    )
                    cnts = pred_str_tmp.split("=")
                    pred_str_tmp = cnts[0] + "-(" + cnts[-1] + ")"

                    new_pred_str_tmp = ""
                    for iis in range(len(pred_str_tmp)):
                        if iis == 0:
                            new_pred_str_tmp += pred_str_tmp[iis]
                            continue
                        if pred_str_tmp[iis] == "x" and (
                            pred_str_tmp[iis - 1] not in ["+", "-", "*", "/", "(", ":"]
                        ):
                            new_pred_str_tmp += "*"
                            new_pred_str_tmp += pred_str_tmp[iis]
                        else:
                            new_pred_str_tmp += pred_str_tmp[iis]

                    # new_pred_str_tmp = new_pred_str_tmp.replace('%','*((1)/(100))')#解决带%的情况的异常
                    while new_pred_str_tmp.find("%") != -1:
                        new_pred_str_tmp = replace_percent(new_pred_str_tmp)

                    source_strs = [
                        "1(",
                        "2(",
                        "3(",
                        "4(",
                        "5(",
                        "6(",
                        "7(",
                        "8(",
                        "9(",
                    ]  # 处理假分数
                    target_strs = [
                        "1+(",
                        "2+(",
                        "3+(",
                        "4+(",
                        "5+(",
                        "6+(",
                        "7+(",
                        "8+(",
                        "9+(",
                    ]
                    for iis in range(len(source_strs)):
                        new_pred_str_tmp = new_pred_str_tmp.replace(
                            source_strs[iis], target_strs[iis]
                        )

                    try:
                        runner = Eval_X(new_pred_str_tmp.replace(":", "/"))
                        ans_float, ans_int = cal_number(runner, -10000000, 10000000)
                    except:
                        continue

                    # 答案格式需要调整：3/44
                    answer = ana_result(pred_str, ans_float)

                    fangchen_answers.append(unknown + "=" + str(answer))
                    fangchen_boxs.append(pred_boxs_list[ip])

                    simply_str = pred_str.replace("F", "\\frac")
                    simply_str = simply_str.replace("*", "×")
                    simply_str = simply_str.replace("/", "÷")
                    simply_str = simply_str.replace("W", "^2").replace("E", "^3")
                    simply_str = simply_str.replace("x", unknown)
                    fangchen_titles.append(simply_str)
                    # 经过后处理的解方程题干和答案 simply_str,answer
                    continue

        if (
            len(tuoshi_titles) + len(fangchen_titles) > 0
        ):  # 当判定有拖式或者解方程的时候再去进行手写文本行的检测
            """
            #考虑缩小手写文本行检测和识别的区域,减少推理时间和资源消耗
            total_box = []
            total_box.extend(tuoshi_boxs)
            total_box.extend(fangchen_boxs)
            total_box = np.array(total_box)
            """

            detect_res, detect_boxs, boxs_list_2p = await self._exec_detect_async(
                data_json["mission_id"], img_key, img_type
            )
            if not detect_res:
                return None
            result_imgs = crop_items(img_photo_cv2, detect_boxs)
            t0 = time.time()
            detect_strs = []
            bsf = Constants.BATCH_SIZE_FORM
            steps = (
                len(result_imgs) // bsf
                if len(result_imgs) % bsf == 0
                else len(result_imgs) // bsf + 1
            )
            for i in range(steps):
                batch_inputs = result_imgs[i * bsf : (i + 1) * bsf]
                batch_imgs = [ImageUtil.numpy_2_base64(inp) for inp in batch_inputs]
                ocr_cn_data = json.dumps({
                    "recordId": data_json["recordId"],
                    "imgs": batch_imgs,
                    "std_answers": []
                })
                model_response = await RedisRequestor.ai_ocr_hand_write_cn(ocr_cn_data, data_json["recordId"])
                if model_response.is_success():
                    detect_strs += model_response.get_response()

            Context.report_cost(
                data_json["mission_id"],
                (MissionAlisa.FORMULA_HW_SERVICE, time.time() - t0),
            )
            for it, box in enumerate(tuoshi_boxs):
                tuoshi_answer = str(tuoshi_answers[it])
                tuoshi_item = tuoshi_titles[it]
                handwrite_strs, big_box = util.get_handwrite(
                    detect_strs, detect_boxs, box, pred_boxs_list, img_photo_cv2
                )  ####计算拖式计算的答案框，并按照Y轴坐标进行排序。同时返回包含题干的大box
                # 拖式计算题及其识别到的用户答案行 tuoshi_item,handwrite_strs
                if handwrite_strs is None:
                    continue

                item_tuoshi = {}
                item_tuoshi["stems"] = tuoshi_item
                item_tuoshi["itemType"] = 31  ###拖式计算题型

                box = np.array(box, dtype=np.int32)
                item_tuoshi["coord"] = [box[0] / w, box[1] / h, box[2] / w, box[3] / h]

                no_answer_flag = True
                if len(handwrite_strs) > 0:
                    for hs in handwrite_strs:
                        if hs != "=" and len(hs) > 0:
                            no_answer_flag = False
                            break

                if no_answer_flag:
                    item_tuoshi["result"] = 3  # 未作答
                    if (tuoshi_item + "=" + tuoshi_answer).find("frac") != -1:  # 未作答
                        item_tuoshi["answer"] = {
                            "value": (tuoshi_item + "=" + tuoshi_answer).replace(
                                "%", "\\%"
                            ),
                            "type": 1,
                        }
                    else:
                        item_tuoshi["answer"] = {
                            "value": tuoshi_item + "=" + tuoshi_answer,
                            "type": 0,
                        }
                    item_tuoshi["rec"] = {}
                    data_json["items"].append(item_tuoshi)
                    continue

                judge_flag = False
                for hs in (
                    handwrite_strs
                ):  # 所有匹配到这个题干的用户作答行中，只要一行是正确答案，就算作答正确
                    if len(hs) == 0:
                        continue
                    if hs.count("=") >= 2:
                        hs = hs[hs.rfind("=") :]
                    if len(hs) > 1 and hs[0] == "2":  # 兼容=写得像2的情况
                        hs = "=" + hs[1:]

                    if len(hs) > 1 and hs[0] == ":":  # 兼容=写得像:的情况
                        hs = "=" + hs[1:]
                    if hs.startswith("\\therefore"):  # 兼容=写得像因为的情况
                        hs = "=" + hs[len("\\therefore") :]
                    if hs.startswith("\\because"):  # 兼容=写得像因为的情况
                        hs = "=" + hs[len("\\because") :]
                    if hs.find("=") == -1:
                        user_answer = hs
                    else:
                        user_answer = hs.split("=")[-1]

                    if len(user_answer) == 0:  # 只写了=号而未作答的情况
                        continue

                    if util.judge_answer_blur(tuoshi_answer, user_answer):
                        item_tuoshi["result"] = 1  # 正确
                        item_tuoshi["answer"] = {}
                        item_tuoshi["rec"] = {}
                        item_tuoshi["coord"] = [
                            big_box[0] / w,
                            big_box[1] / h,
                            big_box[2] / w,
                            big_box[3] / h,
                        ]
                        judge_flag = True
                        break
                if not judge_flag:  # 如果错误，则选取最后一行的用户作答作为识别答案
                    item_tuoshi["result"] = 0  # 错误
                    if (tuoshi_item + "=" + tuoshi_answer).find("frac") != -1:
                        item_tuoshi["answer"] = {
                            "value": (tuoshi_item + "=" + tuoshi_answer).replace(
                                "%", "\\%"
                            ),
                            "type": 1,
                        }
                    else:
                        item_tuoshi["answer"] = {
                            "value": tuoshi_item + "=" + tuoshi_answer,
                            "type": 0,
                        }
                    for hs in handwrite_strs[::-1]:
                        if len(hs) > 0:
                            rec_a = hs
                            break
                    if len(rec_a) > 1 and rec_a[0] == "2":
                        rec_a = "=" + rec_a[1:]
                    if len(rec_a) > 1 and rec_a[0] == ":":
                        rec_a = "=" + rec_a[1:]
                    if rec_a.startswith("\\therefore"):  # 兼容=写得像所以号的情况
                        rec_a = "=" + rec_a[len("\\therefore") :]
                    if rec_a.startswith("\\because"):  # 兼容=写得像因为号的情况
                        rec_a = "=" + rec_a[len("\\because") :]
                    if rec_a.count("=") >= 2:
                        rec_a = rec_a[rec_a.rfind("=") :]

                    if (
                        rec_a is None or len(rec_a) == 0
                    ):  # 如果所有手写识别行都未识别到=号，则认为用户没有作答
                        item_tuoshi["result"] = 3  # 未作答
                        item_tuoshi["rec"] = {}
                        data_json["items"].append(item_tuoshi)
                        continue
                    rec_a = rec_a.replace("\\times", "×").replace("\\div", "÷")
                    rec_a = rec_a.replace("\\%", "%")
                    if (tuoshi_item + rec_a).find("frac") != -1:
                        item_tuoshi["rec"] = {
                            "value": (tuoshi_item + rec_a).replace("%", "\\%"),
                            "type": 1,
                        }
                    else:
                        item_tuoshi["rec"] = {"value": tuoshi_item + rec_a, "type": 0}

                    item_tuoshi["coord"] = [
                        big_box[0] / w,
                        big_box[1] / h,
                        big_box[2] / w,
                        big_box[3] / h,
                    ]
                data_json["items"].append(item_tuoshi)

            for it, box in enumerate(fangchen_boxs):
                fangchen_answer = fangchen_answers[it]  # 答案格式需要进行转换
                fangchen_answer_short = fangchen_answer.split("=")[-1]
                fangchen_item = fangchen_titles[it]
                handwrite_strs, big_box = util.get_handwrite(
                    detect_strs, detect_boxs, box, pred_boxs_list, img_photo_cv2
                )  ####计算解方程的答案框，并按照Y轴坐标进行排序。

                if handwrite_strs is None:
                    continue

                item_fangchen = {}
                item_fangchen["stems"] = fangchen_item
                item_fangchen["itemType"] = 32  ###解方程题型

                box = np.array(box, dtype=np.int32)
                item_fangchen["coord"] = [
                    box[0] / w,
                    box[1] / h,
                    box[2] / w,
                    box[3] / h,
                ]

                no_answer_flag = True
                if len(handwrite_strs) > 0:
                    for hs in handwrite_strs:
                        if hs != "=" and hs != "x=" and len(hs) > 0:
                            no_answer_flag = False
                            break
                if no_answer_flag:
                    item_fangchen["result"] = 3  # 未作答
                    if (fangchen_item + "\\n" + fangchen_answer).find("frac") != -1:
                        item_fangchen["answer"] = {
                            "value": (fangchen_item + "\\n" + fangchen_answer).replace(
                                "%", "\\%"
                            ),
                            "type": 1,
                        }
                    else:
                        item_fangchen["answer"] = {
                            "value": fangchen_item + "\\n" + fangchen_answer,
                            "type": 0,
                        }
                    item_fangchen["rec"] = {}
                    continue

                judge_flag = False
                for hs in (
                    handwrite_strs
                ):  # 所有匹配到这个题干的用户作答行中，只要一行是正确答案，就算作答正确
                    if len(hs) > 2 and hs[:2] == "x2":  # 兼容x=写得像x2的情况
                        hs = "x=" + hs[2:]
                    if len(hs) > 2 and hs[:2] == "x:":  # 兼容=写得像:的情况
                        hs = "x=" + hs[2:]
                    if hs.count("=") >= 2:
                        hs = "x" + hs[hs.rfind("=") :]

                    if hs.find("=") == -1:
                        user_answer = hs
                    else:
                        user_answer = hs.split("=")[-1]
                    if len(user_answer) == 0:  # 只写了=号而未作答的情况
                        continue

                    if util.judge_answer_blur(fangchen_answer_short, user_answer):
                        item_fangchen["result"] = 1  # 正确
                        item_fangchen["answer"] = {}
                        item_fangchen["rec"] = {}
                        item_fangchen["coord"] = [
                            big_box[0] / w,
                            big_box[1] / h,
                            big_box[2] / w,
                            big_box[3] / h,
                        ]
                        judge_flag = True
                        break

                if not judge_flag:
                    item_fangchen["result"] = 0  # 错误
                    if (fangchen_item + "\\n" + fangchen_answer).find("frac") != -1:
                        item_fangchen["answer"] = {
                            "value": (fangchen_item + "\\n" + fangchen_answer).replace(
                                "%", "\\%"
                            ),
                            "type": 1,
                        }
                    else:
                        item_fangchen["answer"] = {
                            "value": fangchen_item + "\\n" + fangchen_answer,
                            "type": 0,
                        }

                    for hs in handwrite_strs[::-1]:
                        if len(hs) > 0:
                            rec_a = hs
                            break
                    if (
                        rec_a is None or len(rec_a) == 0
                    ):  # 如果未识别到手写行，则认为用户没有作答
                        item_fangchen["result"] = 3  # 未作答
                        item_fangchen["rec"] = {}
                        data_json["items"].append(item_fangchen)
                        continue

                    rec_a = rec_a.replace("\\times", "×").replace("\\div", "÷")

                    if len(rec_a) > 2 and rec_a[:2] == "x2":  # 兼容x=写得像x2的情况
                        rec_a = "x=" + rec_a[2:]
                    if len(rec_a) > 2 and rec_a[:2] == "x:":
                        rec_a = "x=" + rec_a[2:]
                    if rec_a.count("=") >= 2:
                        rec_a = "x" + rec_a[rec_a.rfind("=") :]

                    if (fangchen_item + "\\n" + rec_a).find("frac") != -1:
                        item_fangchen["rec"] = {
                            "value": (fangchen_item + "\\n" + rec_a).replace(
                                "%", "\\%"
                            ),
                            "type": 1,
                        }
                    else:
                        item_fangchen["rec"] = {
                            "value": fangchen_item + "\\n" + rec_a,
                            "type": 0,
                        }

                    item_fangchen["coord"] = [
                        big_box[0] / w,
                        big_box[1] / h,
                        big_box[2] / w,
                        big_box[3] / h,
                    ]

                data_json["items"].append(item_fangchen)

        return None
