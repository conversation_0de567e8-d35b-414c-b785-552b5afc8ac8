# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 10:10 
# @Description  : data_of_correction_api_request.py


import json
from base_common import BaseData


class CorrectRequest(BaseData):
    def __init__(self, book_id=None, env=None, image_url=None, record_id=None,
                 coord=None, biz_type=None, is_origin_img=True, is_new_correct=None,
                 correction=True, user_id=None, path=None, encrypt_path=None, task_create=None):
        self._book_id = book_id
        self._env = env
        self._image_url = image_url
        self._record_id = record_id
        self._coord = coord
        self._biz_type = biz_type
        self._is_origin_img = is_origin_img
        self._is_new_correct = is_new_correct
        self._correction = correction
        self._user_id = user_id
        self._path = path
        self._encrypt_path = encrypt_path
        self._mission_id = None
        self._user_image = None
        self._img_base64 = None
        self._user_image_key = None
        self._align_image = True
        self._task_create = task_create
        self._current_thread = None
        self._total_thread = None
        self._escapeCalcAlgorithmJudge = None

    def set_escapeCalcAlgorithmJudge(self, escapeCalcAlgorithmJudge):
        self._escapeCalcAlgorithmJudge = escapeCalcAlgorithmJudge
        return self
    def get_escapeCalcAlgorithmJudge(self):
        return self._escapeCalcAlgorithmJudge
    def set_align_image(self, align_image=True):
        self._align_image = align_image
        return self
    def is_align_image(self):
        return self._align_image
    def set_mission_id(self, mission_id):
        self._mission_id = mission_id
        return self
    def get_mission_id(self): return self._mission_id
    def set_task_create(self, task_create):
        self._task_create = task_create
        return self
    def get_task_create(self): return self._task_create
    def set_book_id(self, book_id=None):
        self._book_id = book_id
        return self

    def get_book_id(self): return self._book_id

    def set_env(self, env=None):
        self._env = env
        return self

    def get_env(self): return self._env

    def set_image_url(self, image_url=None):
        self._image_url = image_url
        return self
    def set_user_image(self, user_image=None):
        self._user_image = user_image
        return self
    def set_img_base64(self, img_base64=None):
        self._img_base64 = img_base64
        return self
    def get_img_base64(self): return self._img_base64
    def get_user_image(self): return self._user_image
    def set_user_image_key(self, user_image_key=None):
        self._user_image_key = user_image_key
        return self
    def get_user_image_key(self): return self._user_image_key
    def get_image_url(self): return self._image_url

    def set_record_id(self, record_id=None):
        self._record_id = record_id
        return self

    def get_record_id(self): return self._record_id

    def set_coord(self, coord=None):
        self._coord = coord
        return self

    def get_coord(self): return self._coord

    def set_biz_type(self, biz_type=None):
        self._biz_type = biz_type
        return self

    def get_biz_type(self): return self._biz_type

    def set_is_origin_img(self, is_origin_img=True):
        self._is_origin_img = is_origin_img
        return self

    def is_origin_img(self): return self._is_origin_img

    def set_is_new_correct(self, is_new_correct=None):
        self._is_new_correct = is_new_correct
        return self

    def is_new_correct(self): return self._is_new_correct

    def set_correction(self, correction=True):
        self._correction = correction
        return self

    def is_correction(self): return self._correction

    def set_user_id(self, user_id=None):
        self._user_id = user_id
        if user_id == 'null' or user_id == '' or user_id == 'None' or user_id is None:
            self._user_id = None
        return self

    def get_user_id(self): return self._user_id

    def set_path(self, path=None):
        self._path = path
        return self

    def get_path(self): return self._path

    def set_encrypt_path(self, encrypt_path=None):
        self._encrypt_path = encrypt_path
        return self

    def get_encrypt_path(self): return self._encrypt_path

    def is_product(self) -> bool:
        return self._env == 'product'

    def is_prepub(self) -> bool:
        return self._env == 'prepub'

    def is_daily(self) -> bool:
        return self._env == 'daily'

    @classmethod
    def fromDict(cls, json_data: dict):
        crd = CorrectRequest()
        crd.set_book_id(json_data.get('bookId', None))
        crd.set_env(json_data.get('env', None))
        crd.set_image_url(json_data.get('imageUrl', None))
        crd.set_record_id(json_data.get('recordId', None))
        crd.set_coord(json_data.get('coord', None))
        crd.set_biz_type(json_data.get('bizType', None))
        crd.set_is_origin_img(json_data.get('isOriginImg', True))
        crd.set_is_new_correct(json_data.get("isNewCorrect", True))
        crd.set_correction(json_data.get('correct', True))
        crd.set_user_id(json_data.get('userId', None))
        crd.set_path(json_data.get('path', None))
        crd.set_encrypt_path(json_data.get('encryptPath', None))
        crd.set_task_create(json_data.get('create_date', None))
        crd.set_current_thread(json_data.get('current_thread', None))
        crd.set_total_thread(json_data.get('total_thread', None))
        crd.set_align_image(json_data.get('alignImage', True))
        crd.set_escapeCalcAlgorithmJudge(json_data.get('escapeCalcAlgorithmJudge', None))
        if not crd.get_image_url():
            crd.set_image_url(json_data.get('originImageUrl', None))
        return crd

    def to_json(self):
        resp = {
            "recordId": self._record_id,
            "env": self._env,
            "bookId": self._book_id,
            "imageUrl": self._image_url,
            "coord": self._coord,
            "bizType": self._biz_type,
            "isOriginImg": self._is_origin_img,
            "isNewCorrect": self._is_new_correct,
            "correct": self._correction,
            "userId": self._user_id,
            "path": self._path,
            "task_create": self._task_create,
            "total_thread": self._total_thread,
            "current_thread": self._current_thread,
            "encryptPath": self._encrypt_path,
            "escapeCalcAlgorithmJudge": self._escapeCalcAlgorithmJudge
        }
        resp = {k: v for k, v in resp.items() if v is not None}
        return resp

    def to_string(self, ensure_ascii=False) -> str:
        resp = self.to_json()
        return json.dumps(resp, ensure_ascii=False)

    def set_current_thread(self, current_thread):
        self._current_thread = current_thread

    def set_total_thread(self, total_thread):
        self._total_thread = total_thread
    def get_current_thread(self):
        return self._current_thread

    def get_total_thread(self):
        return self._total_thread
