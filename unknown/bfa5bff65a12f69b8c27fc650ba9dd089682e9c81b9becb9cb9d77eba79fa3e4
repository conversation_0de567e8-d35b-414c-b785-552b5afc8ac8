# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/8 12:01 
# @Description  : data_of_detection_request.py
import json
from base_common import BaseData


class DirectionRequest(BaseData):

    def __init__(self, the_input=None):
        self._the_input = the_input

    def set_the_input(self, the_input=None):
        self._the_input = the_input
        return self
    def get_the_input(self):
        return self._the_input

    def to_json(self):
        return json.dumps({'the_input': self._the_input})