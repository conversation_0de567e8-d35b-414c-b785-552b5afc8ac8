# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/8 12:01 
# @Description  : data_of_detection_request.py
import json
from base_common import BaseData


class DetectionRequest(BaseData):

    def __init__(self, the_input=None, mat_like=None, img_shape=None):
        self._the_input = the_input
        self._mat_like = mat_like
        self._img_shape = img_shape

    def set_the_input(self, the_input=None):
        self._the_input = the_input
        return self
    def get_the_input(self):
        return self._the_input
    def set_mat_like(self, mat_like=None):
        self._mat_like = mat_like
        return self
    def get_mat_like(self):
        return self._mat_like
    def set_img_shape(self, img_shape=None):
        self._img_shape = img_shape
        return self
    def get_img_shape(self):
        return self._img_shape
    def to_json(self):
        return json.dumps({'the_input': self._the_input, 'IM': self._mat_like, 'img_shape': self._img_shape})