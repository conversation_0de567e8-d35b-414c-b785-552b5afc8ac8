# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/7/1 17:53 
# @Description  : service_of_formula.py
import cv2
import base64
import numpy as np

from base_common import Constants, MissionMode, LoggerFactory
from base_common.service.service_of_base import BaseModelService
from formula_hw_ocr_service.model import can_predict
print('ocr text model Loaded!')
log = LoggerFactory.get_logger('FormulaService')
def base64_to_cv2(base64_code):
    img_data = base64.b64decode(base64_code)
    img_array = np.fromstring(img_data, np.uint8)
    img = cv2.imdecode(img_array, cv2.COLOR_RGB2BGR)
    return img


class FormulaService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.FORMULAR_HW_OCR_MISSION
        model_path = f"{Constants.MODEL_WEIGHT_PATH}/formula_hw_ocr_service"
        self.ocr_predictor = can_predict.CanPredictor(model_path)

    def do_post(self, data_json):
        final_result = {'flag': 1000, 'result': []}
        imgs = [base64_to_cv2(bb) for bb in data_json['imgs']]
        pred_txts = self.ocr_predictor.predict(imgs)
        final_result['result'] = pred_txts
        return final_result