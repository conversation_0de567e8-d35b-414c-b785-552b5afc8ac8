#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 测试 decimal_part 计算逻辑
result = 0.57

print(f"result = {result}")
print(f"result * 10 = {result * 10}")
print(f"int(result * 10) = {int(result * 10)}")
print(f"result * 100 = {result * 100}")
print(f"(result * 100) % 10 = {(result * 100) % 10}")
print(f"int((result * 100) % 10) = {int((result * 100) % 10)}")

# 测试不同的 result 值
test_values = [0.57, 0.56, 0.58, 0.567, 0.566, 0.568]

num_to_chinese = {
    1: "一",
    2: "二",
    3: "三",
    4: "四",
    5: "五",
    6: "六",
    7: "七",
    8: "八",
    9: "九",
    10: "十",
}

for val in test_values:
    integer_part = int(val * 10)
    decimal_part = int((val * 100) % 10)
    integer_chinese = num_to_chinese.get(integer_part, "")
    decimal_chinese = num_to_chinese.get(decimal_part, "")
    print(f"val={val}, integer_part={integer_part}({integer_chinese}), decimal_part={decimal_part}({decimal_chinese})")
