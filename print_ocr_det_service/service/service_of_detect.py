# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_vertical_calculation.py

import base64
import time

import cv2
import numpy as np
import traceback

from base_common import LoggerFactory, Constants, MissionMode
from base_common.service.service_of_base import BaseModelService
from print_ocr_det_service.model.Pytorch_model import Pytorch_model
log = LoggerFactory.get_logger('PrintOcrDetectService')

def b64_to_img(image_b64):
    imageBin = base64.b64decode(image_b64)
    img1 = np.frombuffer(imageBin, np.uint8)
    image = cv2.imdecode(img1, cv2.IMREAD_ANYCOLOR)
    try:
        cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    except:
        image -= image.min()
        image = image / (image.max() - image.min())
        image *= 255
        image = image.astype(np.uint8)
    return image

class PrintOcrDetectService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.PRINT_OCR_DET_MISSION
        mode_path = f'{Constants.MODEL_WEIGHT_PATH}/print_ocr_det_service/model_best.pth'
        self._model = Pytorch_model(mode_path, post_p_thre=0.3, gpu_id=0)

    def do_post(self, data_json):
        t0 = time.time()
        try:
            image = self.get_image(data_json)
            try:
                cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            except:
                image -= image.min()
                image = image / (image.max() - image.min())
                image *= 255
                image = image.astype(np.uint8)

            preds, boxes_list, score_list, t = self._model.predict(image, is_output_polygon=False)
            #log.info(f"模型整体耗时：{time.time() - t0:.4f}")
            res_boxes_list = [box for box, score in zip(boxes_list, score_list) if score >= 0.4]
            if res_boxes_list is None:
                log.error("未检测到内容")
                return {"result": []}
            return {"result": [box.tolist() for box in res_boxes_list]}
        except:
            log.error(f"{traceback.format_exc()}")
            return {"result": []}