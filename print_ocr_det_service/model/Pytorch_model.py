# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/7/22 8:31 
# @Description  : Pytorch_model.py

import time
import cv2
import torch

from base_common import LoggerFactory
from .data_loader import get_transforms
from .models import build_model
from .post_processing import get_post_processing

replace_chars = {'＞':'>','＜':'<','＝':'=','＋':'+','－':'-'}
remove_chars = ['(',')','=','{','}','[',']','+','%']
log = LoggerFactory.get_logger('Pytorch_model')
def resize_image(img, short_size):
    height, width, _ = img.shape
    if height < width:
        new_height = short_size
        new_width = new_height / height * width
    else:
        new_width = short_size
        new_height = new_width / width * height
    new_height = int(round(new_height / 32) * 32)
    new_width = int(round(new_width / 32) * 32)
    resized_img = cv2.resize(img, (new_width, new_height))
    return resized_img

class Pytorch_model:
    def __init__(self, model_path, post_p_thre=0.7, gpu_id=None):
        self.gpu_id = gpu_id
        
        self.device = torch.device("cuda")
        checkpoint = torch.load(model_path, map_location=self.device)

        config = checkpoint['config']
        config['arch']['backbone']['pretrained'] = False
        self.model = build_model(config['arch'])
        self.post_process = get_post_processing(config['post_processing'])
        self.post_process.box_thresh = post_p_thre
        self.img_mode = config['dataset']['train']['dataset']['args']['img_mode']
        self.model.load_state_dict(checkpoint['state_dict'])
        self.model.to(self.device)
        self.model.eval()

        self.transform = []
        for t in config['dataset']['train']['dataset']['args']['transforms']:
            if t['type'] in ['ToTensor', 'Normalize']:
                self.transform.append(t)
        self.transform = get_transforms(self.transform)

    # 不实用img_path
    def predict(self, img, is_output_polygon=False, short_size: int = 640):
        t0 = time.time()
        with torch.no_grad():
            if self.img_mode == 'RGB':
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            # h, w = img.shape[:2]

            original_h, original_w = img.shape[:2]

            # 判断是否需要填充
            pad = 0
            do_pad = False
            if original_h < 200:
                pad = original_w - original_h
                img = cv2.copyMakeBorder(img, 0, pad, 0, 0, cv2.BORDER_CONSTANT, value=(255, 255, 255))
                do_pad = True

            padded_h, padded_w = img.shape[:2]

            # 如果没有填充，则进行 resize
            if not do_pad:
                img = resize_image(img, short_size)
                resized_h, resized_w = img.shape[:2]
            else:
                resized_h, resized_w = padded_h, padded_w  # 保持填充后的大小
            # img = resize_image(img, short_size)


            # 将图片由(w,h)变为(1,img_channel,h,w)
            tensor = self.transform(img)
            tensor = tensor.unsqueeze_(0)
            #log.info(f"transform耗时：{time.time() - t0:.4f}")
            t0 = time.time()
            tensor = tensor.to(self.device)
            # batch = {'shape': [(h, w)]}

            # 记录形状信息以便后处理时映射坐标
            batch = {
                'original_shape': (original_h, original_w),
                'padded_shape': (padded_h, padded_w),
                'resized_shape': (resized_h, resized_w),
                'pad': pad,
                'do_pad': do_pad
            }

            with torch.no_grad():
                start = time.time()
                preds = self.model(tensor)
                #log.info(f"model耗时：{time.time() - t0:.4f}")
                t0 = time.time()
                box_list, score_list = self.post_process(batch, preds, is_output_polygon=is_output_polygon)

                #log.info(f"post_process耗时：{time.time() - t0:.4f}")
                t0 = time.time()
                box_list, score_list = box_list[0], score_list[0]
                if len(box_list) > 0:
                    if is_output_polygon:
                        idx = [x.sum() > 0 for x in box_list]
                        box_list = [box_list[i] for i, v in enumerate(idx) if v]
                        score_list = [score_list[i] for i, v in enumerate(idx) if v]
                    else:
                        idx = box_list.reshape(box_list.shape[0], -1).sum(axis=1) > 0  # 去掉全为0的框
                        box_list, score_list = box_list[idx], score_list[idx]
                else:
                    box_list, score_list = [], []
                t = time.time() - start
                #log.info(f"after_handler耗时：{time.time() - t0:.4f}")
            # print("检测模型释放内存")
            return preds[0, 0, :, :].detach().cpu().numpy(), box_list, score_list, t

