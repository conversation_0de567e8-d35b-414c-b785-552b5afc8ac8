# 代码仓库

- 仓库地址 https://git.bookln.cn/ai/correction_runtime.git 

# 项目结构
| 服务                       | 描述       | 负责人     |
|--------------------------|----------|---------|
| answer_detect_service    | 答案检测模型   | 小羊 
| formula_hw_ocr_service   | 公式手写识别模型 | 小羊
| line_calc_service        | 连线题模型    | 小羊
| quick_calc_service       | 智能口算模型   | 小羊
| graphics_calc_service    | 作图题模型    | 小羊
| vertical_calc_service    | 竖式计算模型   | 小羊
| ch_hw_ocr_service        | 中文手写识别模型 | 小羊
| en_hw_ocr_service        | 英文手写识别模型 | 张飞
| splg_service             | 图像对齐模型   | 张飞
| direction_service        | 旋转模型     | 昭阳
| detection_service        | 框图模型     | 昭阳
| print_ocr_detect_service | 印刷体检测模型  | 路舟
| print_ocr_rec_service    | 印刷体识别模型  | 路舟
| embedding_service        | 图像特征模型   | 路舟
| correction_service       | 拍照批改主程序  | 雁回
| listener_service         | 监控程序     | 雁回
| quick_correction_service | 智能批改主程序  | 翰林

# 启动方式
1. 所有服务均通过shell脚本启动，在服务所在目录执行bash scripts/server.sh restart|start|stop分别表示重启，启动，关闭
2. 服务启动时会从base_common/scripts/get_sys_conf.sh获取conda环境，系统环境EnvType，节点环境NodeType， 若需修改环境，请先修改此shell脚本
3. 脚本中以/usr/servering/correction_runtime为PYTHONPATH，开发过程中配置work directory也是此目录，eg：D:/code/correction_runtime
4. 本地开发时，将base_common/constants/constants.py中的WORK_BASE_PATH为自己的路径，否则将会使用环境变量的PYTHONPATH

# 注意事项
1. 各个服务若需新增可视化结果，使用base_common/constants/constants.py中的SAVE_VIS作为开关，保存根目录为VIS_PATH，子目录自行创建；需自行管理，最好在重启服务时自行清除。
2. 各服务调用若需要传递图片，图片路径根目录为：MNT_TEMP_PATH
3. 监控服务启动时，会根据conf/server_config.json中的配置进行监控，若enable置为false，则不会监控，监控规则是调用/alive接口，返回200表示成功，否则认为失败，这里可以修改一下，将监听接口更改为服务接口，参数为真实数据，后续支持。
4. 开发时请在192.168.2.9上进行测试，在/usr/serving/correction_runtime下有所有服务。测试单个服务时可先将该位置的服务停止，再从自己本地或/home下自己的目录中启动对应的服务即可调试


# 目录结构
  /usr/servering/correction_runtime

  ├── base_common 拍照批改项目的基础模块

  │   ├── config 系统配置

  │   ├── constants 系统常量配置

  │   ├── controller 这里仅将alive路由放在此目录下，其他路由放在对应服务目录下

  │   ├── dto 提取的系统数据传输对象

  │   ├── enums 系统枚举

  │   ├── scripts 脚本文件

  │   ├── util 系统工具类
  
  ├── answer_det_service  答案检测

  │   ├── controller http接口

  │   ├── scripts 脚本文件

  │   ├── model 原项目的answer_det_service/dbnet下所有文件 为检测模型

  │   ├── service 业务代码
  
  ├── detection_service  抠图

  │   ├── controller http接口

  │   ├── scripts 脚本文件

  │   ├── service 业务代码
  
  ├── direction_service  旋转

  │   ├── controller http接口

  │   ├── scripts 脚本文件

  │   ├── service 业务代码

  │   ├── ultralytics1 模型代码
  
  ├── correction_service  主程序

  │   ├── answer_detect 答案检测过程中需要使用的中间数据抽象
  
  │   ├── common 主程序中公共模块抽离
  
  │   ├── controller http接口
  
  │   ├── data 主程序中的所有数据抽象
  
  │   ├── pool 主程序中的服务对象池

  │   ├── scripts 脚本文件

  │   ├── service 业务代码

  │   ├── util 工具类
  
  ├── formula_hw_ocr_service  公式手写识别

  │   ├── controller http接口

  │   ├── model 模型代码 原项目can_formul_hw下所有代码

  │   ├── scripts 脚本文件

  │   ├── service 业务代码  
  
  ├── graphics_calc_service  作图题模型

  │   ├── controller http接口

  │   ├── model 模型代码 原项目的answer_det_service/paint_item_yolo4下所有文件

  │   ├── scripts 脚本文件

  │   ├── service 业务代码

  ├── line_calc_service  作图题模型

  │   ├── controller http接口

  │   ├── model 模型代码 原项目的answer_det_service/line_interface下所有文件

  │   ├── scripts 脚本文件

  │   ├── service 业务代码

  ├── quick_calc_service  作图题模型

  │   ├── controller http接口

  │   ├── model 模型代码 原项目的answer_det_service/quickcal下所有文件

  │   ├── scripts 脚本文件

  │   ├── service 业务代码

  ├── vertical_calc_service  作图题模型

  │   ├── controller http接口

  │   ├── model 模型代码 原项目的answer_det_service/shushijisuan下所有文件

  │   ├── scripts 脚本文件

  │   ├── service 业务代码

  ├── quick_correction_service  智能批改主程序

  │   ├── controller http接口

  │   ├── scripts 脚本文件

  │   ├── service 业务代码

其中答案检查服务中拆分出来的服务代码，web接口定义和业务代码已经转移到controller和service
  
# 拍照批改端口矩阵
### <font color="red">注意：待定表示未进行统一重构！</font>
| 端口   | 路由             | 用途      | 所属服务                     |
|------|----------------|---------|--------------------------|
| 8886 | /correct       | 拍照批改主程序 | correction_service       |
| 8889 | /AnswerDetect  | 答案检测    | answer_det_service       |
| 8860 | /line_calc     | 连线题     | line_calc_service        |
| 8861 | /quick_calc    | 智能口算    | quick_calc_service       |
| 8862 | /vertical_calc | 竖式计算    | vertical_calc_service    |
| 8863 | /graphics_calc | 作图题     | graphics_calc_service    |
| 8887 | /OcrFormular   | 公式手写识别  | formula_det_service      |
| 8883 | /detection     | 抠图      | detection_service        |
| 8884 | /direction     | 旋转      | direction_service        |
| 8876 | /auto_correct  | 智能批改主程序 | quick_correction_service |
| 8882 | /embeding2     | 图像特征    | embedding_service        |
| 8885 | /detect        | 印刷体检测   | print_ocr_detect_service |
| 8890 | /rec           | 印刷体识别   | print_ocr_rec_service    |
| 8891 | /rec           | 印刷体识别   | print_ocr_rec_service    |
| 8892 | /rec           | 印刷体识别   | print_ocr_rec_service    | 
| 8893 | /rec           | 印刷体识别   | print_ocr_rec_service    |
| 6666 | /english       | 英文手写识别  | en_hw_ocr_service        | 
| 8880 | /ocrtext       | 中文手写识别  | ch_hw_ocr_service        |

所有服务均添加了检活路由，具体实现在base_common/controller/controller_of_alive.py

# 版本v2.0.2 2024-8-2
1. 智能口算业务更新
2. 拉飞问题更新


