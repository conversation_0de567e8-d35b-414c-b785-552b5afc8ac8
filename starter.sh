#!/bin/bash

if mount | grep -q "1c62c4b2b3-hgp62.cn-beijing.nas.aliyuncs.com"; then
  echo "NAS 已经挂载，无需重新挂载。"
else
  echo "重新挂载nas..."
  sudo umount /mnt
  sudo mount -t nfs -o vers=3,nolock,proto=tcp,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport 1c62c4b2b3-hgp62.cn-beijing.nas.aliyuncs.com:/ /mnt
fi

df -h
echo "授权脚本..."
find /usr/servering/correction_runtime -name "*.sh" -exec chmod +x {} \;

BASE_PATH=/usr/servering/correction_runtime
CORRECTION_PATH=$BASE_PATH/correction_service
QUICK_CORRECTION_PATH=$BASE_PATH/quick_correction_service
ANSWER_DETECT_PATH=$BASE_PATH/answer_det_service
DETECTION_PATH=$BASE_PATH/detection_service
DIRECTION_PATH=$BASE_PATH/direction_service
FORMULA_PATH=$BASE_PATH/formula_hw_ocr_service
EMBEDDING_PATH=$BASE_PATH/embedding_service
CH_PATH=$BASE_PATH/ch_hw_ocr_service
EN_PATH=$BASE_PATH/en_hw_ocr_service
QUICK_DET_PATH=$BASE_PATH/quick_calc_det_service
QUICK_REC_PATH=$BASE_PATH/quick_calc_rec_service
GRAPHICS_PATH=$BASE_PATH/graphics_calc_service
VERTICAL_PATH=$BASE_PATH/vertical_calc_service
VERTICAL_DET_PATH=$BASE_PATH/vertical_calc_det_service
VERTICAL_REC_PATH=$BASE_PATH/vertical_calc_rec_service
LINE_PATH=$BASE_PATH/line_calc_service
PRINT_DET_PATH=$BASE_PATH/print_ocr_det_service
PRINT_REC_PATH=$BASE_PATH/print_ocr_rec_service
SURF_PATH=$BASE_PATH/surf_service
ZONE_ALIGN_PATH=$BASE_PATH/zone_align_service

echo "清除缓存文件..."
cd ~/pythonlogs
rm -rf ./*.correction_runtime.std
rm -rf ./*.correction_runtime.print
cd ~

start_cpu() {
  cd "$CORRECTION_PATH"
  bash scripts/server.sh
}
start_task() {
  cd "$CORRECTION_PATH"
  bash scripts/task.sh
}
start_quick_cpu() {
  cd "$QUICK_CORRECTION_PATH"
  bash scripts/server.sh
}
start_quick_task() {
  cd "$QUICK_CORRECTION_PATH"
  bash scripts/task.sh
}
start_search() {
  cd "$CORRECTION_PATH"
  bash scripts/server_search.sh
}
start_search_task() {
  cd "$CORRECTION_PATH"
  bash scripts/search_task.sh
}
start() {
  local service_dir="$1"
  local gpu_num=$2
  cd "$service_dir"
  echo "Current dir: $(pwd) Start service on gpu $gpu_num"
  bash scripts/mission.sh start $gpu_num
}

echo "切换到工作目录"
cd /usr/servering/correction_runtime
pwd
echo "停止服务..."
bash stop.sh

##cd /usr/servering/correction_runtime && bash stop.sh && rm -rf product.zip && cp /mnt/product.zip ./ && unzip -o product.zip && chmod +x /usr/servering/correction_runtime/base_common/scripts/get_sys_conf.sh && bash starter.sh gpu
## cd /usr/servering/Monitor && bash bin/server.sh stop && rm -rf Monitor.zip classes lib temp/* && cp /mnt/Monitor.zip ./ && unzip -o Monitor.zip && bash bin/server.sh start
echo "清空运行零时文件..."
find /usr/servering/correction_runtime -type f -path '*/pids/*.pid' -exec rm -f {} +
find /usr/servering/correction_runtime -type f -path '*/scripts/*.pid' -exec rm -f {} +
find /usr/servering/correction_runtime -type f -path '*/scripts/2024*.sh' -exec rm -f {} +
find /usr/servering/correction_runtime -type d -name '__pycache__' -exec rm -rf {} +

echo "清空日志文件..."
rm -rf ~/pythonlogs/*
echo "授权脚本..."
find /usr/servering/correction_runtime -name "*.sh" -exec chmod +x {} \;

case "$1" in
    pre0)
      start "$ANSWER_DETECT_PATH" 0
      start "$GRAPHICS_PATH" 0
      start "$LINE_PATH" 0
      start "$PRINT_REC_PATH" 0
      start "$PRINT_DET_PATH" 0
      start "$QUICK_REC_PATH" 0
      start "$ZONE_ALIGN_PATH" 0
      start "$SURF_PATH" 0

      start "$CH_PATH" 1
      start "$EN_PATH" 1
      start "$QUICK_DET_PATH" 1
      start "$VERTICAL_DET_PATH" 1
      start "$VERTICAL_REC_PATH" 1
      start "$DETECTION_PATH" 1
      start "$EMBEDDING_PATH" 1

      start "$DIRECTION_PATH" 0
      start_cpu
      start_task
      start_quick_cpu
      start_quick_task
    ;;
    gpu1)
      start "$ANSWER_DETECT_PATH" 0
      start "$PRINT_REC_PATH" 0
      start "$CH_PATH" 0
      start "$CH_PATH" 0
      start "$QUICK_REC_PATH" 0
      start "$QUICK_REC_PATH" 0
      #start "$FORMULA_PATH" 0
      start "$DETECTION_PATH" 0
      start "$EN_PATH" 0
      start "$VERTICAL_REC_PATH" 0

      start "$DIRECTION_PATH" 0
	    start "$SURF_PATH" 0

      start_quick_cpu
      start_quick_task

      start_search
      start_search_task
    ;;
    gpu2)
      start "$PRINT_REC_PATH" 0
      start "$CH_PATH" 0
      start "$CH_PATH" 0
      start "$LINE_PATH" 0
      start "$QUICK_REC_PATH" 0
      start "$QUICK_REC_PATH" 0
      start "$VERTICAL_DET_PATH" 0
      start "$VERTICAL_REC_PATH" 0
      start "$ZONE_ALIGN_PATH" 0

      start_cpu
      start_task
    ;;
    gpu3)
      start "$PRINT_REC_PATH" 0
      start "$PRINT_REC_PATH" 0
      start "$QUICK_DET_PATH" 0
      start "$DETECTION_PATH" 0
      start "$EMBEDDING_PATH" 0
      start "$GRAPHICS_PATH" 0
      start "$PRINT_DET_PATH" 0
      start "$VERTICAL_REC_PATH" 0
      start "$ZONE_ALIGN_PATH" 0

      start "$DIRECTION_PATH" 0

      start_quick_cpu
      start_quick_task
    ;;
    gpu4)
      start "$PRINT_REC_PATH" 0
      start "$PRINT_REC_PATH" 0
      start "$CH_PATH" 0
      start "$CH_PATH" 0
      start "$QUICK_DET_PATH" 0
      start "$QUICK_REC_PATH" 0
      start "$QUICK_REC_PATH" 0
      start "$ANSWER_DETECT_PATH" 0
      start "$EMBEDDING_PATH" 0
	    start "$SURF_PATH" 0

      start_cpu
      start_task
    ;;
esac

ps -ef|grep python
exit 0