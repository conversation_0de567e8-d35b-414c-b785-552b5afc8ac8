# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 16:29 
# @Description  : server_detection.py
import sys

from base_common import Context, LoggerFactory, Constants, ParamLoader
from direction_service.service.service_of_direction import DirectionService
log = LoggerFactory.get_logger('DirectionService')
if __name__ == '__main__':
    args = sys.argv[1:]
    params = ParamLoader(args)
    if not params.is_success():
        log.error('Usage: python xx.py <port> <gpu_num>')
        exit(1)

    Context.setup()
    Constants.setup(Context.is_product())
    Context.set_gpu_num("-1")
    service = DirectionService()
    service.set_use_ai_lab(True)
    service.set_stop_time(params.get_stop_time())
    service.start(params.get_port(), True)
