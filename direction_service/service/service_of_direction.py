# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 16:17 
# @Description  : service_of_detection.py
import json
import traceback

import cv2
import torch
import numpy as np
from statistics import mode

from base_common import LoggerFactory, Constants, ImageUtil, MissionMode
from base_common.service.service_of_base import BaseModelService
from direction_service.model.nn.autobackend import AutoBackend
log = LoggerFactory.get_logger('DirectionService')

class DirectionService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.DIRECTION_MISSION
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/direction_service/best.pt'
        self.direction_fn = AutoBackend(weights=model_path)
        log.info('Model loaded and warmed up successfully!')
        log.info('模型加载并预热完成！')
        self.__empty_resp = {'img_key': None, 'img_type': None, 'angle': "-1"}
    def internal_do_direction(self, img):
        imgs = self.crop_img(image=img)
        inputs = np.stack([ImageUtil.preprocess_batch(elem) for elem in imgs], axis=0).astype(np.float32)

        # 转换为PyTorch张量
        inputs_tensor = torch.from_numpy(inputs)
        out = self.direction_fn(inputs_tensor)
        out_numpy = out.numpy()
        angle = mode(out_numpy.argmax(axis=1))

        if angle == 1:
            img = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
        elif angle == 2:
            img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
        elif angle == 3:
            img = cv2.rotate(img, cv2.ROTATE_180)
        return img, angle

    def do_post(self, data_json):
        try:
            img_key = data_json['img_key']
            image = self.get_image(data_json)
            img, angle = self.internal_do_direction(image)
            if angle == 0:
                return {'img_key': img_key, 'img_type': data_json['img_type'], 'angle': "0"}
            img_key, img_type = self.set_image(img)
            return {'img_key': img_key, 'img_type': img_type, 'angle': str(angle)}
        except:
            log.error(f'Error in processing: {traceback.format_exc()}')
            return self.__empty_resp
    def crop_img(self, image, target_size=(224, 224), overlap_ratio=0.1, crop_percentage=0.1,threshold=128):
        """
        裁剪并旋转图像。
        参数：
        - image：输入图像
        - target_size：目标尺寸（默认为（224, 224））
        - overlap_ratio：相邻裁剪区域之间的重叠比例（默认为0.1）
        - crop_percentage：裁剪百分比（默认为0.2）
        返回：
        - crops：裁剪和旋转后的图像列表
        """

        cropped_image = image[
                        int(crop_percentage * image.shape[0]):int(-crop_percentage * image.shape[0]),
                        int(crop_percentage * image.shape[1]):int(-crop_percentage * image.shape[1]),
                        :]

        height, width = cropped_image.shape[0], cropped_image.shape[1]

        min_edge = min(height, width)
        if min_edge < target_size[0]:
            # 计算缩放比例，确保最小边至少为224+1
            scale_factor = (224 + 2) / min_edge
            cropped_image = cv2.resize(cropped_image, None, fx=scale_factor, fy=scale_factor)

        overlap_pixel_x = int(target_size[1] * (1 - overlap_ratio))
        overlap_pixel_y = int(target_size[0] * (1 - overlap_ratio))

        crops = []
        # 裁剪左边
        left_crop = cv2.resize(cropped_image[:, :width - overlap_pixel_x, :], target_size)
        crops.append(left_crop)
        # 裁剪右边
        right_crop = cv2.resize(cropped_image[:, overlap_pixel_x:, :], target_size)
        crops.append(right_crop)
        # 裁剪上边
        top_crop = cv2.resize(cropped_image[:height - overlap_pixel_y, :, :], target_size)
        crops.append(top_crop)
        # 裁剪下边
        bottom_crop = cv2.resize(cropped_image[overlap_pixel_y:, :, :], target_size)
        crops.append(bottom_crop)
        # 裁剪中心区域
        center_crop = cv2.resize(cropped_image, target_size)
        crops.append(center_crop)

        # imgs = np.stack(crops, axis=0)
        # return imgs

        # 对剩下的四张图像进行二值化处理
        binary_images = [cv2.threshold(crop, threshold, 255, cv2.THRESH_BINARY)[1] for crop in crops[:4]]

        # 计算黑色像素数量
        black_pixel_counts = [np.sum(binary_image == 0) for binary_image in binary_images]

        # 保留黑色像素数量最多的两张
        top_two_indices = np.argsort(black_pixel_counts)[-2:]
        selected_images = [crops[i] for i in top_two_indices]

        imgs = np.stack([center_crop] + selected_images, axis=0)
        return imgs
