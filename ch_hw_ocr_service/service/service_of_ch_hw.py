# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_vertical_calculation.py
import traceback

from PIL import Image

from base_common import ImageUtil, Constants, MissionMode, LoggerFactory
from base_common.auto_setting import AutoSetting
from base_common.service.service_of_base import BaseModelService
from ch_hw_ocr_service.model import trocr_predict

one_piece_ch_ocr_size = 6
log = LoggerFactory.get_logger('ChHwService')
class ChHwService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.CH_HW_OCR_MISSION
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/ch_hw_ocr_service/'
        self.ocr_predictor = trocr_predict.Trocr_Predictor(model_path)
        self.update_batch_size()
    def update_batch_size(self):
        global one_piece_ch_ocr_size
        try:
            config = AutoSetting.get_config()
            one_piece_ch_ocr_size = config['one_piece_ch_ocr_size']
        except:
            one_piece_ch_ocr_size = 6
    def do_post(self, data_json):
        """
        data的格式：
        data:{
        'imgs':[base64(img),...]
        'std_answers':['答案',...] #标准答案可能为''
        'modes':[0,] #包括了选择，判断，等特殊题型的mode，用来限制识别结果的范围。
        }
        """
        self.update_batch_size()
        final_result = {'flag': 1000, 'result': []}
        try:
            imgs = [Image.fromarray(ImageUtil.base64_2_numpy(bb)) for bb in data_json['imgs']]
            std_answers = data_json['std_answers']
            if len(imgs) != len(std_answers):
                log.info('len(imgs) != len(std_answers)')
                std_answers = [''] * len(imgs)
        except:
            log.error(f'error in download data {traceback.format_exc()}')
            final_result['flag'] = 1001  # 输入数据异常
            return final_result

        pred_txts = [''] * len(imgs)
        batch_labeled_imgs = []
        batch_labeled_imgs_label = []
        batch_labeled_imgs_ind = []
        batch_imgs = []
        batch_imgs_ind = []

        for i, img in enumerate(imgs):
            #img.save(f'/home/<USER>')
            if len(std_answers[i]) != 0:
                batch_labeled_imgs.append(img)
                batch_labeled_imgs_label.append(std_answers[i])
                batch_labeled_imgs_ind.append(i)

                if len(batch_labeled_imgs) == one_piece_ch_ocr_size:
                    batch_pred_txts = self.ocr_predictor.predict(batch_labeled_imgs, batch_labeled_imgs_label)
                    for jj, ind in enumerate(batch_labeled_imgs_ind):
                        pred_txts[ind] = batch_pred_txts[jj]

                    batch_labeled_imgs = []
                    batch_labeled_imgs_label = []
                    batch_labeled_imgs_ind = []
            else:
                batch_imgs.append(img)
                batch_imgs_ind.append(i)

                if len(batch_imgs) == one_piece_ch_ocr_size:
                    batch_pred_txts = self.ocr_predictor.predict(batch_imgs)
                    for jj, ind in enumerate(batch_imgs_ind):
                        pred_txts[ind] = batch_pred_txts[jj]

                    batch_imgs = []
                    batch_imgs_ind = []

        if len(batch_labeled_imgs) != 0:
            batch_pred_txts = self.ocr_predictor.predict(batch_labeled_imgs, batch_labeled_imgs_label)
            for jj, ind in enumerate(batch_labeled_imgs_ind):
                pred_txts[ind] = batch_pred_txts[jj]

        if len(batch_imgs) != 0:
            batch_pred_txts = self.ocr_predictor.predict(batch_imgs)
            for jj, ind in enumerate(batch_imgs_ind):
                pred_txts[ind] = batch_pred_txts[jj]

        final_result['result'] = pred_txts  # 输入数据异常
        return final_result
