from base_common import Constants,LoggerFactory
log = LoggerFactory.get_logger('hw_ocr_blur_match')
sym_chars = []
def str_unification(string):
    ### 字符串格式统一
    
    #字符统一成英文字符，替换后识别结果和推荐答案都会替换
    replace_dict = {'％':'%','（':'(','）':')','！':'!','＋':'+','﹣':'-','，':',','－':'-','：':':','；':';','＜':'<','＝':'=','＞':'>','？':'?','＿':'_','｛':'{','｝':'}','｜':'|','６':'6','８':'8','９':'9','～':'~','＇':'\'','￥':'¥','″':'\"','。':'。','、':'、','゜':'ᵒ','⁰':'ᵒ','⁰':'ᵒ','✓':'√','⭐':'☆','―':'–','—':'–','×':'x','✕':'x','一':'-','•':'·','∶':':','丶':'、'} 
    for k in replace_dict.keys():
        string = string.replace(k,replace_dict[k])
        
    # 统一余数符号表达
    # … . ·    
    count = 0
    new_str = ''
    tmp_str = ''
    for c in string:
        if c == '.':
            count += 1
            tmp_str += '.'
        elif c == '·':
            count += 1
            tmp_str += '·'
        elif c == '、':
            count += 1
            tmp_str += '、'
        elif c == '…':
            count += 3
            tmp_str += '…'
        else:
            if count > 2:
                new_str += '……'
            else:
                new_str += tmp_str
            new_str += c
            count = 0
            tmp_str = ''
    
    if count > 2:
        new_str += '……'
    else:
        new_str += tmp_str
    string = new_str    
    
    if string.count('0') > 2:
        string = string.replace('o','0').replace('O','0')
    
    return string

def simple_exp(item):
    final_str = ''
    exp  = False
    start = False
    count = 0
    for i in range(len(item)):
        if item[i] == '^':
            if i == len(item)-1:
                return final_str
            elif item[i+1] != '{':
                continue
            else:
                exp = True
        elif item[i] == '{':
            if exp:
                if start:
                    final_str += item[i]
                    count += 1
                else:
                    start = True
            else:
                final_str += item[i]
        elif item[i] == '}':
            if exp:
                if start:
                    if count > 0:
                        final_str += item[i]
                        count -= 1
                    else:
                        start = False
                        exp = False
                else:
                    final_str += item[i]
            else:
                final_str += item[i]
        else:
            final_str += item[i]
        
    return final_str           
        
  
def judge_strs(str1,str2):
    #log.info(f'str1: {str1}, str2: {str2}')
    # str1 标准答案字符串
    # str2 预测字符串
    if str1 == str2:
        return True
    # 只是替换后用于对比，如果不一致不会改变识别结果
    # "①": "1", "②": "2", "③": "3", "④": "4", "⑤": "5", "⑥": "6", "⑦": "7", "⑧": "8", "⑨": "9", 
    tmp_replace_dict = {
        "[": "(", "∠": "<",
        "]": ")", "十": "+", "ɑ": "a", "Ƅ": "b", "ɛ": "e",
        "ɡ": "g",' ':'',
        "ɪ": "i", "ɵ": "o", "ρ": "p",
        "χ": "x", 
        "干": "千", "亳": "毫", "口十": "叶", "曰": "日", "隹": "佳", "已": "巳",
        "汆": "氽", "王": "壬",
        "二": "=", "＝": "=", "2": "z","±":"土","士":"土",
        'c':'C','K':'k','O':'o','P':'p','S':'s','V':'v','W':'w','X':'x','Z':'z','U':'u','V':'v','丫':'y','Y':'y','M':'m',
        '²':'2','³':'3','了':'3','∞':'00',
        '一':'-','、':','
    }

    number_replace_dict = {
        "①": "1", "②": "2","③": "3", "④": "4", "⑤": "5", "⑥": "6", "⑦": "7", "⑧": "8", "⑨": "9", "⑩": "10", "⑪": "11", "⑫": "12", "⑬": "13", "⑭": "14", "⑮": "15", "⑯": "16", "⑰": "17"
    }
    
    for k in number_replace_dict.keys():
        str2_ = str2.replace(k,number_replace_dict[k])
    if str1 == str2_:
        return True
    for k in tmp_replace_dict.keys():
        str1 = str1.replace(k,tmp_replace_dict[k])
        str2 = str2.replace(k,tmp_replace_dict[k])
    if str1 == str2:
        return True
        
    ### 部分标点符号的替换
    replace_dict = {'、':'.',',':'.','。':'.'}
    if len(str1) > 1:#答案长度大于1默认不是填写标点符号的题，可以进行替换
        for k in replace_dict.keys():
            str1 = str1.replace(k,replace_dict[k])
            str2 = str2.replace(k,replace_dict[k])
    if str1 == str2:
        return True
        
    # 针对答案为ABCDEFTF的旋转题型，进行适当的后处理，提高批改准确率
    # print('%%%%%%%%%%%%%%%%: ',str1,str2)
    if str1 in ['A','B','C','D','E','F','G','T']:
        str2 = str2.replace(' ','').replace(',','').replace('(','').replace(')','').replace('.','')
        ### D写得像0的情况
        if str1 == 'D':
            flag = False
            for st in str2:
                if st in ['D','0','o','O','P','口','p','又','①','中','6']:
                    flag = True
                    continue
                if st in ['A','B','C','E','F','G','T']:
                    flag = False
                    break
            return flag
            
        ### B写得像13等情况
        if str1 == 'B':
            flag = False
            for st in str2:
                if st in ['B','8','3','阝']:
                    flag = True
                    continue
                if st in ['A','C','D','E','F','G','T']:
                    flag = False
                    break
            return flag
            
        ### A写得像4等情况
        if str1 == 'A':
            flag = False
            for st in str2:
                if st in ['A','4','人']:
                    flag = True
                    continue
                if st in ['B','C','D','E','F','G','T']:
                    flag = False
                    break
            return flag

        if str1 == 'C':
            flag = False
            for st in str2:
                if st in ['C','e','∠','c','<']:
                    flag = True
                    continue
                if st in ['A','B','D','E','F','G','T']:
                    flag = False
                    break
            return flag
            
        if str1 == 'E':
            flag = False
            for st in str2:
                if st in ['E','日']:
                    flag = True
                    continue
                if st in ['A','B','C','D','F','G','T']:
                    flag = False
                    break
            return flag
            
        if str1 == 'F':
            flag = False
            for st in str2:
                if st in ['F']:
                    flag = True
                    continue
                if st in ['A','B','C','D','E','G','T']:
                    flag = False
                    break
            return flag
            
        if str1 == 'G':
            flag = False
            for st in str2:
                if st in ['G']:
                    flag = True
                    continue
                if st in ['A','B','C','D','F','E','T']:
                    flag = False
                    break
            return flag
            
        if str1 == 'T':
            flag = False
            for st in str2:
                if st in ['T']:
                    flag = True
                    continue
                if st in ['A','B','C','D','F','G','E']:
                    flag = False
                    break
            return flag
              
    # 针对多选题，标准答案用@@@进行拼装
    if str1.find('@@@') != -1:
        # print('#########: ',str1,str2)
        #log.info(f'str1: {str1}; str2:{str2}')
        str1_replace_dict = {'a':'A','b':'B','c':'C','d':'D'}
        for k in str1_replace_dict.keys():
            str1 = str1.replace(k,str1_replace_dict[k])
        
        str2 = str2.replace(' ','').replace(',','').replace('(','').replace(')','').replace('.','').replace('.','')
        str2 = str2.replace('4','A').replace('13','B').replace('8','B').replace('阝','B').replace('3','B').replace('c','C').replace('<','C').replace('∠','C').replace('O','D').replace('口','D')
        #if len(str1.replace('@@@','')) != len(str2):
        #    return False
        judge_flag = True
        cnts = str1.split('@@@')
        for ss in cnts:
            if str2.find(ss) == -1:
                judge_flag = False
                
        for ch in ['A','B','C','D','E','F','G','H','I']:
            if not ch in cnts and str2.find(ch) != -1:
                judge_flag = False

        return judge_flag
            
    ### ^{a}替换成a
    if str1.find('^') != -1 or str2.find('^') != -1:
        str1 = simple_exp(str1)
        str2 = simple_exp(str2)
    if str1 == str2:
        return True
    
    ### 识别出的文本比gt多或少一个标点符号
    # 处理识别结果前后多出一个印刷字符，remove_chars中的字符都是有概率被误识别的印刷体，可以根据实际情况增减。
    # 只有当remove_chars中的字符在首位或者末尾时候才会删除
    remove_chars = ['(',')','=','{','}','[',']','+','%','-','*','×','÷','>','<',':','\'',',','.','、','m','。',]
    if len(str2)>1:
        pred_text_copy = str2+''
        if_stop = False
        for char in remove_chars:
            if pred_text_copy.startswith(char):
                if pred_text_copy[1:] == str1:
                    return True
            if pred_text_copy.endswith(char):
                if pred_text_copy[:-1] == str1:
                    return True
    if len(str2)>4 and len(str1)>3 and (len(str2)-len(str1))==1:
        if str2.startswith(str1):
            return True
        if str2.endswith(str1) and not str2[0] in ['-','不','非']:
            return True

    return str1 == str2

def init(file_path):
    with open(file_path, 'r', encoding='utf-8') as ff:
        lines = ff.readlines()
    for line in lines:
        sym_chars.append(line.strip())
        
def judge_pred(label_loss_mean,pred_loss_mean,lbl,pred_text='、'):
    #if abs(len(lbl) - len(pred_text)) > 2:
    #    return False
    if label_loss_mean > 4:
        return False
        
    #当标准答案全是汉字且长度小于等于4的时候严格限制，其他时候放宽模糊匹配
    strict_flag = True
    if len(lbl) > 4:
        strict_flag = False
        
    for c in lbl:
        if c in sym_chars:
            strict_flag = False
            break
    if lbl in ['A','B','C','D','F','E','T','G','×','√']:
        strict_flag = True
        
    if not strict_flag:
        if label_loss_mean < 0.5:
            return True
        #return label_loss_mean/pred_loss_mean < 120.
        return pred_loss_mean > label_loss_mean**2/40

    else:
        if label_loss_mean < 0.1:
            return True
        #return label_loss_mean/pred_loss_mean < 20.
        return pred_loss_mean > label_loss_mean**2/10