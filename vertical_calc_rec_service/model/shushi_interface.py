
import numpy as np
from vertical_calc_rec_service.model.CAN import can_predict
from base_common import LoggerFactory, BoxUtil

log = LoggerFactory.get_logger('VerticalCalculationModel')

class Shushi_Auto:
    def __init__(self, model_path):
        self.can_predictor = can_predict.CanPredictor(model_path)

    def run(self, img, bb_, new_boxes_list):
        (bb_0,bb_1) = bb_
        items = BoxUtil.crop_items(img, new_boxes_list)

        boxs = []
        answers = []
        item_futures = []
        for item_idx, item in enumerate(items):
            item_img = item['img']
            if item_img is None:
                continue
            try:
                pred_strs_l = self.can_predictor.predict(item_img.astype(np.float32))
                item_futures.append((item, pred_strs_l))
            except:
                log.info("error in can predict predict")
                continue

        for item, item_result in item_futures:
            poly_box = item['box']
            xmin = poly_box[:, 0].min() + bb_0
            xmax = poly_box[:, 0].max() + bb_0
            ymin = poly_box[:, 1].min() + bb_1
            ymax = poly_box[:, 1].max() + bb_1
            boxs.append([int(xmin), int(ymin), int(xmax), int(ymax)])
            answers.append(item_result[0][0])
        return boxs, answers
