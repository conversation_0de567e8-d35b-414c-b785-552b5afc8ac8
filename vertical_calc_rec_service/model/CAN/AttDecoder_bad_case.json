{"mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_9.jpg": {"label": "1 - 6 - 4 4", "predi": "1 - - 4 4"}, "mask_93550ba1-e370-11ed-b62a-271c54bc3310_35.jpg": {"label": "0 . 9 0 4 ≈ 0 . 9", "predi": "0 . 9 0 4 ≈ $ 0 . 9 $"}, "mask_78052551-ce1d-11ed-9651-a9da729490b9_1.jpg": {"label": "0 4 + 8 % 4", "predi": "0 0 0"}, "mask_e4f569e1-e880-11ed-8240-c907d23284e5_25.jpg": {"label": "4 9 万 - 2 7 万 = $ 2 2 万 $", "predi": "4 9 万 - 2 7 万 = $ 2 2 万"}, "mask_301765d1-db80-11ed-9b86-95a6e77f6b03_23.jpg": {"label": "3 0 + 4 - 3 6", "predi": "3 0 + 4 - 3"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_35.jpg": {"label": "9 - 9 $ = $ 0 1", "predi": "7 - 9 $ = $ 0 1"}, "mask_c7183621-c8b2-11ed-8c68-d1b4066848bf_24.jpg": {"label": "F { 3 } { 2 } = F { 3 }", "predi": "F { 3 } { 2 } = F { 3 } { 3 2 }"}, "mask_8f82bca1-ea70-11ed-8497-558fa19ab074_11.jpg": {"label": "8 . 1 + 1 . 6 = $ 9 . 7 $", "predi": "5 . 1 + 1 . 6 = $ 9 . 7 $"}, "mask_bf758eb1-e89f-11ed-b47a-177cc8d1e3e2_5.jpg": {"label": "F { 1 } { 2 } : F { 3 } { 4 } = $ F { 2 } { 3 } $", "predi": "F { 1 } { 2 } / F { 3 } { 4 } = $ F { 2 } { 3 } $"}, "mask_45c90931-c71f-11ed-8247-99d7d34504d3_29.jpg": {"label": "5 8 5 / 6 = $ 1 1 7 $", "predi": "5 8 5 / 5 = $ 1 1 7 $"}, "mask_bde08451-cae6-11ed-980c-1bb4d4d27233_13.jpg": {"label": "F { 3 } { 4 } - F { 4 } { 1 5 } =", "predi": "F { 3 } { 4 } * F { 4 } { 1 5 } ="}, "mask_90f3ddd1-c329-11ed-a6c3-5781877ada00_35.jpg": {"label": "1 0 0 $ > $ 4 1", "predi": "1 0 0 $ > $ 9 1"}, "mask_90f3ddd1-c329-11ed-a6c3-5781877ada00_17.jpg": {"label": "1 2 - 3 $ > $ 1 7 - 1", "predi": "1 2 - 3 $ > $ 1 7 - 0"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_23.jpg": {"label": "8 - 1 - 2", "predi": "8 - - 2"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_8.jpg": {"label": "9 1 = 6 + 1 5", "predi": "9 1 = 6 + 1 6"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_32.jpg": {"label": "1 4 + 分", "predi": "1 1 + 4 +"}, "mask_9e321fa1-cede-11ed-85a4-cf1cd11915cb_35.jpg": {"label": "6 * 6 = $ 6 6 $", "predi": "6 * b = $ 6 6 $"}, "mask_ff04d611-dd07-11ed-8d37-831e7084deb4_12.jpg": {"label": "6 + 6 0 = $ 6 9 $", "predi": "9 + 6 0 = $ 6 9 $"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_4.jpg": {"label": "八 成 八 = ( $ 8 8 $ ) %", "predi": "公 成 顷 = ( $ 8 8 $ ) %"}, "mask_d69e4070-deac-11ed-af3b-d19d1131ae48_13.jpg": {"label": "0 0 0 0 : 4 = $ 5 0 0 $ :", "predi": "0 0 0 0 : 4 = $ 5 0 0 = 0 : $"}, "mask_1e0459b1-d9ab-11ed-ba6b-87e3de31db02_44.jpg": {"label": "5 4 / 9 = $ 0 . 6 $", "predi": "5 . 4 / 9 = $ 0 . 6 $"}, "mask_fc41afd1-cf50-11ed-9483-6f8242279988_0.jpg": {"label": "$ 1 6 $ $ - $ $ 8 $ = $ 8 $ ( 只 )", "predi": "$ 1 6 $ - $ 8 $ = $ 8 $ ( 顷 )"}, "mask_3b471c30-d78d-11ed-a82a-f5347e681596_0.jpg": {"label": "F { 2 } { 5 } = ( $ 2 $ ) / ( $ 5 $ ) = ( $ 0 . 4 $", "predi": "F { 2 } { 5 } = ( $ 2 $ ) / ( $ 5 $ ) = ( $ 0 . 4 3 $ )"}, "mask_d60505c1-d47c-11ed-ad0c-bbd87a87a925_21.jpg": {"label": "1 ( 1 ) 1", "predi": "1 - ( $ 1 $ ) = 0"}, "mask_1eac3931-d9fd-11ed-aae3-47e61ed0b4f9_40.jpg": {"label": "F { 2 1 ] { 2 } = $ 9 F { 3 } { 2 } $", "predi": "F { 2 1 } { 2 } = $ 9 F { 3 } { 2 } $"}, "mask_e16c4231-d6b0-11ed-811c-c911f1b35dfe_18.jpg": {"label": "1 9 / 9 = $ 2 P 1 $", "predi": "1 9 / 9 = $ 2 - 1 $"}, "mask_ee7b0411-e41c-11ed-9f1c-4f4ff0cd46ea_31.jpg": {"label": "3 5 / 4 = $ 8 P 3 $", "predi": "3 5 / 4 = 8 P 3"}, "mask_e781d991-c8a1-11ed-9d0a-95ec6217db79_29.jpg": {"label": "6 c m : 3 6 m = $ 1 : 6 0 0 $", "predi": "6 c m : 3 6 m = $ 1 : 6 0 $"}, "mask_f0cbac31-e1ea-11ed-a240-3fcbf3237676_11.jpg": {"label": "5 6 吨 + 2 0 0 0 吨 = ( $ 2 0 0 5 $ ) 吨", "predi": "5 吨 + 2 0 0 0 吨 = ( $ 2 0 0 5 $ ) 吨"}, "mask_de11f8f1-c3ff-11ed-b594-775abc41496b_1.jpg": {"label": "6 0 6 * 6 = $ 1 0 0 P 5 $", "predi": "6 0 5 / 6 = $ 1 0 0 P 5 $"}, "mask_f97f2601-bf4d-11ed-910d-79fca93155ec_54.jpg": {"label": "8 8 / 2 =", "predi": "8 8 / 2"}, "mask_5bb635d1-e25c-11ed-be6f-4faa061d2a84_44.jpg": {"label": "7 0 0 0 / 1 0 0 0 = $ 7 $", "predi": "7 0 0 0 / / 0 0 0 = $ 7 $"}, "mask_de7f6a11-dd14-11ed-9870-8d8b3ed7121b_28.jpg": {"label": "5 6 0 * 4 =", "predi": "5 6 0 * 4 0 ="}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_25.jpg": {"label": "F { 2 } { 9 } - F { 1 } { 5 } + F { 7 } { 9 } = $ F { 2 } { 9 } + F { 7 } { 9 } - F { 1 } { 5 } = 1 - F { 1 } { 5 } = F { 5 } { 5 } - F { 1 } { 5 } = F { 5 - 1 } { 5 } = F { 4 } { 5 } $", "predi": "F { 2 } { 9 } - F { 1 } { 5 } + F { 7 } { 9 } = $ F { 2 } { 9 } + F { 7 } { 9 } - F { 1 } { 5 } = 1 - F { 1 } { 5 } = F { 5 } { 5 } - 5 } { 5 } { 5 } { 5 } { F { 4 } { 5 } $"}, "mask_1ab1d2f1-cf9d-11ed-b0e2-15cbdd81d5fa_43.jpg": {"label": "5 . 4 亿 = ( $ 5 4 0 0 0 0 0 0 $", "predi": "5 . 4 亿 = ( $ 5 4 0 0 0 0 0 0 $ )"}, "mask_f1e133e1-d873-11ed-83f3-63712f2f1b3e_15.jpg": {"label": "F { 3 } { 1 0 } = F { 3 * ( $ 8 $ ) } { 1 0 * ( $ 8 $ ) } = F { ( $ 2 4 $ ) } { ( $ 8 0 $ }", "predi": "F { 3 } { 1 0 } = F { 3 * ( $ 8 $ ) } { 1 0 * ( $ 8 $ ) } = F { ( $ 2 4 $ ) } { ( $ 8 0 $ ) }"}, "mask_4e1b5001-ed9b-11ed-aeeb-1747dd124717_2.jpg": {"label": "3 9 . 6 5 % ≈ ( $ 3 9 . 7 $ )", "predi": "3 9 . 6 5 % ≈ ( $ 3 9 . 7 $"}, "mask_3db13d11-db6c-11ed-a160-21a90d504099_9.jpg": {"label": "3 2 角 = ( $ 3 $ ) = ( $ 2 $ ) 角", "predi": "3 2 角 = ( $ 3 $ ) 元 ( $ 2 $ ) 角"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_32.jpg": {"label": "1 1 $ 1 1 1 ( 1 $ ) 1", "predi": "1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_dc684d70-e726-11ed-9acb-cd2e592f90ec_26.jpg": {"label": "1 + 2 + 6 = $ 1 $", "predi": "1 + 2 + 6 ="}, "mask_1acbca51-c62b-11ed-a003-2fa78111c925_29.jpg": {"label": "( $ 7 7 0 $ } + 4 3 0 = 1 2 0 0", "predi": "( $ 7 7 0 $ ) + 4 3 0 = 1 2 0 0"}, "mask_eb522101-efe9-11ed-90ac-9fc756c44b48_9.jpg": {"label": "$ F { 6 } { 4 0 } $ F { 3 } { 2 0 } + F { 1 3 } { 4 0 } = $ F { 1 9 } { 4 0 } $", "predi": "$ F { 6 } { 4 } $ F { 3 } { 2 0 } + F { 1 3 } { 4 0 } = $ F { 1 9 } { 4 0 } $"}, "mask_3fa101b1-d201-11ed-84a1-456fdeaec262_20.jpg": {"label": "F { 4 } { ( $ 3 $ ) }", "predi": "F { ( $ F { 7 } { 3 } $"}, "mask_2c4ea711-e41d-11ed-8fa4-8bdfce412ea8_12.jpg": {"label": "七 成 五 = ( $ 7 5 $ ) %", "predi": "成 成 五 = ( $ 7 5 $ ) %"}, "mask_d9cff8c1-d7a3-11ed-b32a-f3c9db460797_4.jpg": {"label": "F { 1 } { 4 } = F { 9 } { $ 3 6 $ ) }", "predi": "F { 1 } { 4 } = F { 9 } { ( $ 3 6 $ ) }"}, "mask_fee728a1-d095-11ed-a34e-810c1e9c0e59_36.jpg": {"label": "七 成 = ( $ 7 0 $ ) %", "predi": "成 成 = ( $ 7 0 $ ) %"}, "mask_ecd7a021-ceda-11ed-85a4-cf1cd11915cb_1.jpg": {"label": "1 1 * 1 1 = $ 1 2 1 $", "predi": "1 1 * 1 1 = 1 2 1"}, "mask_f2584351-d47b-11ed-9a47-8b6f33680ffa_1.jpg": {"label": "5 . 4 * F { 2 } { 9 } = $ F { 1 0 . 8 } { 9 } $", "predi": "5 . 4 * F { 2 } { 9 } = $ F { 1 0 0 } { 9 } $"}, "mask_f87a0b51-d9d6-11ed-be74-518ade31ec1c_40.jpg": {"label": "6 + 6 = $ 1 2 $", "predi": "$ 6 + 6 = 1 2 $"}, "mask_2d149b01-e2b0-11ed-8772-5705ff086af7_26.jpg": {"label": "0 . 6 5 + F { 1 } { 2 0 } = $ F { 6 5 } { 1 0 0 } + F { 1 } { 2 0 } = F { 6 5 } { 1 0 0 } + F { 5 } { 1 0 0 } = F { 7 0 } { 1 0 0 } $", "predi": "0 . 6 5 + F { 1 } { 2 0 } = $ F { 6 5 } { 1 0 0 } + F { 1 } { 2 0 } = F { 6 5 } { 1 0 0 } + F { 5 } { 1 0 0 } = F { 7 0 } { 1 0 0 } $ F { 7 } { } $"}, "mask_db1614a1-d864-11ed-8917-4fe416191213_1.jpg": {"label": "七 分 七 五", "predi": "4 4 4"}, "mask_f3e20851-db65-11ed-a6a9-13feeea71b50_23.jpg": {"label": "4 角 + 1 元 4 角 = ( $ 1 $ ) 元 ( $ 8 $ ) ) 角", "predi": "4 角 + 1 元 4 角 = ( $ 1 $ ) 元 ( $ 8 $ ) 角"}, "mask_fee728a1-d095-11ed-a34e-810c1e9c0e59_30.jpg": {"label": "六 成 五 = ( $ 6 5 $ ) %", "predi": "公 成 五 = ( $ 6 5 $ ) %"}, "mask_d69e4070-deac-11ed-af3b-d19d1131ae48_0.jpg": {"label": "3 3 0 0 0 0", "predi": "( $ 7 0 0 0 0 $ )"}, "mask_eca0bf21-d6d1-11ed-a57c-5ba9d58fc7c9_43.jpg": {"label": "7 . 6 m ^ 3 = ( $ 7 6 0 $ ) d m ^ 3", "predi": "7 . 6 ^ 3 = ( $ 7 6 0 $ ) d m ^ 3"}, "mask_eff79761-d9a7-11ed-9628-eb2ffcdf8662_26.jpg": {"label": "3 . 6 c m : 1 8 0 k m = 1 : ( $ 5 0 0 0 0 0 0 $ )", "predi": "3 . 6 c m : 1 8 0 k m = 1 : ( $ 5 0 0 0 0 0 0 $"}, "mask_f97e3961-e983-11ed-bac1-db1612922de1_20.jpg": {"label": "( $ 3 5 $ ) / 4 = 8 P ( $ 3 $ )", "predi": "( $ 3 5 $ ) / 4 = 8 P ( $ 3 $"}, "mask_4efc0101-ef25-11ed-b03d-b94246ddbbe9_19.jpg": {"label": "F { 1 } { 2 } + F { 1 } { 6 } = $ F { 3 } { 6 } + F { 1 } { 6 } = F { 4 } { 6 } = F { 2 } { 3 } $", "predi": "F { 1 } { 2 } + F { 1 } { 6 } = $ F { 3 } { 6 } = F { 1 } { 6 } F { 1 } { 6 } = F { 2 } { 3 } $"}, "mask_eb9fdb40-c549-11ed-9024-a164bd547d9c_25.jpg": {"label": "7 2 0 / 5 =", "predi": "7 2 0 / 5 = $ 1 4 4 $"}, "mask_fb197ba1-df37-11ed-a794-0bd03935def7_21.jpg": {"label": "1 0 / 4 =", "predi": "1 0 / 4 = ("}, "mask_1ed80ff1-ea6b-11ed-a99e-7315b94357fa_43.jpg": {"label": "F { 1 } { 8 } * 1 2 . 5 * 6 4 = $ 1 0 0 . 9 $", "predi": "F { 1 } { 8 } * 1 2 . 5 * 6 4 = $ 1 0 0 9 9 $"}, "mask_fd2ec491-d6c7-11ed-8d26-95957dea9f25_24.jpg": {"label": "2 4 / 7 = $ 3 P 3 $", "predi": "2 4 / 7 = $ 3 - 3 $"}, "mask_db1e1371-bfc4-11ed-a01c-a1500e978aba_31.jpg": {"label": "F { 7 } { 2 } - F { 3 } { 5 } = 2 F { 9 } { 1 0 }", "predi": "F { 7 } { 2 } - F { 3 } { 5 } = $ 2 F { 9 } { 1 0 } $"}, "mask_f458db21-d092-11ed-a1a3-737af6466f3d_44.jpg": {"label": "7 2 / 0 P P P 1 1", "predi": "7 2 / 0 P P 1 1"}, "mask_fb8fa401-c8a4-11ed-84d9-0df2c3af046c_5.jpg": {"label": "5 1 = ( ) * ( )", "predi": "5 1 = ( ) * ( $ 7 $ )"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_34.jpg": {"label": "5 L 5 2 分", "predi": "7 5 5 = ( $ 6 0 $ ) 分"}, "mask_dc8c1b41-effc-11ed-aaee-151279633bcd_28.jpg": {"label": "5 9 角 = ( $ 5 $ ) 元 ( $ 9 $ )", "predi": "5 9 角 = ( $ 5 $ ) 元 ( $ 7 $ )"}, "mask_ed4ceaa0-cc7e-11ed-919b-31322e3a9138_6.jpg": {"label": "3 7 = $ 7 2 $", "predi": "- 3 7 = $ 7 2 $"}, "mask_e15b1fc1-d532-11ed-8bc3-dd4ef0baf717_37.jpg": {"label": "1 3 - 4 - 5 = $ 3 $", "predi": "1 3 - 4 - 6 = $ 3 $"}, "mask_4bde2e70-e41d-11ed-b190-a701d8346fd0_10.jpg": {"label": "3 . 6 万 = ( $ 3 6 0 0 0 $ )", "predi": "3 . 6 万 = ( $ 3 6 0 0 0 $"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_31.jpg": {"label": "1 1 1 1 1 1 1 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_6dd2f001-dd0c-11ed-9d84-d32c0689dfde_4.jpg": {"label": "5 . 4 亿 = ( $ 5 4 0 0 0 0 0 0 0 $ )", "predi": "5 . 4 亿 = ( $ 5 4 0 0 0 0 0 0 0 $"}, "mask_de7f6a11-dd14-11ed-9870-8d8b3ed7121b_17.jpg": {"label": "5 0 0 * 1 8", "predi": "5 0 0 * 1 8 -"}, "mask_e49851c1-cdd1-11ed-897e-7f3c0f6048c5_0.jpg": {"label": "2 6 % x = $ 5 . 2 $", "predi": "2 6 % x = 5 . 2"}, "mask_ff3307a1-c250-11ed-8390-15967ebdcb8c_14.jpg": {"label": "4 分 米 米 2 分 一 ( $ 4 0 2 $ )", "predi": "4 分 千 米 2 分 - ( $ 4 0 2 $ )"}, "mask_eac228a0-c06b-11ed-b897-1dfb019eb6db_1.jpg": {"label": "1 6 - 9 $ < $ 1 5 - 6", "predi": "1 6 - 9 $ < $ 1 5 - ("}, "mask_6a471ab1-ce25-11ed-ae5e-bbbe8c893dff_5.jpg": {"label": "F 0 7 $ > $", "predi": "F { 1 4 - 7 = $ 7 $"}, "mask_ff04d611-dd07-11ed-8d37-831e7084deb4_3.jpg": {"label": "3 7 - 7 = $ 3 0 $", "predi": "1 7 - 7 = $ 3 0 $"}, "mask_3aeb1791-ecd8-11ed-aa76-cfb33f5867bd_22.jpg": {"label": "1 9 + 3 8 = $ 5 5 $", "predi": "1 9 + 3 6 = $ 5 5 $"}, "mask_df14f8d1-d92e-11ed-b22c-ff1fa9c01a70_45.jpg": {"label": "2 . m 4", "predi": "2 . m $"}, "mask_0ee4f121-ecc9-11ed-9b55-ebf3e747b1fb_19.jpg": {"label": "3 5 - 8 = $ 2 7 $", "predi": "3 5 - 8 = $ 7 7 $"}, "mask_de1d7a81-ee58-11ed-bd3d-e16639461d2c_45.jpg": {"label": "2 . 角 - 折 0", "predi": "2 . 角 - 5 0"}, "mask_01c51cf1-ef2f-11ed-b5bd-93c1850cfbbf_44.jpg": {"label": "F { 2 } { 3 } $ > $ F { 1 } { { 3 }", "predi": "F { 2 } { 3 } $ > $ F { 1 } { 3 }"}, "mask_5d4fcda1-efba-11ed-828b-296aa3f01d24_3.jpg": {"label": "6 8 0 0 - ( $ 6 4 0 0 $ ) = 4 0 0 0", "predi": "6 8 0 0 - ( $ 6 4 0 $ ) = 4 0 0 0"}, "mask_4f410611-ee71-11ed-addf-8d31ef84c420_36.jpg": {"label": "5 0 8 = $ 5 0 0 $", "predi": "5 0 8 ≈ $ 5 0 0 $"}, "mask_ed20acc1-e1c2-11ed-a05a-470731b22b9f_33.jpg": {"label": "1 7 0 - 9 0 = $ 8 0 $", "predi": "1 7 0 - 9 0 = $ 8 $"}, "mask_e09e7140-d929-11ed-b301-7b407328673d_18.jpg": {"label": "( $ 2 . 0 8 8 $ ) < 2 . 0 8 9 < ( $ 2 . 0 9 0 $ )", "predi": "( $ 2 . 0 8 8 $ ) < 2 . 0 8 9 < ( $ 2 . 0 9 0 $"}, "mask_ec072811-d52d-11ed-8d67-cd2864e724ac_6.jpg": {"label": "F { 2 2 } { 9 } * F { 6 } { 1 1 } = $ F { 4 ] { 3 } $", "predi": "F { 2 2 } { 9 } * F { 6 } { 1 1 } = $ F { 4 } { 3 } $"}, "mask_e7a85421-ecce-11ed-ad7b-bdb0342c2d0b_33.jpg": {"label": "6 0 - 4 0 $ > $ 5 5", "predi": "6 0 - 4 $ > $ 5 5"}, "mask_ea0f6361-df78-11ed-9638-63bc230c3369_42.jpg": {"label": "F { 6 } { 3 6 } $ < $ F ( 1 0 } { 1 2 }", "predi": "F { 6 } { 3 6 } $ < $ F { 1 0 } { 1 2 }"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_38.jpg": {"label": "1 1 0 1 6 1 1 1 1 1", "predi": "1 1 0 + 1 1 1 1 1 1 1"}, "mask_d81a7071-c653-11ed-ba9f-6d24159c277d_6.jpg": {"label": "$ 1 3 $ 0", "predi": "$ 1 2 $ )"}, "mask_6f7adad1-d802-11ed-b0a4-19f011fc32f1_12.jpg": {"label": "F { 8 } { 9 } / 1 2 = $ F { } { 9 } * F { 1 } { 3 } = F { 2 } { 2 7 } $", "predi": "F { 8 } { 9 } / 1 2 = $ F { 8 } { 9 } * F { 1 } { 3 } = F { 2 } { 2 7 } $"}, "mask_d9de1a61-df76-11ed-84f7-3f172b345776_15.jpg": {"label": "3 5 / ( 3 2 - 2 7 ) =", "predi": "3 5 / ( 3 2 - 2 7 ) = $ 7 $"}, "mask_f5b434f1-d53e-11ed-a640-fb9b4f351d70_2.jpg": {"label": "F { 1 5 } { 4 0 } $ > $ F { 3 } { 2 4 }", "predi": "F { 1 5 } { 4 0 } $ > $ F { 3 } { 2 4 } ="}, "mask_d83ca591-c20d-11ed-82ce-e3ec7feaad83_6.jpg": {"label": "( $ F { 1 0 0 0 } { 8 7 5 } $ ) * 0 .", "predi": "( $ F { 1 0 0 0 } { 8 7 7 } $ ) * 0 ."}, "mask_1e452061-c7dd-11ed-89f5-53d46e3ea68a_10.jpg": {"label": "1 7 + 2 - 8 = $ 1 1 $", "predi": "1 7 + 2 - 8 = $ 1 $"}, "mask_1c2b64f1-c7d9-11ed-8b19-3bcd8236b454_11.jpg": {"label": "5 4 6 / 8 = $ 7 2 $", "predi": "5 7 6 / 8 = $ 7 2 $"}, "mask_3cd22941-cf9b-11ed-bdc0-9b7e095850d0_4.jpg": {"label": "F { 5 } { 8 } * F { 3 } { 2 0 } = $ F { 3 } { 3 2 } $", "predi": "F { 5 } { 8 } * F { 3 } { 2 0 } = $ F { 3 2 } { 3 2 } $"}, "mask_4aa39801-d208-11ed-b2fe-5dd7384a699e_15.jpg": {"label": "2 1 0 0 米 $ > $ 千 米", "predi": "2 1 0 0 米 $ > $ 2 千 米"}, "mask_f7892e51-c951-11ed-9e3e-61ffe2d03345_29.jpg": {"label": "0 . 9 * 0 . 1 2 = $ 1 . 0 8 $", "predi": "0 . 9 * 0 . 1 2 = $ 1 . 0 . 8 $"}, "mask_e1c2cb71-cf01-11ed-90ca-73f9290a83b4_10.jpg": {"label": "0 = $ 8 . 6 = 4 . 1 = $ 5", "predi": "a = $ 8 . 6 = 4 . 1 = 5"}, "mask_4eba9eb1-cfca-11ed-af01-8fe4a3064efe_14.jpg": {"label": "6 0 0 米 $ < $ 6 米", "predi": "6 0 0 米 $ < $ 6 千 米"}, "mask_4af13181-ddea-11ed-bf12-13fdbcb84ac8_3.jpg": {"label": "6 0 4 8 3 9 0 ≈ ( $ 6 0 4 . 8 4 $ ) 万", "predi": "6 0 4 8 9 9 0 ≈ ( $ 6 0 4 8 4 $ ) 万"}, "mask_3f807261-eff2-11ed-a976-39ac7fb7ad4c_23.jpg": {"label": "F { 3 } { 6 } / F { 1 } { 8 } = $ F { 3 } { 2 } $", "predi": "F { 3 } { 1 6 } / F { 1 } { 8 } = $ F { 3 } { 2 } $"}, "mask_efb78d51-ecd0-11ed-8f11-bda4e2e18b85_25.jpg": {"label": "3 7 0 - 3 3 5 = $ 3 5 $", "predi": "3 7 0 - 3 3 5 = $ 3 5 5 $"}, "mask_d97129c1-c967-11ed-902f-c7e2c8d05c21_10.jpg": {"label": "4 . 2 / { 7 } { 1 0 } = $ 6 $", "predi": "4 . 2 / F { 7 } { 1 0 } = $ 6 $"}, "mask_fe660dd1-c8aa-11ed-81c2-ad6785b79d8f_46.jpg": {"label": "1 0 1 0 1", "predi": "4 0 1 1 1"}, "mask_fc254421-c44d-11ed-acb0-2d11f04581e2_12.jpg": {"label": "F { 2 4 } { 2 5 } : 4 8 = $ F { } { 2 5 } * F { 1 } { 2 } = 1 : 5 0 $", "predi": "F { 2 4 } { 2 5 } : 4 8 = $ F { 2 4 } { 2 5 } * F { 1 } { 2 } = 1 : 5 0 $"}, "mask_de424501-e65f-11ed-ab77-fd2449fd0b42_76.jpg": {"label": "F { 1 } 0 } { 1 5 } { 5 }", "predi": "F { 1 } { 9 9 } { 8 = $ F { 3 5 } { 9 } $"}, "mask_f07771c1-c3da-11ed-839a-2ffd213ffa5a_7.jpg": {"label": "6 7 0 * 3 0 = $ 2 0 1 0 0 $", "predi": "6 7 0 * 3 0 = $ 2 0 6 0 0 $"}, "mask_e8deb711-e2a3-11ed-9e55-a520ea92ec5e_35.jpg": {"label": "6 . 6 8 = ( $ 6 8 $ ) %", "predi": "0 . 6 8 = ( $ 6 8 $ ) %"}, "mask_fb168381-c45e-11ed-a493-45b1d70af721_21.jpg": {"label": "1 2 5 * 1 1 * 8 = $ 1 1 0 0 0 $", "predi": "1 2 5 * 1 1 * 8 = $ 1 1 0 0 0 0 $"}, "mask_db67a161-e428-11ed-908a-8745f4a591fb_1.jpg": {"label": "5 0 0 $ = $ 0 . 张", "predi": "5 0 0 $ = $ 0 ."}, "mask_fd964271-c19e-11ed-af14-99eb3ceddf3b_9.jpg": {"label": "+ 3 . 3 6 = $ 1 6 . 0 9 $", "predi": "+ 3 . 3 6 = $ 1 6 . 0 6 $"}, "mask_fbb3e991-ef1f-11ed-8d4b-9568653269a1_43.jpg": {"label": "1 5 . 4 * F { 2 } { } = $ 1 . 2 $", "predi": "5 . 4 * F { 2 } { } = $ 1 . 2 $"}, "mask_ed61e2d1-bf44-11ed-a345-ed3f6b44010c_23.jpg": {"label": "$ 1 8 4 - 9 ) / 8 = 9 $", "predi": "$ 1 8 1 - 9 ) / 8 = 9 $"}, "mask_e35bd8a1-cca9-11ed-a431-f7a8068ef342_6.jpg": {"label": "2 5 0 * 5 = $ 1 2 5 0 $", "predi": "2 5 0 * 5 = $ 1 2 5 1 $"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_31.jpg": {"label": "七 成 五 = ( $ 7 5 $ ) %", "predi": "成 成 五 = ( $ 7 5 $ ) %"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_25.jpg": {"label": "四 成 = ( $ 4 0 $ ) %", "predi": "成 成 = ( $ 4 0 $ ) %"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_36.jpg": {"label": "1 周 = ( $ 7 $ ) 天", "predi": "1 厘 = ( $ 7 $ ) 万"}, "mask_ebd00ca1-cc94-11ed-9629-47431946c27d_14.jpg": {"label": "3 0 + ( 4 5 + 8 ) = $ 6 7 $", "predi": "3 0 + ( 4 5 ) - 8 ) = $ 6 7 $"}, "mask_fa59ded1-cca4-11ed-8388-2be924829a12_6.jpg": {"label": "F { 4 0 } { 2 } = 2 0", "predi": "F { 4 0 } { 2 } = $ 2 0 $"}, "mask_fbe701d1-c95f-11ed-be2b-49c6311ac385_11.jpg": {"label": "6 4 3 / 8 ≈", "predi": "6 3 4 / 8 ≈"}, "mask_d5059331-cc91-11ed-a218-d59c38fd8296_26.jpg": {"label": "6 3 / 8 = $ 7 P 3 $", "predi": "6 3 / 8 = $ 7 P 7 $"}, "mask_eff79761-d9a7-11ed-9628-eb2ffcdf8662_25.jpg": {"label": "5 c m : ( $ 1 2 . 5 $ ) k m = 1 : 2 5 0 0 0 0", "predi": "5 c m : ( $ 1 2 5 $ ) k m = 1 : 2 5 0 0 0 0"}, "mask_1b96b631-f082-11ed-bcc7-f39e92579638_10.jpg": {"label": "F { 5 } { 7 } = $ F { 5 } { 9 } $", "predi": "F { 5 } { 7 } = $ F { 5 } { 9 } $ 5 { 5 { 5 { } { 5 } { $ F { 5 { } { $ 5 } { $ F { 5 { 5 } { $ 5 { 5 { 5 { 5 { 5 } { 5 $ { 5 { 5 { 5 { 5 } { 5 { 5 { 5 { 5 } { 5 $"}, "mask_5a116f41-dca5-11ed-912e-f724c0c2a1a0_26.jpg": {"label": "1 1 1 ( 0 . 4", "predi": "0 . 3 7 8 - 0 . 4"}, "mask_1d781620-d925-11ed-b182-dda4e9f1aaa3_24.jpg": {"label": "7 0 . 8 c m ^ 2 = ( $ 0 . 7 0 8 $ ) d m", "predi": "7 0 . 8 c m ^ 2 = ( $ 0 . 7 0 8 $ ) d m ^ 3"}, "mask_d678c801-deb5-11ed-81ea-3d89c6fa1628_7.jpg": {"label": "五 1 1 5 ( $ $ )", "predi": "五 1 1 5 ( )"}, "mask_e352c101-d917-11ed-b551-235da80c790a_21.jpg": {"label": "4 * ( $ 9 $ ) < 3 7", "predi": "4 * ( $ 9 P $ ) < 3 7"}, "mask_fb6c6541-e9b9-11ed-bbb5-af52a3d4768d_14.jpg": {"label": "3 8 / ( 2 9 + 1 2 ) = $ F { 3 8 } { 4 4 } $", "predi": "3 8 / ( 2 9 + 1 2 ) = $ F { 3 8 } { 4 9 } $"}, "mask_d7578300-d3a3-11ed-ae91-eb6c9f34fa4b_11.jpg": {"label": "$ x = F { } { 2 } $", "predi": "$ 米 = F { } { 2 } $"}, "mask_3b14cf11-c4a9-11ed-b508-db0f7c02ccfa_21.jpg": {"label": "6 6 / 1 2 0 0 0 = $ F { 1 } { 2 0 } $", "predi": "6 0 / 1 2 0 0 0 = $ F { 1 } { 2 0 } $"}, "mask_ec2672f0-d92a-11ed-9ed9-8bc57e9268ad_21.jpg": {"label": "3 2 * 2 . 5 * 9 = ( * ) * 2 . 5 * 9 =", "predi": "3 2 * 2 . 5 * 9 = ( * 2 . 5 * 9 ="}, "mask_e65f7e20-dd30-11ed-8d79-4332d1ec06e4_36.jpg": {"label": "6 * 4 + 2 = $ 2 6 $", "predi": "6 * 4 + 2 = 2 6"}, "mask_eee56f81-e4e9-11ed-a679-df399ecfb988_18.jpg": {"label": "9 8 $ > $ 0 . 8 9", "predi": "2 8 $ > $ 0 . 8 9"}, "mask_fdab2e41-c0d3-11ed-a578-9b110f5ccd50_19.jpg": {"label": "1 8 0 / 6 = $ 3 0 $", "predi": "1 8 0 / 6 = $ 3 $"}, "mask_0f8e41d1-d3a1-11ed-b91e-d7d3eedf17f4_6.jpg": {"label": "0 . 1 5 m ^ 2 $ < $ 3 1 1 d m ^ 3", "predi": "0 . 1 5 m ^ 3 $ < $ 3 1 1 d m ^ 3"}, "mask_3ab05e01-ce17-11ed-8d95-871805f46e15_37.jpg": {"label": "1 1 1 1 1", "predi": "1 1 1 1 1 1"}, "mask_fee728a1-d095-11ed-a34e-810c1e9c0e59_18.jpg": {"label": "一 成 = ( $ 1 0 $ ) %", "predi": "成 成 = ( $ 1 0 $ ) %"}, "mask_fc18d331-ed2d-11ed-b8ce-ff2a340f5f5a_9.jpg": {"label": "七 八 折 = ( $ 7 8 $ ) %", "predi": "八 八 成 = ( $ 7 8 $ ) %"}, "mask_2b612171-cee2-11ed-9c5c-595b8f803de6_51.jpg": {"label": "6 5 8 - 5 0 0 = $ 1 7 8 $", "predi": "6 7 8 - 5 0 0 = $ 1 7 8 $"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_9.jpg": {"label": "1 1 ( ( 8 L", "predi": "1 0 0 9 8"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_10.jpg": {"label": "七 七 * 1 八 1 =", "predi": "5 * 1 0 0 = $ 8 0 $"}, "mask_0ecef610-eb32-11ed-b32a-011f53d9877c_16.jpg": {"label": "+ 2 0 + 6 0 = $ 9 0 $", "predi": "+ 2 0 + 6 0 = $ 9 1 $"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_20.jpg": {"label": "七 1 1 1", "predi": "1 1 1 1 1 1 1"}, "mask_dd413da1-d043-11ed-af06-b70f51ba59d9_18.jpg": {"label": "F { 7 = $ 5 米 5 $", "predi": "+ = 5 米 = 5"}, "mask_f38ec3f1-d69e-11ed-b74d-afb93ff497b5_3.jpg": {"label": "2 . 7 3 * 3 = $ 9 . 1 9 $", "predi": "2 . 7 3 * 3 = $ 8 . 1 9 $"}, "mask_e1b3c1a1-c25b-11ed-bc2e-93481d99d4cf_45.jpg": {"label": "1 . 成 - 5 0", "predi": "1 . 角 - 5 0"}, "mask_eb6cf4d0-eda0-11ed-a5f2-3d0645728940_7.jpg": {"label": "1 8 0 ° * 4 = $ 2 7 0 ° $", "predi": "1 8 0 ° * 4 = $ 2 7 0 $"}, "mask_5b650e31-c484-11ed-a2dd-4f8a69cf87a7_18.jpg": {"label": "2 5 * 3 = $ 7 . 5 $", "predi": "2 . 5 * 3 = $ 7 . 5 $"}, "mask_4c6f1511-e423-11ed-aa3d-6be9e3674035_14.jpg": {"label": "1 3 0 + 2 0 / 2 = $ 4 0 $", "predi": "3 0 + 2 0 / 2 = $ 4 0 $"}, "mask_db5c6951-c1a3-11ed-9f04-6510225d74fa_10.jpg": {"label": "五 5 $ : ( $", "predi": "米 5 $ : - ( $"}, "mask_6e9904e1-d616-11ed-a52c-cfa8ef074264_16.jpg": {"label": "1 5 : 7 . 5 = $ F { 1 } { 5 } $", "predi": "1 . 5 : 7 . 5 = $ F { 1 } { 5 } $"}, "mask_f3b48390-d3ad-11ed-9af3-bba1aa92c1cc_39.jpg": {"label": "F { 5 } { 3 } * F ( $ F { 3 } { 5 } $ ) = 1", "predi": "F { 5 } { 3 } * ( $ F { 3 } { 5 } $ ) = 1"}, "mask_2feff0b1-d52a-11ed-a0a3-970bbc8adfec_36.jpg": {"label": "5 ° + 5 5 ° + 8 0 ° = ( $ 1 8 0 ° $ )", "predi": "5 ° + 5 5 ° + 8 0 ° = ( $ 1 8 0 ° $"}, "mask_1ab1d2f1-cf9d-11ed-b0e2-15cbdd81d5fa_19.jpg": {"label": "4 万 = ( $ 4 0 0 0 0 $ )", "predi": "4 万 = ( $ 4 0 0 0 0 $"}, "mask_3eefe9b0-d43c-11ed-91fe-e937538423d3_30.jpg": {"label": "$ 三 1 1 $", "predi": "2 / 6 = $ 9 . 7 $"}, "mask_e4758391-ea85-11ed-a215-63bf75350653_33.jpg": {"label": "3 5 / ( F { 1 } { 5 } + F { 3 } { 7 } ) = $ 2 2 $", "predi": "3 5 * ( F { 1 } { 5 } + F { 3 } { 7 } ) = $ 2 2 $"}, "mask_3f807261-eff2-11ed-a976-39ac7fb7ad4c_7.jpg": {"label": "F { 8 } { 5 } * 2 =", "predi": "F { 8 } { 5 } / 2 ="}, "mask_faeec271-ddd4-11ed-ac93-7f3d98aaa450_38.jpg": {"label": "F { 2 6 } { 2 1 } + F { 8 } { 7 } = $ F { 2 6 } { 2 1 } + F { 8 * 3 } { 7 * 3 } = F { 5 0 } { 2 1 } $", "predi": "F { 2 6 } { 2 1 } + F { 8 } { 7 } = $ F { 2 6 } { 2 1 } + F { 8 * 5 } { 7 * 3 } = F { 5 0 } { 2 1 } $"}, "mask_4b4e5511-ce25-11ed-8f25-fd73d1630bfd_20.jpg": {"label": "6 1 * 0 . 0 6 = $ 0 . 3 6 6 $", "predi": "6 . 1 * 0 . 0 6 = $ 0 . 3 6 6 $"}, "mask_fcd17b40-ddcb-11ed-94fc-a559e98a4c5b_11.jpg": {"label": "4 9 + 5 . 2 7 . 8 + 6 . 4", "predi": "4 . 9 + 5 . 2 7 . 8 + 6 . 4"}, "mask_e1b68af0-e440-11ed-876d-71cf24c36a09_13.jpg": {"label": "0 ) 4 角 厘 分", "predi": "0 ) 4 万 厘 米"}, "mask_dccd7fe0-bfbe-11ed-9196-b5061c4bb90c_11.jpg": {"label": "F { 1 9 } { 1 0 } * F { 1 } { 4 } = $ 1 F { 1 3 } { 2 0 } $", "predi": "F { 1 9 } { 1 0 } - F { 1 } { 4 } = $ 1 F { 1 3 } { 2 0 } $"}, "mask_eca0bf21-d6d1-11ed-a57c-5ba9d58fc7c9_29.jpg": {"label": "4 6 0 c ^ 2 0 . 4 5 4 分 4 分 2", "predi": "4 5 c m ^ 3 0 0 4 5 4 4 2"}, "mask_0bd2d041-eb36-11ed-9273-0f17366a4cfb_2.jpg": {"label": "9 / 7 = $ 1 P 2 $", "predi": "9 / 7 = $ 7 P 2 $"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_5.jpg": {"label": "六 三 = $ $", "predi": "= $ $"}, "mask_3b3c4161-c3f2-11ed-afa5-e3fd537b6828_1.jpg": {"label": "6 . 0 9 L $ > $ 9 0 m L", "predi": "0 . 0 9 L $ > $ 9 0 m L"}, "mask_e415b9f1-e4eb-11ed-8911-05c77a6b9a3c_20.jpg": {"label": "1 8 - 5 + 3 = 6", "predi": "1 8 - 5 + 3 ="}, "mask_1a458a41-eb41-11ed-ac4f-5deaca229540_0.jpg": {"label": "5 7 + x = $ 1 0 0 $", "predi": "5 7 + x = 1 0 0"}, "mask_dd4e6461-effd-11ed-9591-89cf6883e6d6_33.jpg": {"label": "七 4 六 9 = $ 5 P 5 $ 1 2 8 0 - 2 8 0 = 1 0 0 0", "predi": "4 4 / 9 = $ 5 P 5 ( 1 2 8 0 $ ) - 2 8 0 = 1 0 0 0"}, "mask_4e1b5001-ed9b-11ed-aeeb-1747dd124717_16.jpg": {"label": "6 5 7 0 0 0 万 ≈ ( $ 6 6 $ ) 万", "predi": "6 5 7 0 0 0 万 ≈ ( $ 6 6 $ ) 亿"}, "mask_fd964271-c19e-11ed-af14-99eb3ceddf3b_10.jpg": {"label": "0 . 4 - 0 . 2 5 =", "predi": "0 . 4 - 0 . 2 5 = ("}, "mask_fdd3ce41-ef1d-11ed-be82-1d6d4ce3e9f2_1.jpg": {"label": "$ 8 2 $ $ + $ $ 1 5 $ = $ 9 7 $ ( 元 )", "predi": "$ 8 2 $ + $ $ $ 1 5 $ = $ 9 7 $ ( 元 )"}, "mask_6a5288b1-c32a-11ed-b177-7f637bd61700_43.jpg": {"label": "3 8 2 / 2 = $ 1 9 1 1 $", "predi": "3 8 2 / 2 = $ 1 9 1 $"}, "mask_d52ad251-cb7c-11ed-8866-b1d65d108aef_4.jpg": {"label": "F { 7 } { 1 8 } + F { 5 } { 1 2 } + F { 1 1 } { 1 8 } =", "predi": "F { 7 } { 1 8 } + F { 5 } { 1 2 } + F { 1 1 } { 1 8 }"}, "mask_5e66ee31-e4de-11ed-a463-a13c60a05132_21.jpg": {"label": "F { 4 } { 5 } + F { 1 1 } { 3 0 } = $ F { 2 4 } { 3 0 } + F { 1 1 } { 3 0 } = F { 3 9 } { 3 0 } = F { 7 } { 6 } $", "predi": "F { 4 } { 5 } + F { 1 1 } { 3 0 } = $ F { 2 4 } { 3 0 } + F { 1 1 } { 3 0 } = F { 3 5 } { 3 0 } = F { 7 } { 6 } $"}, "mask_f35b2c51-c962-11ed-b500-e1f479f02c1c_30.jpg": {"label": "4 6 米 8 8 8", "predi": "1 6 米 8 8 8"}, "mask_4bde2e70-e41d-11ed-b190-a701d8346fd0_8.jpg": {"label": "1 亿 = ( $ 1 0 0 0 0 0 0 0 0 $ )", "predi": "1 亿 = ( $ 1 0 0 0 0 0 0 0 0 $"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_26.jpg": {"label": "三 $ 2 $", "predi": "= $ 6 0 2 $"}, "mask_f5b434f1-d53e-11ed-a640-fb9b4f351d70_42.jpg": {"label": "F { 1 4 } { 1 8 } = $ F { 7 } { 9 } $", "predi": "F { 1 4 } { 1 8 } = $ F { 7 } { 9 } { 9 7 $ F { 7 8 { 9 7 { 9 7 7 { 9 7 { $ F { 7 { } { $ 7 7 { 7 7 { 9 $ { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 { 7 } { 7 7 $ { 7 7 $ { 7 7 { 7 7 7 7 $ { 7 7 $ { 7 7 { 7 7 7 $ { 7 7 $ { 7 7 { 7 7 7 $ { 7 $ { 7 7 { 7 7 7 $ { $ { 7 { 7 { 7 7 $ { 7 $ { 7 { 7 7 { 7 7 $ { $ 7 { 7 { 7 7 { 7 7 $ { $ 7 { 7 { 7 7 7 7 $ 7 $ { 7 { 7 {"}, "mask_dfcb2461-eb40-11ed-bf18-fbcb8b623bb2_30.jpg": {"label": "七 八 = $ $", "predi": "+ $ 5 $"}, "mask_df4128f0-d9de-11ed-a03c-b77097dacfcb_1.jpg": {"label": "= ( $ 5 5 $", "predi": "= ( $ 5 $"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_37.jpg": {"label": "1 1 1 1 4 1 4 1 0", "predi": "1 1 1 1 1 1 1 1 1 1"}, "mask_4bd75311-deab-11ed-8bb1-1d9304b61fc2_41.jpg": {"label": "F 1 } { 6 元 F 1", "predi": "4 0 5 / 4 ="}, "mask_f65eaaf1-d52c-11ed-8271-af52227f6dad_6.jpg": {"label": "F { 3 } { 2 } * F { 1 } { 4 } = $ F { 1 } { 6 } $", "predi": "F { 2 } { 3 } * F { 1 } { 4 } = $ F { 1 } { 6 } $"}, "mask_1bf50501-df7a-11ed-bac7-fb2f45b6f854_12.jpg": {"label": "F { 5 } { 1 2 } $ < $ F { 1 } { 1 2 }", "predi": "F { 7 } { 1 2 } $ < $ F { 1 } { 1 2 }"}, "mask_ed06c621-d790-11ed-9d16-ad82cc3f7a88_8.jpg": {"label": "5 - ( $ 8 $ ) = 7", "predi": "1 5 - ( $ 8 $ ) = 7"}, "mask_e6d8b7a1-cf4c-11ed-a851-c7174b1f6160_20.jpg": {"label": "3 0 c m : 6 0 0 m = $ 1 : 2 0 0 0 0 $", "predi": "3 c m : 6 0 0 m = $ 1 : 2 0 0 0 0 $"}, "mask_6d480131-dd24-11ed-afe3-f1a572ca92fc_30.jpg": {"label": "4 2 / 5 = $ 8 $ P $ 2 $", "predi": "4 2 / 5 = $ 8 P 2 $"}, "mask_ec329371-e297-11ed-a5eb-8ffb7edf4f3b_14.jpg": {"label": "F { 3 0 } { 5 5 }", "predi": "F { 3 0 } { 5 5 } ="}, "mask_dda2fb41-db3c-11ed-bbe1-fb5d906afa04_0.jpg": {"label": "1 0 = ( $ 7 5 $ ) 0 m m", "predi": "1 1 = ( $ 7 5 $ ) c m"}, "mask_e25e89d1-dea5-11ed-a862-fbf397740eb1_16.jpg": {"label": "3 6 : . 8 4 = $ F { } { 7 } $", "predi": "3 6 : 8 4 = $ F { } { 7 } $"}, "mask_f1cd68b1-cefa-11ed-87ca-2d9a37665e4e_0.jpg": {"label": "9 角 9 分 $ > $ 1 元 2 角", "predi": "9 角 9 分 $ < $ 1 元 2 角"}, "mask_e0dd7e51-daa3-11ed-8374-4d73392fe3f4_34.jpg": {"label": "2 5 / 5 = $ 5 $", "predi": "2 5 / 5 = $ 5 7 $"}, "mask_de424501-e65f-11ed-ab77-fd2449fd0b42_80.jpg": {"label": "F 5 4 } 5 } = $ ( { 3 $", "predi": "F { 5 4 } { 4 7 - 9 = $ 4 9 $"}, "mask_d66c80a0-d08b-11ed-b88a-f35c3433537e_6.jpg": {"label": "6 = $ 1 0 $", "predi": "a = $ 1 0 $"}, "mask_5a116f41-dca5-11ed-912e-f724c0c2a1a0_29.jpg": {"label": "七 分 4 0", "predi": "0 . 8 4 5 6 ≈ ( $ 1 . 8 $ )"}, "mask_ed2882e1-bfbe-11ed-9196-b5061c4bb90c_20.jpg": {"label": "F { 5 } { 9 } + F { 1 } { 4 } = $ F { 2 9 } { 3 6 } $", "predi": "F { 5 } { 9 } + F { 1 } { 4 } = $ F { 2 9 9 } { 3 6 } $"}, "mask_4a001571-df84-11ed-ac88-4787b0b68593_2.jpg": {"label": "1 7 - 9 元 = ( $ 8 $ ) 元", "predi": "1 7 元 - 9 元 = ( $ 8 $ ) 元"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_18.jpg": {"label": "三 = $ 5 $", "predi": "= $ 5 $"}, "mask_efb5a791-ed8b-11ed-8c42-b5f8e932df84_44.jpg": {"label": "F { 3 } { 4 } = F { ( $ 1 2 $ ) } { 6 }", "predi": "F { 3 } { 4 } = F { ( $ 1 2 $ ) } { 1 6 }"}, "mask_5d915051-e6ef-11ed-99a1-61d1f3929e03_43.jpg": {"label": "F { 5 7 } { 1 0 0 0 } = ( $ 0 . 0 5 7 $ )", "predi": "F { 5 7 } { 1 0 0 0 } = ( $ 0 . 0 7 7 $ )"}, "mask_e273c571-c44c-11ed-acb0-2d11f04581e2_17.jpg": {"label": "F { 1 0 } { 2 7 } : F { 5 } { 9 } = $ F { 2 } { 3 } * = 2 : 3 $", "predi": "F { 1 0 } { 2 7 } : F { 5 } { 9 } = $ F { 2 } { 3 * 7 } * F { } { } = 2 : 3 $"}, "mask_1e8ee371-ed54-11ed-af4f-1b6df7ae09e2_34.jpg": {"label": "5 0 . 2 4 ≈ ( $ 5 0 . 2 $ )", "predi": "5 0 . 2 4 ≈ ( $ 5 0 . 2 $"}, "mask_f02ae1a1-ca2b-11ed-8d7b-4b03830502be_1.jpg": {"label": "F { 3 } { 5 } : 4 = F { 5 } { 8 } : ( $ F { 2 5 } { 6 } $ )", "predi": "F { 3 } { 5 } : 4 = F { 5 } { 8 } : ( $ F { 2 5 } { 6 } $ ) }"}, "mask_f6c2f251-e222-11ed-8e62-e7ec90002be1_33.jpg": {"label": "9 + 5 = $ 1 4 $", "predi": "1 + 5 = $ 1 4 $"}, "mask_feab8181-c076-11ed-abf9-61e66d7d6dc1_22.jpg": {"label": "0 2 / 5 = $ 4 6 $", "predi": "0 / 5 = $ 4 6 $"}, "mask_f8cc8bd1-ea63-11ed-8057-191f1749131e_27.jpg": {"label": "6 8 - 4 2 = $ 7 4 $", "predi": "6 6 - 4 2 = $ 7 4 $"}, "mask_4e1b5001-ed9b-11ed-aeeb-1747dd124717_34.jpg": {"label": "1 9 . 9 9 8 ≈ ( $ 2 0 . 0 0 $ )", "predi": "1 9 . 9 9 8 ≈ ( $ 2 0 . 0 0 $"}, "mask_2eb2b0e1-db2c-11ed-93f2-4da729f169a6_30.jpg": {"label": "F { 6 } { 9 } = $ F { 2 } { } = F { 2 } { 3 } $", "predi": "F { 6 } { 9 } = $ F { 2 } { 3 } $"}, "mask_fc18d331-ed2d-11ed-b8ce-ff2a340f5f5a_28.jpg": {"label": "五 折 = ( $ 5 0 $ ) %", "predi": "五 成 = ( $ 5 0 $ ) %"}, "mask_fee728a1-d095-11ed-a34e-810c1e9c0e59_42.jpg": {"label": "九 成 = ( $ 9 0 $ ) %", "predi": "元 成 = ( $ 9 0 $ ) %"}, "mask_d8659441-e973-11ed-8d17-f3444709f1b5_8.jpg": {"label": "( 2 $ ) = $ 六 0 8 2", "predi": "( 2 $ ) = 米 0 8 2 $"}, "mask_e3b49540-d694-11ed-bfec-cdce704b26e6_2.jpg": {"label": "F { 2 } { 3 } : F { 1 1 } { 9 } = $ 6 : 1 1 $", "predi": "F { 2 } { 3 } : F { 1 1 } { 9 } = $ 6 : 1 1 1 $"}, "mask_f363f141-c193-11ed-8732-dbd4e48b5136_19.jpg": {"label": "5 6 / 4 = $ 1 4 $", "predi": "5 6 / 4 = $ 1 4 P 1 $"}, "mask_5bba47b1-d46a-11ed-aa95-878fc944c866_18.jpg": {"label": "3 0 0 / F { 1 } { 1 0 0 0 } = $ 3 0 0 0 0 0 $", "predi": "3 0 0 / F { 1 } { 1 0 0 0 } = $ 3 0 0 0 0 0 0 $"}, "mask_1d5f9f40-dd0e-11ed-a456-6b5dfb7f9111_2.jpg": {"label": "F [ 3 } { 4 } = $ F { 2 1 } { 2 8 } $", "predi": "F { 3 } { 4 } = $ F { 2 1 } { 2 8 } $"}, "mask_dd692261-e296-11ed-9bdc-1bcda24cade3_29.jpg": {"label": "5 0 / 5 = $ 5 $", "predi": "5 0 + 5 = $ 5 $"}, "mask_ff3307a1-c250-11ed-8390-15967ebdcb8c_23.jpg": {"label": "7 + 5 2 + + 米 6 4 - 元 ( $ 7 2 6 $ )", "predi": "7 + 5 . 2 分 + 米 6 分 - 元 ( $ 7 2 6 $ )"}, "mask_2a2a1671-e109-11ed-b306-ada02662f8c6_109.jpg": {"label": "2 0 + 8 1 / 9 = $ 1 9 $", "predi": "1 0 + 8 1 / 9 = $ 1 9 $"}, "mask_4af4ced1-d214-11ed-ad85-07eac7019b1c_19.jpg": {"label": "9 7 - 5 = $ 6 4 $", "predi": "9 7 - 3 3 = $ 6 4 $"}, "mask_d328ae71-d9da-11ed-b8f5-21d006063f91_12.jpg": {"label": "七 0 * 6 0", "predi": "2 0 * 6 0"}, "mask_fb679451-e358-11ed-b038-15145afa6ce7_0.jpg": {"label": "$ 2 6 $ $ - $ $ 2 0 $ = $ 6 $ ( $ 只 $ )", "predi": "$ 2 6 $ - $ 2 0 $ = $ 6 $ ( $ 7 $ )"}, "mask_e5b40971-dd10-11ed-9d84-c18aa0f7804d_20.jpg": {"label": "5 3 0 / 6 0 ≈ $ 9 0 $", "predi": "5 3 0 / 6 = $ 9 0 ° $"}, "mask_5aace2c1-df54-11ed-9fe6-8950d6bbd9e9_18.jpg": {"label": "4 5 + 3 = $ 4 8 $", "predi": "4 5 + 3 = $ 4 8 0 $"}, "mask_2c4ea711-e41d-11ed-8fa4-8bdfce412ea8_34.jpg": {"label": "2 . 5 = ( $ F { 1 } { 4 } $ )", "predi": "2 . 2 = ( $ F { } { 4 } $ )"}, "mask_ccc94381-ce24-11ed-ae5e-bbbe8c893dff_26.jpg": {"label": "1 6 - 9 = 7", "predi": "1 6 - 9 = $ 7 $"}, "mask_c16a2f41-c07d-11ed-8608-3ffa0ff724b9_0.jpg": {"label": "3 0 + 5 5 6 6", "predi": "3 0 + 5 5 6 6 9 0"}, "mask_b9a3cfa1-cc94-11ed-b49d-a997a7ab92cd_7.jpg": {"label": "1 2 > 1 6 - ( $ $ )", "predi": "1 2 > 1 6 - ( $ 5 $ )"}, "mask_b1b39f21-c70b-11ed-b7e9-e7375eb474b7_27.jpg": {"label": "( $ $ ) * 8 = 1 2 * 1 6", "predi": "( ) * 8 = 1 2 * 1 6"}, "mask_90f3ddd1-c329-11ed-a6c3-5781877ada00_22.jpg": {"label": "1 1 - 5 = $ 9 $", "predi": "1 4 - 5 = $ 9 $"}, "mask_8feeeb21-de6b-11ed-8646-33765f8ba913_4.jpg": {"label": "0 . 0 0 8 = $ F { 8 } { 1 0 0 0 } = F { 2 } { 2 5 0 } $", "predi": "0 . 0 0 8 = $ F { 8 } { 1 0 0 0 } = F { 2 } { 2 5 0 0 } $"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_18.jpg": {"label": "2 - 9 - 4 4", "predi": "9 - 9 - 4 4"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_2.jpg": {"label": "- 1 = $ 2 1 $ $ 8 1 $", "predi": "- 1 = 2 1 $ - $ 8 1"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_39.jpg": {"label": "9 = ( $ 8 $ ) - L", "predi": "9 = ( $ 8 $ ) - 千"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_29.jpg": {"label": "6 = - 8 1", "predi": "6 = - 8"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_16.jpg": {"label": "7 8 = 2 $ + $ 8", "predi": "7 8 = $ 8 $ + $ 8"}, "mask_a47a0c61-d52d-11ed-8d67-cd2864e724ac_3.jpg": {"label": "F { 1 } { 2 } * F { 4 } { 5 } = F { 2 } { 5 }", "predi": "F { 1 } { 2 } * F { 4 } { 5 } = $ F { 2 } { 5 } $"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_0.jpg": {"label": "6 $ > $ 9 + 8", "predi": "4 $ > $ 9 + 8"}, "mask_2bbd19f0-db60-11ed-94bc-27939ff3335f_41.jpg": {"label": "9 3 - 8 7 0 = $ 6 0 $", "predi": "9 3 0 - 8 7 0 = $ 6 0 $"}, "mask_9e321fa1-cede-11ed-85a4-cf1cd11915cb_28.jpg": {"label": "y * y = $ y ^ 2 $", "predi": "x * a = $ 4 ^ 2 $"}, "mask_8b4c1480-ee65-11ed-a0ac-d5695dcde8d4_38.jpg": {"label": "6 8 + 5 4 $ ) 5 2 5 $", "predi": "6 8 + ( 5 + 2 ) = $ 7 5 $"}, "mask_fc414991-d6c2-11ed-84b2-0960ca7d0f2e_16.jpg": {"label": "F { 3 } { 1 } + F { 1 } { 7 } = $ F { 4 } { 7 } $", "predi": "F { 3 } { 7 } + F { 1 } { 7 } = $ F { 4 } { 7 } $"}, "mask_b7d11c61-cc9d-11ed-8ebf-0f8a28cc1a0c_9.jpg": {"label": "6 $ x $ 3 = 2 4 $ - $ 6", "predi": "6 $ * $ 3 = 2 4 $ - $ 6"}, "mask_4980ef21-d467-11ed-a34e-8dcded8a8783_14.jpg": {"label": "4 5 + 1 5 + 4 0 =", "predi": "4 5 + 1 5 + 4 0 = $ 0 0 $"}, "mask_a0ab22e1-d2e4-11ed-b586-d99cfdbe5666_21.jpg": {"label": "1 7 - 5 = $ 1 2 $", "predi": "1 7 - 5 = $ 1 2 . $"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_13.jpg": {"label": "8 = $ 8 - $", "predi": "8 - 8 -"}, "mask_85635f51-dc50-11ed-83f0-933afe161692_0.jpg": {"label": "3 0 . 1 $ > $ 3 0 1 . 2", "predi": "3 0 2 . 1 $ > $ 3 0 1 . 2"}, "mask_235bc631-c4c2-11ed-95f7-71d52b2784e3_31.jpg": {"label": "2 x + 0 . 6 = $ 5 . 2 $", "predi": "2 x + 0 . 6 = 5 . 2"}, "mask_09a11621-dde3-11ed-ab18-81baaab662d4_27.jpg": {"label": "3 8 $ > $ 2 8", "predi": "3 8 $ > $ 2"}, "mask_7ef42c60-bfc4-11ed-8d6a-1304f93d1dd9_39.jpg": {"label": "1 5 * 5 = $ 8 0 $", "predi": "1 6 * 5 = $ 8 0 $"}, "mask_7a9f7241-d870-11ed-9c06-c909318bca6a_5.jpg": {"label": "F { 4 } { 1 5 } + F { 1 }", "predi": "F { 4 } { 1 5 } + F { 1 } { 5 } ="}, "mask_0ad3d481-ef20-11ed-b259-b31592c9c3c8_32.jpg": {"label": "6 2 - 5 9 = $ 3 $", "predi": "6 2 - 5 9 = $ 3 3 $"}, "mask_4593f731-ee76-11ed-9b22-4d68216c24c1_37.jpg": {"label": "4 4 + 3 - 8 = $ 3 5 $", "predi": "4 0 + 3 - 8 = $ 3 5 $"}, "mask_afef6191-e7d3-11ed-bd30-3bf1fd7a27ca_12.jpg": {"label": "5 m = $ ( ) m ^ 2 $", "predi": "5 m = ( ) c m ^ 2"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_48.jpg": {"label": "6 1 $ 9 $ 6 + 8", "predi": "6 1 $ > $ 6 + 8"}, "mask_d39a8661-c25d-11ed-9313-0be8c6ff5802_13.jpg": {"label": "F { 1 6 } { 6 } * F { 4 }", "predi": "F { 1 6 } { 2 1 } / F { 4 } { 7 } ="}, "mask_efb6d9c1-ef30-11ed-b984-f34b7720ac33_1.jpg": {"label": "F { 3 } { 1 7 } + F { 3 4 } { 1 5 } = $ F { 2 } { 5 } $", "predi": "F { 3 } { 1 7 } * F { 3 4 } { 1 5 } = $ F { 2 } { 5 } $"}, "mask_55b413f1-da05-11ed-b765-bd591c6ece53_31.jpg": {"label": "6 0 1 1 =", "predi": "6 c 1 1 ="}, "mask_2e921f31-e7d2-11ed-92b3-c99ebdbf361f_6.jpg": {"label": "F { 9 } { 8 } - F { 8", "predi": "F { 3 9 } { 5 6 } * F { 2 8 } { 1 3 } ="}, "mask_2e921f31-e7d2-11ed-92b3-c99ebdbf361f_4.jpg": {"label": "F { 3 } { 6 } - F { 1", "predi": "F { 3 } { 1 6 } / F { 2 1 } { 3 2 } ="}, "mask_9e321fa1-cede-11ed-85a4-cf1cd11915cb_0.jpg": {"label": "a * b * c = $ a b c $", "predi": "a * b * c = a a c"}, "mask_75ecbbb0-c932-11ed-a31f-e97cbd3bdcec_6.jpg": {"label": "3 . 1 4 * 3 0 = $ 9 4 2 $", "predi": "3 . 1 4 * 3 0 = $ 9 4 . 2 $"}, "mask_1092d081-bf1c-11ed-8ce5-67b53c3d12db_38.jpg": {"label": "1 8 - 4 = $ 1 9 $", "predi": "1 8 - 4 = $ 1 4 $"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_29.jpg": {"label": "1 = 3 - 1 1", "predi": "1 = - 1 1"}, "mask_e4788f81-ee64-11ed-82f4-0900d61267b8_40.jpg": {"label": "8 4 - 5 5 = $ 7 5 $", "predi": "8 4 + 5 + 2 ) = $ 7 5 $"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_13.jpg": {"label": "F { 4 } { 7 3 = $ 4 + 1 8 $", "predi": "$ 4 + 3 = 4 + 4 $"}, "mask_2dcd9841-e5c5-11ed-8f55-9718bf1ca423_40.jpg": {"label": "3 . 4 0 = $ ( ) d $", "predi": "3 . 4 m ^ 3 = ( ) d m ^ 3"}, "mask_89c81a11-c0a6-11ed-a925-dd223da8c677_25.jpg": {"label": "2 2 + 0 . 6 = $ 5 . 2 $", "predi": "2 2 + 0 . 6 = 5 . 2"}, "mask_10ac57f0-ea8a-11ed-8ea0-2726ffd31f5c_1.jpg": {"label": "3 3 3 3 * 3 3 3 3 = $ 1 1 1 0 8 8 8 9 $", "predi": "3 3 3 3 * 3 3 3 3 = $ 1 1 1 0 8 8 8 9"}, "mask_eb15fa71-e5b9-11ed-a84b-6f60be017b3d_43.jpg": {"label": "2 0 + 4 6 = $ 6 6 $", "predi": "2 0 + 4 6 = $ 6 6 6 $"}, "mask_f3949011-dc1f-11ed-a6ca-738f081ae4e6_25.jpg": {"label": "3 5 - 3 0 $ = $ 1 5 - 1 0", "predi": "3 5 - 3 0 $ = $ 1 5 - 1 c"}, "mask_fc18d331-ed2d-11ed-b8ce-ff2a340f5f5a_38.jpg": {"label": "七 折 = ( $ 7 0 $ ) %", "predi": "千 成 = ( $ 7 0 $ ) %"}, "mask_ed36f781-d9f9-11ed-83eb-9fb0cbf0f2d7_21.jpg": {"label": "8 / 4 0 0 0 0 = $ F { 2 } { 1 0 0 0 0 } = F { 1 } { 5 0 0 0 } $", "predi": "8 / 4 0 0 0 0 = $ F { 2 } { 1 0 0 0 0 } = F { 1 } { 5 0 0 0 0 } $"}, "mask_1f79fd31-c8a9-11ed-9c74-9f765749440d_28.jpg": {"label": "八 八 3 1 4 0", "predi": "8 0 0 / 8 = $ 1 0 0 $"}, "mask_fc18d331-ed2d-11ed-b8ce-ff2a340f5f5a_22.jpg": {"label": "四 五 折 = ( $ 4 5 $ ) %", "predi": "吨 五 成 = ( $ 4 5 $ ) %"}, "mask_0a5ed921-e05a-11ed-aec8-694e69857300_22.jpg": {"label": "5 % = ( $ 0 $ )", "predi": "5 % = ( $ 2 0 $ )"}, "mask_5deecaa1-ddd0-11ed-a1ba-cfbbd3beaed0_26.jpg": {"label": "2 元 1 角 $ > $ 2 元 1", "predi": "2 元 1 角 $ > $ 2 元 1 分"}, "mask_2b4d2d20-dead-11ed-92ce-8f9d95bbca57_22.jpg": {"label": "7 6 c m ^ 2 = ( $ 7 6 $", "predi": "7 6 c m ^ 3 = ( $ 7 6 $"}, "mask_d59754a0-c57a-11ed-a2ad-e19c170ef01a_16.jpg": {"label": "2 2 厘 $ > $ 2 分 米", "predi": "2 2 厘 米 $ > $ 2 分 米"}, "mask_3fdd92d1-e1d6-11ed-9e20-ef1ad4a58e8d_32.jpg": {"label": "1 2 * F ( $ 1 } { 1 2 } $ ) = 1", "predi": "1 2 * ( $ F { 1 } { 1 2 } $ ) = 1"}, "mask_d721f011-e004-11ed-97b4-25072c4ce7ba_22.jpg": {"label": "3 m l = ( $ 0 . 0 0 3 $ ) L", "predi": "3 m L = ( $ 0 . 0 0 3 $ ) L"}, "mask_dcfaa051-ea71-11ed-b8a2-371f4bcad74c_2.jpg": {"label": "* 5 1 0 0 0 0 0 0 = $ ( ) m 分 ( $", "predi": ": 5 1 0 0 0 0 0 0 = ( ) 亿 ≈ ("}, "mask_fce25c41-e4ff-11ed-b20a-59db44123f4d_27.jpg": {"label": "0 . 4 2 = $ F { 2 1 } { 5 0 } $", "predi": "0 . 2 2"}, "mask_f7109ac1-dd0c-11ed-b704-e77afdf7f94f_31.jpg": {"label": "( 七 ( 1 六 1", "predi": "( 2 / 8 = $ 9 $"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_29.jpg": {"label": "1 1 1 1 1 1 1 . 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_33.jpg": {"label": "1 1 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1"}, "mask_dd9f55f1-cc8f-11ed-a231-692348c94de2_2.jpg": {"label": "3 . 6 8 L = ( ) c m", "predi": "3 . 6 8 L = ( ) c m ^ 3"}, "mask_fc2504c1-d07c-11ed-820b-9d16e36cf589_39.jpg": {"label": "F { 1 0 3 } { 9 } = $ 1 0 3 / 9 = 1 1 P 4 = 1 1 F { 4 } { 9 } $", "predi": "F { 1 0 3 } { 9 } = $ 1 0 3 / 9 = 1 1 P 4 = 1 F { 4 } { 9 } $"}, "mask_3ad3d201-e4b4-11ed-8987-79eff8960919_0.jpg": {"label": "F { 1 } { 5 } / 6 = $ { 1 } { 3 0 } $", "predi": "F { 1 } { 5 } / 6 = $ F { 1 } { 3 0 } $"}, "mask_d8972341-c1ad-11ed-b768-613d9569d463_43.jpg": {"label": "F { 1 } { 5 } / 6 = $ { 1 } { 3 0 } $", "predi": "F { 1 } { 5 } / 6 = $ F { 1 } { 3 0 } $"}, "mask_f98714a1-c484-11ed-ae1e-7d11c488cff7_16.jpg": {"label": "2 . 5 + 5 =", "predi": "2 . 5 / 5 ="}, "mask_1d5f2391-c624-11ed-9be4-c721ba52226f_4.jpg": {"label": "0 0 0 0 = $ 5 0 0 $", "predi": "2 0 0 0 0 = $ 5 0 0 $"}, "mask_ea14aff1-e03b-11ed-bd1a-4dd44cce79f9_48.jpg": {"label": "4 8 / 8", "predi": "4 8 / 8 ="}, "mask_e5ca7c21-d935-11ed-a88a-a1613aa4f187_37.jpg": {"label": "3 m ^ 2 8 d m ^ 2 = ( $ 3 . 8 0 $ ) m ^ 2", "predi": "3 m ^ 2 8 0 d m ^ 2 = ( $ 3 . 8 0 $ ) m ^ 2"}, "mask_e0268f11-d47c-11ed-8d4e-63ad0fd8d277_4.jpg": {"label": "5 8 角 ( $ 5 $ ) 元 ( $ 8 $ ) 角", "predi": "5 8 角 = ( $ 5 $ ) 元 ( $ 8 $ ) 角"}, "mask_e5155d61-d168-11ed-a116-110d5447c267_2.jpg": {"label": "5 + 2 . 5 ) * 6 = $ 5 4 $", "predi": ". 5 + 2 . 5 ) * 6 = $ 5 4 $"}, "mask_fb197ba1-df37-11ed-a794-0bd03935def7_13.jpg": {"label": "1 5 x = $ 6 0 * 2 5 $", "predi": "1 5 x = 6 0 * 2 5"}, "mask_5e66ee31-e4de-11ed-a463-a13c60a05132_15.jpg": {"label": "F { 7 } { 1 5 } + F { 1 9 } { 2 1 } + F { 2 } { 2 1 } = $ F { 1 9 } { 2 1 } + F { 2 } { 2 1 } + F { 7 } { 1 5 } = 1 $", "predi": "F { 7 } { 1 5 } + F { 1 9 } { 2 1 } + F { 2 } { 2 1 } = $ F { 1 9 } { 2 1 } + F { 2 } { 2 1 } + F { 7 } { 1 5 } = 1"}, "mask_f5a09d01-d79b-11ed-b1f9-d59d104042e2_11.jpg": {"label": "F { 1 } { 9 } + F { 2 } { 3 } = $ F { 1 } { 9 } + F { 6 } { 9 } = F { 7 } { 9 } $", "predi": "F { 1 } { 9 } + F { 2 } { 3 } = $ F { 1 } { 9 } + F { 6 } { 9 } = F { 7 } { 9 } { 9 7 { 9 } { 9 7 { 9 7 { 9 } { 9 7 7 { 9 7 { 9 } { 9 7 { 9 } { F { 7 } { } { 9 } { F { 7 } { } { 9 } { F { 7 } { } { 7 } { F { 7 } { } { 7 } { 7 { 7 { 7 } { 7 } { 7 { 7 { 7 } { } { 7 { 7 } { } { 7 { 7 { } { } { } { 7 { 7 { } { } { } { } { 7 { 7 { } { } { } { } {"}, "mask_d678c801-deb5-11ed-81ea-3d89c6fa1628_2.jpg": {"label": "1 3 毫 1 5 ( $ 1 $ )", "predi": "1 3 角 1 5 ( $ 1 $ )"}, "mask_dc8f6901-d123-11ed-8559-8d0ce8d3b4ac_15.jpg": {"label": "$ 8 x / 8 = 4 8 / 8 $", "predi": "$ 8 x / 8 = 4 8 / 8"}, "mask_e20a9d31-df44-11ed-a77b-876a1ac60f73_41.jpg": {"label": "4 5 0 c m ^ 3 = ( $ 0 . 0 4 5 $ ) d m ^ 3", "predi": "4 5 c m ^ 3 = ( $ 0 . 0 4 5 $ ) d m ^ 3"}, "mask_01ddf6d1-e705-11ed-ac2c-57eb037f92a2_5.jpg": {"label": "8 $ - $ 3 = 5", "predi": "8 $ + $ 3 = 5"}, "mask_1bcc1ae1-d5a3-11ed-bf9c-939b8a07abcd_1.jpg": {"label": "F { 3 } { 7 } * F { 3 } { 7 } = $ F { 6 } { 7 } $", "predi": "F { 3 } { 7 } + F { 3 } { 7 } = $ F { 6 } { 7 } $"}, "mask_3cd22941-cf9b-11ed-bdc0-9b7e095850d0_17.jpg": {"label": "5 9 * { 7 } { 5 8 } = $ 7 F { 7 } { 5 8 } $", "predi": "5 9 * F { 7 } { 5 8 } = $ 7 F { 7 } { 5 8 } $"}, "mask_00ab4401-dd11-11ed-b69a-d76908d4f989_12.jpg": {"label": "0 . 3 亿 = ( $ 3 0 0 0 0 0 0 0 $ )", "predi": "0 . 3 亿 = ( $ 3 0 0 0 0 0 0 0 $"}, "mask_d7715f41-cf53-11ed-9f11-29237218da95_7.jpg": {"label": "F { 1 } { 6 } : F { 1 } { 5 } = $ F { 1 } { 3 0 } $", "predi": "F { 1 } { 6 } * F { 1 } { 5 } = $ F { 1 } { 3 0 } $"}, "mask_d1124a51-e96c-11ed-aff6-0d373b26e3b7_3.jpg": {"label": "2 9 6 5 1 0 0 0 0 0 0 = ( ) 亿 ≈ ( ) 亿", "predi": "2 9 6 5 1 0 0 0 0 0 = ( ) 亿 ≈ ( ) 亿"}, "mask_f4b43940-e364-11ed-a124-a512d473bd22_22.jpg": {"label": "7 . 0 1 k m ^ 2 = ( $ 0 . 7 0 1 $ 顷", "predi": "7 . 0 1 k m ^ 2 = ( $ 0 . 7 0 1 $ )"}, "mask_fbc4ec91-e637-11ed-9d6c-3d1a33781e77_27.jpg": {"label": "F { 7 } { } - F { 1 } { 8 } = $ F { 5 6 } { 7 2 } - F { 9 } { 7 2 } = F { 4 7 } { 7 2 } $", "predi": "F { 7 } { 9 } - F { 1 } { 8 } = $ F { 5 6 } { 7 2 } - F { 9 } { 7 2 } = F { 4 7 } { 7 2 } $"}, "mask_ee94f100-ea38-11ed-b223-91fb2f3898ac_17.jpg": {"label": "5 个 月 = ( $ F { 5 } { 1 2 } $ ) 年", "predi": "5 千 顷 = ( $ F { 5 } { 1 2 } $ ) 秒"}, "mask_4a001571-df84-11ed-ac88-4787b0b68593_15.jpg": {"label": "1 3 角 1 元 1 角", "predi": "1 3 角 $ = $ 1 元 1 角"}, "mask_e8deb711-e2a3-11ed-9e55-a520ea92ec5e_41.jpg": {"label": "F { 3 } { 1 0 } = ( $ 0 . 3 $ }", "predi": "F { 3 } { 1 0 } = ( $ 0 . 3 $"}, "mask_f98714a1-c484-11ed-ae1e-7d11c488cff7_10.jpg": {"label": "1 1 0 0 0 . 0 1", "predi": "1 1 / 1 0 0 0 = $ 0 . 0 1 1 $"}, "mask_e65f7e20-dd30-11ed-8d79-4332d1ec06e4_33.jpg": {"label": "4 8 / 6 = $ 7 P 6 $", "predi": "4 8 / 6 = 7 P 6"}, "mask_e6a39e61-c6bb-11ed-b9a6-d9977d9471dd_13.jpg": {"label": "( $ 1 9 0 $ ) * 3 0 = 5 7 0 0", "predi": "( $ 1 9 0 $ ) * 3 0 = 5 7 0 0 0"}, "mask_f74ab241-efff-11ed-951c-9305a4076d73_2.jpg": {"label": "1 F { 3 } [ 5 } $ < $ F { 1 3 } { 6 }", "predi": "1 F { 3 } { 5 } $ < $ F { 1 3 } { 6 }"}, "mask_dc8f6901-d123-11ed-8559-8d0ce8d3b4ac_16.jpg": {"label": "$ 0 . 3 * 2 = 0 . 5 * $", "predi": "$ 0 . 3 * 2 = 0 . 5 *"}, "mask_4c761c61-d75e-11ed-8e69-3d524e5ae4bd_5.jpg": {"label": "F { 9 } { 1 0 } - F { 7 } { 1 7 } = $ F { 2 } { 1 7 } $", "predi": "F { 9 } { 1 7 } - F { 7 } { 1 7 } = $ F { 2 } { 1 7 } $"}, "mask_f76d50c1-c2e1-11ed-99c4-4bcf7d8256fe_18.jpg": {"label": "3 F { 3 } { 5 } = F { ( $ 3 $ } * ( $ 3 $ ) } { 5 } + F { ( $ 3 $ ) } { 5 } = F { ( $ 9 $ ) } { 5 }", "predi": "3 F { 3 } { 5 } = F { ( $ 3 $ ) * ( $ 3 $ ) } { 5 } + F { ( $ 3 $ ) } { 5 } = F { ( $ 9 $ ) } { 5 }"}, "mask_efb0fa51-d379-11ed-9280-c3b565066808_37.jpg": {"label": "F { 3 3 } { 2 6 } = $ F { ( ) } { 1 3 } $", "predi": "F { 3 3 } { 2 6 } = F { ( ) } { 1 3 }"}, "mask_fc3abbd1-e887-11ed-90a5-a1a675244ed2_0.jpg": {"label": "x : 1 . 6 = $ 6 : 8 $", "predi": "x : 1 . 6 = 6 : 8"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_39.jpg": {"label": "1 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1"}, "mask_5f435420-e320-11ed-99f6-81f8f4ba29d8_5.jpg": {"label": "0 = $ 0 . 3 1 $", "predi": "a = $ 0 . 3 m $"}, "mask_dc8f6901-d123-11ed-8559-8d0ce8d3b4ac_7.jpg": {"label": "2 4 F { 1 } { 1 } + F { 5 }", "predi": "2 4 F { 1 } { 6 } + F { 5 } { 4 }"}, "mask_dcae2ff1-f001-11ed-b88b-4537c1d86037_0.jpg": {"label": "$ F { 5 1 } { 1 5 } F { 1 7 } { 5 } $ 3 F { 2 } { 5 } $ < $ F { 1 1 } { 3 } $ F { 5 5 } { 1 5 } $", "predi": "$ F { 5 1 } { 1 5 } - F { 1 7 } { 5 } $ 3 F { 2 } { 5 } $ < $ F { 1 1 } { 3 } $ F { 5 5 } { 1 5 } $"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_20.jpg": {"label": "四 成 五 = ( $ 4 5 $ ) x", "predi": "成 成 五 = ( $ 4 5 $ ) %"}, "mask_0fc3c291-e04b-11ed-843c-9374249f3349_14.jpg": {"label": "6 0 - 1 0 = $ 5 0 $", "predi": "6 0 - 1 0 = $ 5 6 $"}, "mask_fa489201-c557-11ed-94aa-d741bcf0c51c_22.jpg": {"label": "1 4 - 8 = $ 6 $", "predi": "1 4 - 8 = $ 6 $ )"}, "mask_6b59b6c1-e03a-11ed-b909-a9a5088fcc1c_11.jpg": {"label": "9 0 * 1 1 / 3 = $ 3 $", "predi": "9 0 * 1 1 / 3 = $ 3"}, "mask_0ec54261-d597-11ed-aaf2-27000ed0e390_18.jpg": {"label": "F { 3 } { 4 ] * F { 3 } { 8 } = $ F { 9 } { 3 2 } $", "predi": "F { 3 } { 4 } * F { 3 } { 8 } = $ F { 9 } { 3 2 } $"}, "mask_3f807261-eff2-11ed-a976-39ac7fb7ad4c_26.jpg": {"label": "F { 5 } { 1 2 } * 5 = $ F { 1 } { 1 2 } $", "predi": "F { 5 } { 1 2 } / 5 = $ F { 1 } { 1 2 } $"}, "mask_5cca42f1-e643-11ed-830d-cbe284fabe89_20.jpg": {"label": "6 * x * y = $ 6 x y $", "predi": "6 * x * x = $ 6 x % $"}, "mask_4b4e7ba1-d927-11ed-9a9f-093d82bc0be8_20.jpg": {"label": "F { 5 } { 3 } + F { 3 } { 4 } = $ F { 2 9 } { 1 2 } $", "predi": "F { 5 } { 3 } + F { 3 } { 4 } = $ F { 2 9 } { 1 2 9 } $"}, "mask_1b7cee61-e9b8-11ed-b02d-dbc19e7fd366_25.jpg": {"label": "$ 4 $ ) 分 = F { ( $ 4 $ ) } { ( $ 1 0 $ ) 角 = 0 . 4 角", "predi": "$ 4 $ ) 分 = F { ( $ 4 $ ) } { ( $ 1 0 $ ) } 角 = 0 . 4 角"}, "mask_1f64cfc1-cc94-11ed-bb44-3b11b70e4561_2.jpg": {"label": "5 5 1 6", "predi": "5 5 5 4 / 6"}, "mask_e20a9d31-df44-11ed-a77b-876a1ac60f73_23.jpg": {"label": "0 . 1 c m ^ 3 = ( $ 0 . 0 0 0 1 $ ) d m ^ 3", "predi": "0 . 1 c m ^ 3 = ( $ 0 . 0 0 0 1 $ ) d m ^ 3 m ^ 3"}, "mask_05fe6481-d53e-11ed-92e9-a7d3915530df_30.jpg": {"label": "F { 6 } { 3 0 } 1", "predi": "F { 3 } { 6 3 } = $ F { 1 2 } { 2 1 } $"}, "mask_dd413da1-d043-11ed-af06-b70f51ba59d9_15.jpg": {"label": "4 $ * 5 = $ 1 5 1 ( 1 七 张", "predi": "4 5 = $ 1 5 1 ( $ )"}, "mask_d83ca591-c20d-11ed-82ce-e3ec7feaad83_41.jpg": {"label": "F { 5 } { 3 } * ( $ F { 3 } { 5 ) $ ) = 1", "predi": "F { 5 } { 3 } * ( $ F { 3 } { 5 } $ ) = 1"}, "mask_f0a8f391-cd52-11ed-badc-ed5067333532_47.jpg": {"label": "8 3 - 3 0 + 8 = $ 1 1 $", "predi": "3 3 - 3 0 + 8 = $ 1 1 $"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_1.jpg": {"label": "5 8 * 5 0 = $ 2 8 0 0 $", "predi": "5 6 * 5 0 = $ 2 8 0 0 $"}, "mask_fc18d331-ed2d-11ed-b8ce-ff2a340f5f5a_3.jpg": {"label": "八 八 折 = ( $ 8 8 $ ) %", "predi": "公 成 五 = ( $ 8 8 $ ) %"}, "mask_e15e4c71-d845-11ed-a2b8-296f2b952440_23.jpg": {"label": "5 + 角 $ < $ 4 5 0 0 角", "predi": "5 千 克 $ < $ 4 5 0 0 克"}, "mask_de11f8f1-c3ff-11ed-b594-775abc41496b_4.jpg": {"label": "/ 一 = $ 1 P P 3 $", "predi": "/ 7 = $ 1 P P 3 $"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_30.jpg": {"label": "4 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1"}, "mask_3db13d11-db6c-11ed-a160-21a90d504099_5.jpg": {"label": "7 6 角 = ( $ 7 $ ) 元 = ( $ 6 $ ) 角", "predi": "7 6 角 = ( $ 7 $ ) 元 ( $ 6 $ ) 角"}, "mask_fbb1eff1-c0cb-11ed-ad0a-a7fa301928fc_14.jpg": {"label": "1 1 - 6 = 5", "predi": "1 1 - 6 = $ 5 $"}, "mask_f84322d1-cd5f-11ed-9dc9-e932b77baa2c_56.jpg": {"label": "4 * 6 + 1 8 = $ 3 $", "predi": "4 * 6 / 8 = $ 3 $"}, "mask_dddab031-e0e6-11ed-af69-43c679e8fd39_4.jpg": {"label": "F { 5 } { 4 } * 2 = $ 1 2 $", "predi": "F { 7 } { } / 3 = $ 1 2 $"}, "mask_6da934d1-c96c-11ed-981d-a5f7490bd76f_6.jpg": {"label": "5 8 . 6 / 1 0 0 0 = $ 0 $", "predi": "5 8 . 6 / 1 0 0 0 = $ 0 . $"}, "mask_2a792920-e1bf-11ed-b1a2-3de3e1d4c95a_23.jpg": {"label": "7 * ( $ 6 $ ) > 4 8", "predi": "7 * ( $ 6 $ ) < 4 8"}, "mask_5f435420-e320-11ed-99f6-81f8f4ba29d8_16.jpg": {"label": "1 1 = $ ( ) c m 3 $", "predi": "1 1 = ( ) c m ^ 3"}, "mask_3c167811-d867-11ed-8785-ab756f605bb6_5.jpg": {"label": "1 4 * 6 0 = $ 8 4 $", "predi": "1 4 * 6 0 = $ 8 . 4 $"}, "mask_fc35cb81-d21a-11ed-a137-e75cd9a33ba9_7.jpg": {"label": "2 0 m 3 0 m = ( $ 2 3 $ ) m", "predi": "2 0 m 3 0 c m = ( $ 2 3 $ ) m"}, "mask_f080d960-e9b2-11ed-a399-370e7a2d2fec_25.jpg": {"label": "8 4 - 2 1 0 = $ 6 3 1 $", "predi": "8 4 1 - 2 1 0 = $ 6 3 1 $"}, "mask_6a471ab1-ce25-11ed-ae5e-bbbe8c893dff_33.jpg": {"label": "4 8 - 6 - 6 = $ 6 $", "predi": "1 8 - 6 - 6 = $ 6 $"}, "mask_f9551f31-e4f1-11ed-8820-d7c199e649a2_0.jpg": {"label": "1 0 角 = ( $ 1 $ ) 元", "predi": "1 0 角 = ( $ 1 $ ) 万"}, "mask_e2478181-dd07-11ed-8e2b-11b569fbfafe_42.jpg": {"label": "3 3 / 6 = $ 6 $ P $ $", "predi": "3 3 / 6 = $ 6 $ P $ 2 $"}, "mask_1e117591-d3b7-11ed-bab1-51fa7e1d31fb_19.jpg": {"label": "$ 2 1 0 $ ) * 3 = 6 3 0", "predi": "( $ 2 1 0 $ ) * 3 = 6 3 0"}, "mask_4a0f2291-de71-11ed-beac-db5ae962f66f_28.jpg": {"label": "F { 2 } { 5 } * 0 . 0 8 = $ 0 . 0 3 2 $", "predi": "F { 2 } { 5 } * 0 . 0 8 = $ 0 . 0 3 2 2 $"}, "mask_4f3e6761-d69e-11ed-a547-c1c59ae9cb2a_38.jpg": {"label": "5 1 1 5", "predi": "5 5"}, "mask_6a0ab7e1-d5ce-11ed-b7c5-4794a5632b19_38.jpg": {"label": "0 . 0 5 7 m ^ 2 = ( $ 5 . 1 $ ) d m ^ 2", "predi": "0 . 0 5 7 m ^ 2 = ( $ 5 . 7 $ ) d m ^ 2"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_14.jpg": {"label": "六 $ $ 0 + 2", "predi": "0 0 0 0 0 0"}, "mask_df4128f0-d9de-11ed-a03c-b77097dacfcb_19.jpg": {"label": "1 0 0 千 克 + 2 吨 = ( $ 3 $ ) 吨", "predi": "1 0 0 0 千 克 + 2 吨 = ( $ 3 $ ) 吨"}, "mask_0f6bd161-cf9e-11ed-b0e2-15cbdd81d5fa_12.jpg": {"label": "1 2 0 * 2 . 5 * F { 1 } { 3 } = $ 1 0 0 $", "predi": "1 2 0 * 2 . 5 * F { 1 } { 3 } = $ 1 0 0 0 $"}, "mask_d0474a51-d9e3-11ed-84f9-453cfc188b9d_2.jpg": {"label": "5 米 成 1 4", "predi": "5 5 5 5"}, "mask_ee94f100-ea38-11ed-b223-91fb2f3898ac_20.jpg": {"label": "1 5 7 m l = ( $ F { 1 5 7 } { 1 0 0 0 } $ ) L", "predi": "1 5 7 m L = ( $ F { 1 5 7 } { 1 0 0 0 } $ ) L"}, "mask_d058be41-d6a8-11ed-a8c5-f7e0dea328c4_4.jpg": {"label": "1 - F { 1 } { 2 0 } = $ 1 $", "predi": "1 - F { 1 } { 2 0 } ="}, "mask_dc1fdf91-e672-11ed-8ea0-296af31eae69_10.jpg": {"label": "五 厘 分 米 = ( $ 0 $ ) 米 米 米", "predi": "米 万 分 米 = ( $ 0 $ ) 米 万 米"}, "mask_3c4435d1-cc95-11ed-ad7c-bff2706a4cc1_9.jpg": {"label": "6 5 * 4 * 5 = $ 1 3 0 $", "predi": "6 . 5 * 4 * 5 = $ 1 3 0 $"}, "mask_ed20acc1-e1c2-11ed-a05a-470731b22b9f_41.jpg": {"label": "1 7 0 - 8 0 = $ 9 0 $", "predi": "1 7 0 - 8 0 = $ 9 0 0 $"}, "mask_1fc371f1-e437-11ed-8a24-6d1509d42c39_34.jpg": {"label": "八 八 六 3", "predi": "0 / 7 ="}, "mask_03b781b0-e719-11ed-83a7-ed386574381d_19.jpg": {"label": "1 2 0 ° - 9 0 ° = $ 3 0 0 $", "predi": "1 2 0 ° - 9 0 ° = $ 3 0 ° $"}, "mask_f98678e0-ef38-11ed-b440-51876898e120_17.jpg": {"label": "1 3 . 2 + 2 . 8 8 = $ 0 . 0 8 $", "predi": "1 3 . 2 + 2 . 8 8 = $ 8 . 0 8 $"}, "mask_3eefe9b0-d43c-11ed-91fe-e937538423d3_40.jpg": {"label": "1 + 1 1 . 1", "predi": "1 + 1 1 1 1"}, "mask_deb86521-df69-11ed-a883-a93564bc4909_5.jpg": {"label": "0 0 + 5 米 = ( $ 3 0 $ ) 分", "predi": "0 0 + 万 厘 米 = ( $ 3 0 $ ) 4"}, "mask_e68bfdb1-effa-11ed-ae10-5b394dea4c46_7.jpg": {"label": "1 8 6 3 - 4 5 $ $ 2 5 + 1 6 4", "predi": "1 8 6 3 - 4 5 $ < $ 2 5 + 1 6 4"}, "mask_2a38d170-e293-11ed-8b02-0f14cb6d567a_12.jpg": {"label": "4 8 / 0 . 3 / 0 . 4 = $ 4 $", "predi": "4 . 8 / 0 . 3 / 0 . 4 = $ 4 $"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_27.jpg": {"label": "1 1 1 1", "predi": "1 1 1 1 1 1 1"}, "mask_4bde2e70-e41d-11ed-b190-a701d8346fd0_17.jpg": {"label": "4 万 = ( $ 4 0 0 0 0 $ )", "predi": "4 万 = ( $ 4 0 0 0 0 $"}, "mask_2b6fcab1-ce22-11ed-9150-77ae055d59ae_41.jpg": {"label": "2 7 5 / 3 ≈ 9 0", "predi": "2 7 5 / 3 ≈ $ 9 0 $"}, "mask_4cd3dc90-dddc-11ed-9fb4-13954188f6f6_34.jpg": {"label": "1 0 0 0 * 0 . 0 4 / 1 0 0 = 0 . 4 0", "predi": "1 0 0 0 * 0 . 0 4 / 1 0 0 = $ 0 . 4 0 $"}, "mask_4bd75311-deab-11ed-8bb1-1d9304b61fc2_21.jpg": {"label": "7 2 0 + 6 = $ 1 2 0 $", "predi": "7 2 0 / 6 = $ 1 2 0 $"}, "mask_faeec271-ddd4-11ed-ac93-7f3d98aaa450_18.jpg": {"label": "F { 1 } { 1 2 } / 6 = $ F { 1 } { 1 2 } * F { 1 } { 6 } $", "predi": "F { 1 } { 1 2 } / 6 = $ F { 1 } { 1 2 } * F { 1 } { 6 } ="}, "mask_3eafb441-e412-11ed-b742-c9b46b19b088_11.jpg": {"label": "6 * 2 7 $ < $ 2 7 + 7", "predi": "6 + 2 7 $ < $ 2 7 + 7"}, "mask_3eefe9b0-d43c-11ed-91fe-e937538423d3_8.jpg": {"label": "2 八 3 = $ 6 4 $", "predi": "1 9 . 2 / 3 = $ 6 4 $"}, "mask_d76fbb71-eb48-11ed-adb8-4de2322d7653_77.jpg": {"label": "F { 4 5 1 1 0 = $ $", "predi": "F { 4 5 1 1 0 = $ 5 2 $"}, "mask_fe660dd1-c8aa-11ed-81c2-ad6785b79d8f_60.jpg": {"label": "1 0 1 1 1", "predi": "1 1 1 1 1 1"}, "mask_1d6cda11-ca54-11ed-9b4c-71a9c4957a43_28.jpg": {"label": "1 2 m l = ( ) d m ^ 3", "predi": "1 2 m L = ( ) d m ^ 3"}, "mask_fc18d331-ed2d-11ed-b8ce-ff2a340f5f5a_32.jpg": {"label": "三 折 = ( $ 3 0 $ ) %", "predi": "5 成 = ( $ 3 0 $ ) %"}, "mask_2e22bd81-debc-11ed-8cf0-a7cf1a8c1d09_34.jpg": {"label": "F { 2 6 } { 1 8 } = $ F { 1 3 } { 9 } $", "predi": "F { 2 6 } { 1 8 } = $ F { 1 3 } { 9 3 } $"}, "mask_4ad5dcf1-ec72-11ed-a4b6-4dfacb3c2bf4_15.jpg": {"label": "5 . 1 7 - 2 . 1 7 / 1 . 3 = $ 4 . 3 $", "predi": "5 . 1 7 - 2 . 1 7 + 1 . 3 = $ 4 . 3 $"}, "mask_e7fb84e1-e8f7-11ed-ba37-0b3d221c4ab2_30.jpg": {"label": "3 4 * 1 2 = $ 4 0 8 $", "predi": "3 4 * 1 2 = 4 0 8"}, "mask_f6292841-ec73-11ed-8536-83e1d818562a_33.jpg": {"label": "8 . 6 2 - 5 . 9 1 = $ 2 . 7 1 $", "predi": "8 . 6 2 - 5 . 9 1 = $ 2 . 7 1"}, "mask_e781d991-c8a1-11ed-9d0a-95ec6217db79_4.jpg": {"label": "3 2 : F { 8 } { 9 } = $ 4 * F { 9 } { 1 } = 3 6 $", "predi": "3 2 : F { 8 } { 9 } = $ 4 * F { 9 } { } = 3 6 $"}, "mask_4e562911-ec7d-11ed-a017-a16f8243b981_6.jpg": {"label": "3 3 * 3 3 = $ 1 0 8 9 $", "predi": "3 3 * 3 3 = 1 0 8 9"}, "mask_4e1b5001-ed9b-11ed-aeeb-1747dd124717_36.jpg": {"label": "5 0 . 2 4 ≈ ( $ 5 0 . 2 $ )", "predi": "5 0 . 2 4 ≈ ( $ 5 0 . 2 $"}, "mask_3bc07ba1-efdb-11ed-bec6-cfd0c4bf668e_41.jpg": {"label": "F { 1 } { 2 } $ > $ F { 1 } { 3 }", "predi": "F { 1 } { 2 } $ > $ F { 1 { 3 } { 3 } { 3 1 { 3 1 } { 3 1 1 1 1"}, "mask_dd413da1-d043-11ed-af06-b70f51ba59d9_2.jpg": {"label": ". 5 + = $ 0 . 0 $", "predi": "2 1 = $ 0 . $"}, "mask_f6c2f251-e222-11ed-8e62-e7ec90002be1_39.jpg": {"label": "1 1 - 9 =", "predi": "1 1 - 9 = $ 7 $"}, "mask_3f807261-eff2-11ed-a976-39ac7fb7ad4c_13.jpg": {"label": "F { 1 } { 2 } / F { 9 } { 1 1 } = F $ { 1 1 } { 1 8 } $", "predi": "F { 1 } { 2 } / F { 9 } { 1 1 } = $ F { 1 1 } { 1 8 } $"}, "mask_1e0459b1-d9ab-11ed-ba6b-87e3de31db02_13.jpg": {"label": "3 0 c m : ( $ 1 . 5 $ ) k m = 1 : 5 0 0 0 0", "predi": "3 c m : ( $ 1 . 5 $ ) k m = 1 : 5 0 0 0 0"}, "mask_ed4ceaa0-cc7e-11ed-919b-31322e3a9138_24.jpg": {"label": "5 6 + 2 5 = $ 6 1 $", "predi": "3 6 + 2 5 = $ 6 1 $"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_30.jpg": {"label": "F { 7 } { 1 8 } - ( F { 5 } { 1 8 } - F { 1 } { 9 } ) = $ F { 7 } { 1 8 } - F { 5 } { 1 8 } + F { 2 } { 1 8 } = F { 7 - 5 + 2 } { 1 8 } = F { 2 } { 9 } $", "predi": "F { 7 } { 1 8 } - ( F { 5 } { 1 8 } - F { 1 } { 9 } ) = $ F { 7 } { 1 8 } - F { 5 } { 1 8 } + F { 2 } { 1 8 } = F { 7 5 + 2 } { 1 8 } = F { 2 } { 9 } $"}, "mask_3a0be5a1-d051-11ed-ab3b-2f95a99ca10b_16.jpg": {"label": "0 . 3 : 6 = 0 . 5 * 1 0", "predi": "0 . 3 : 6 = 0 . 5 : 1 0"}, "mask_fa1062f1-d9fb-11ed-a5ef-d9502945a53a_17.jpg": {"label": "7 7 - ( $ 1 0 $ ) < 8", "predi": "1 7 - ( $ 1 0 $ ) < 8"}, "mask_2a1f0201-eff3-11ed-aa3f-57649601d057_2.jpg": {"label": "F { 7 } { 9 } - F { 1 } { 3 7 } = $ F { 7 } { 9 } - F { 3 } { 9 } F { 4 } { 9 } $", "predi": "F { 7 } { 9 } - F { 1 } { 3 7 } = $ F { 7 } { 9 } - F { 3 } { 9 } = F { 4 } { 9 } $"}, "mask_ee7b0411-e41c-11ed-9f1c-4f4ff0cd46ea_33.jpg": {"label": "3 7 / 5 = $ 7 P 2 $", "predi": "3 7 / 5 = 7 P 2"}, "mask_dc99f9b1-d85d-11ed-85e1-3d7455d955a8_26.jpg": {"label": "1 0 元 = ( 1 ) 张 5", "predi": "1 0 元 = ( $ 1 $ ) 张 5"}, "mask_dd413da1-d043-11ed-af06-b70f51ba59d9_17.jpg": {"label": "5 = $ 4 $", "predi": "5 = 4"}, "mask_fb390650-db6d-11ed-bd6b-3b67dae566b9_18.jpg": {"label": "8 + 0 . 4 2 = $ 4 . 2 2 $", "predi": ". 8 + 0 . 4 2 = $ 4 . 2 2 $"}, "mask_f32d1680-c8a9-11ed-b1d2-b5c91517fbed_41.jpg": {"label": "( $ 5 0 0 0 $ ) / 5 = 1 0 0", "predi": "( $ 5 0 0 $ ) / 5 = 1 0 0"}, "mask_d738c7f0-e1bb-11ed-ba34-2dd9e9195247_38.jpg": {"label": "3 5 $ 7 $ 2 < 3 5 8 3", "predi": "3 5 $ 7 $ 2 < 3 5 8 1"}, "mask_5a5fe721-ce30-11ed-a033-bf7dfd9d8c37_37.jpg": {"label": "7 . 4 / 2 0 * 1 0 0 = $ 4 0 0 $", "predi": "7 . 4 / 1 0 * 1 0 0 = $ 4 0 0 $"}, "mask_5b4c45b1-ebfa-11ed-9a2c-1fe9fa50ec60_2.jpg": {"label": "2 4 个 月 = ( $ 2 $ ) 年", "predi": "2 4 千 顷 = ( $ 2 $ ) 秒"}, "mask_ec22af01-d851-11ed-b335-2d5f49ab9aa4_33.jpg": {"label": "1 4 5 0 m > 0 . 1 4 5 k m", "predi": "1 4 5 0 m $ > $ 0 . 1 4 5 k m"}, "mask_f1e8ce91-c0c5-11ed-a336-8f731e1e441c_39.jpg": {"label": "7 0 9 / 2 ≈ $ 3 0 $", "predi": "1 0 9 / 2 ≈ $ 3 0 $"}, "mask_e9fc3f00-d38f-11ed-9792-f533b72429c3_13.jpg": {"label": "F { 3 } { 1 0 } = $ F { 9 } { 1 0 0 } $", "predi": "F { 3 } { 1 0 } * F { 3 } { 1 0 } = $ F { 9 } { 1 0 0 } $"}, "mask_e928a051-d358-11ed-8d54-53f46390e02a_5.jpg": {"label": "F { 5 } { 7 } = F { ( 2 0 ) } { 2 8 }", "predi": "F { 5 } { 7 } = F { ( $ 2 0 $ ) } { 2 8 }"}, "mask_5e66ee31-e4de-11ed-a463-a13c60a05132_18.jpg": {"label": "1 0 - F { 1 } { 1 0 } - F { 9 } { 1 0 } = $ 1 0 - ( F { 1 } { 1 0 } + F { 9 } { 1 0 ) = 1 0 $", "predi": "1 0 - F { 1 } { 1 0 } - F { 9 } { 1 0 } = $ 1 0 - ( F { 1 } { 1 0 } + F { 9 } { 1 0 } ) = 1 0"}, "mask_ff3307a1-c250-11ed-8390-15967ebdcb8c_17.jpg": {"label": "6 分 角 米 4 分 + 元 ( $ 6 ° 4 0 $ )", "predi": "6 分 5 米 4 分 + 元 ( $ 6 ° 4 0 $ )"}, "mask_04b31621-ef24-11ed-9699-373a48b7b23f_37.jpg": {"label": "6 0 + ( 4 1 - 4 8 ) = $ 1 0 3 $", "predi": "6 0 + ( 4 1 - 8 8 ) = $ 1 0 3 $"}, "mask_f5b434f1-d53e-11ed-a640-fb9b4f351d70_17.jpg": {"label": "F { 1 0 0 } { 2 0 } = $ { 5 } { 1 } $", "predi": "F { 1 0 0 } { 2 0 } = $ F { 5 } { 1 } $"}, "mask_f91c7340-cc98-11ed-b053-6f185afcadfd_35.jpg": {"label": "8 4 / 9 = $ 9 $ P $ 3 $", "predi": "8 4 / 9 = $ 9 $ P 3"}, "mask_5e39a3e1-eb45-11ed-a3eb-b55b50976d6e_5.jpg": {"label": "F { 2 9 } { 1 0 0 0 } = $ 0 . 0 2 9 $", "predi": "F { 2 9 } { 1 0 0 0 } = $ 0 . 0 2 9 9 $"}, "mask_fc18d331-ed2d-11ed-b8ce-ff2a340f5f5a_0.jpg": {"label": "二 五 折 = ( $ 2 5 $ ) %", "predi": "五 五 五 = ( $ 2 5 $ ) %"}, "mask_e2610201-e35f-11ed-97c6-51e3490845a6_32.jpg": {"label": "F { 7 } { 3 0 } = $ F { 1 4 0 } { 6 0 0 } $", "predi": "F { 7 } { 3 0 } = $ F { 1 4 0 0 0 0 0 0 0 0 0 } $ 6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0"}, "mask_1ca7ceb1-dd13-11ed-ab7f-5b28ff4dd439_43.jpg": {"label": "2 9 / 3 8 = F { ( $ 2 9 $ ) } { $ 3 8 $ }", "predi": "2 9 / 3 8 = F { ( $ 2 9 $ ) } { ( $ 3 8 $ ) }"}, "mask_6e195221-cc95-11ed-b7d6-ab5e25e28f89_20.jpg": {"label": "1 2 : 1 6 = $ 2 $", "predi": "1 2 : 1 6 = $ F { } { 2 } $"}, "mask_f1fd98b1-d606-11ed-aa2b-7dd3371e768d_6.jpg": {"label": "5 0 g = ( $ 0 . 0 5 $ ) k g", "predi": "5 0 g = ( $ 0 . 0 5 $ ) k g g"}, "mask_fa7c6471-e0fc-11ed-8169-412e8bfbbfbf_11.jpg": {"label": "3 5 / * 7 * 5 0 = $ 2 5 $", "predi": "3 5 / 7 * 5 0 = $ 2 5 $"}, "mask_dc8f6901-d123-11ed-8559-8d0ce8d3b4ac_10.jpg": {"label": "$ 4 * 2 = 9 * x $", "predi": "$ 4 * 2 = 9 * x"}, "mask_1c5fa2e1-c53d-11ed-86da-8f0aa91cc175_4.jpg": {"label": "F { 1 2 } { 3 0 } = F { 1 2 $ / $ ( $ 6 $ ) } { 3 0 / $ / $ ( $ 6 $ ) } = F { ( $ 2 $ ) } { ( $ 5 $ ) }", "predi": "F { 1 2 } { 3 0 } = F { 1 2 $ / $ ( $ 6 $ ) } { 3 0 $ / $ ( $ 6 $ ) } = F { ( $ 2 $ ) } { ( $ 5 $ ) }"}, "mask_4fb2b3b1-d543-11ed-b02d-5f55ac67201b_29.jpg": {"label": "1 0 . 2 = $ F { 1 } { 5 } $", "predi": "0 . 2 = $ F { 1 } { 5 } $"}, "mask_d333aa01-d921-11ed-b912-2334d9f34950_1.jpg": {"label": "F { 4 } { 9 } + F { 1 } { 7 }", "predi": "F { 4 } { 9 } + F { } { 7 }"}, "mask_ed36f781-d9f9-11ed-83eb-9fb0cbf0f2d7_11.jpg": {"label": "F { 3 } { ( $ 4 5 0 0 0 $ ) } = F { 1 } { 1 5 0 0 0 }", "predi": "F { 3 } { ( $ 4 5 0 0 $ ) } = F { 1 } { 1 5 0 0 0 }"}, "mask_de7c7121-bffa-11ed-bdf5-b1183f85a2a0_3.jpg": {"label": "0 . 8 7 * 0 . 0 2 = $ 0 . 0 1 7 4 $", "predi": "0 . 8 7 * 0 . 0 2 = $ 0 . 0 7 4 $"}, "mask_f7109ac1-dd0c-11ed-b704-e77afdf7f94f_49.jpg": {"label": "1 1 1 1 1", "predi": "1 0 1 1 1"}, "mask_4bde2e70-e41d-11ed-b190-a701d8346fd0_26.jpg": {"label": "2 5 亿 = ( $ 7 2 5 0 0 0 0 0 0 $ )", "predi": "2 5 亿 = ( $ 7 2 5 0 0 0 0 0 0 $"}, "mask_d712c161-d0fc-11ed-9ce0-f1481015d1f6_22.jpg": {"label": "F { 5 } { 4 } : F { 2 } { 5 } = $ 3 F { 1 } { 8 } $", "predi": "F { 5 } { 4 } / F { 2 } { 5 } = $ 3 F { 1 } { 8 } $"}, "mask_e38ec8a1-ed9c-11ed-9606-3fca84e6de7e_16.jpg": {"label": "F { 1 3 } { 2 4 } - F { 1 } { 8 } = $ F { 1 0 } { 2 4 } = F { 5 } { 1 2 } $", "predi": "F { 1 3 } { 2 4 } - F { 1 } { 8 } = $ F { 1 0 } { 2 4 } = F { 5 } { 4 2 } $"}, "mask_5ab4e041-e7df-11ed-bda6-edb2e93e2c38_41.jpg": {"label": "1 元 = ( $ 1 0 0 $ ) 分", "predi": "1 元 = ( $ 7 0 0 $ ) 分"}, "mask_ed8daa81-c004-11ed-b7d3-c1f0d0dd9d72_0.jpg": {"label": "1 6 - 5 = $ 1 1 $", "predi": "1 6 - 5 = $ 1 1 1 $"}, "mask_3ff7a871-e2b0-11ed-8772-5705ff086af7_23.jpg": {"label": "5 4 * F { 2 } { 9 } =", "predi": "5 . 4 * F { 2 } { 9 } ="}, "mask_e20a9d31-df44-11ed-a77b-876a1ac60f73_3.jpg": {"label": "0 . 2 0 c m ^ 3 = ( $ 0 . 0 0 0 2 $ ) d m ^ 3", "predi": "0 . 2 c m ^ 3 = ( $ 0 . 0 0 0 2 $ ) d m ^ 3"}, "mask_ff3307a1-c250-11ed-8390-15967ebdcb8c_18.jpg": {"label": "9 分 + 米 0 1 分 5 元 ( $ 9 1 0 0 $ )", "predi": "9 分 千 米 0 1 分 5 元 ( $ 9 1 0 0 $ )"}, "mask_e4758391-ea85-11ed-a215-63bf75350653_35.jpg": {"label": "F { 1 } { 4 } + F { 1 } { 9 } + F { 3 } { 4 } = $ 1 F { 1 } { 9 } $", "predi": "F { 1 } { 4 } + F { 1 } { 9 } + F { 3 } { 4 } = $ 1 F { 1 } { 9 } { 9 } { 1 1 } { 9 1 } { 9 1"}, "mask_0bed4231-d813-11ed-aa63-6f481826652e_39.jpg": {"label": "1 5 0 * 3 0 = $ 4 5 0 $", "predi": "1 5 0 * 3 0 = $ 4 5 0 6 $"}, "mask_0f095351-cd5f-11ed-a627-335e1c5ee477_0.jpg": {"label": "1 0 * 0 . 0 7 7 = $ 0 . 7 $", "predi": "1 0 * 0 . 0 7 7 = $ 0 . 7 7 $"}, "mask_fcb20351-ed98-11ed-8270-4ba2950ab50d_39.jpg": {"label": "5 0 = $ 7 5 0 0 $", "predi": "* 5 0 = $ 7 5 0 0 $"}, "mask_9a8ce961-c00c-11ed-9c18-e76740d6f4dd_30.jpg": {"label": "3 5 / 5 = $ 5 $", "predi": "3 5 / 7 = $ 5 $"}, "mask_e23e1ce1-c0d2-11ed-8350-195c0f5bccbd_4.jpg": {"label": "( 1 ) b + a = a + ( $ b $ )", "predi": "( 1 ) b + a = a + ( $ 6 $ )"}, "mask_ea1ea561-db35-11ed-bd2e-ddf5a160d88e_7.jpg": {"label": "F { 7 } { 8 } : F { 2 1 } { 3 2 } = F { 4 } { 3 }", "predi": "F { 7 } { 8 } : F { 2 1 } { 3 2 } = $ F { 4 } { 3 } $"}, "mask_89c81a11-c0a6-11ed-a925-dd223da8c677_28.jpg": {"label": "3 * - 7 . 5 = $ 7 . 5 $", "predi": "3 x - 7 . 5 = 7 . 5"}, "mask_c5c6c331-d795-11ed-a3aa-992765e31b7b_3.jpg": {"label": "F { 4 } { 7 } - F { 2 } { 7 } = F { 2 } { 7 }", "predi": "F { 4 } { 7 } - F { 2 } { 7 } = $ F { 2 } { 7 } $"}, "mask_afef6191-e7d3-11ed-bd30-3bf1fd7a27ca_11.jpg": {"label": "四 = $ ( ) 0 m $", "predi": "c = ( ) c m"}, "mask_19271221-e734-11ed-9e10-3bd4e3b4f6ca_44.jpg": {"label": "1 0 0 - 5 = $ 9 $ = $ $", "predi": "1 0 0 - 5 = $ 9 $ 5"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_40.jpg": {"label": "F { 2 } 1 = $ 4 + 4 $", "predi": "$ F { 1 } { 1 7 } $ = 4 + 4"}, "mask_eed8c811-ef2b-11ed-8912-5bbadea3f215_2.jpg": {"label": "1 2 * F { 3 } { 8 } - $ F { 3 6 } { 8 } $", "predi": "1 2 * F { 3 } { 8 } = $ F { 3 6 } { 8 } $"}, "mask_bf758eb1-e89f-11ed-b47a-177cc8d1e3e2_28.jpg": {"label": "F { 4 } { 5 } * F { 1 5 } { 1 6 } = F { 3 } { 4 }", "predi": "F { 4 } { 5 } * F { 1 5 } { 1 6 } = $ F { 3 } { 4 } $"}, "mask_f2fe95c1-f064-11ed-bb50-4ff54aa55f8d_40.jpg": {"label": "1 0 9 - 5 6 = $ 4 4 $", "predi": "1 0 0 - 5 6 = $ 4 4 $"}, "mask_c7183621-c8b2-11ed-8c68-d1b4066848bf_12.jpg": {"label": "F { 4 } { 5 } * 6 0 = $ 1 6 $", "predi": "F { 4 } { 1 5 } * 6 0 = $ 1 6 $"}, "mask_76f97c21-eda2-11ed-b374-970372aa7a9a_37.jpg": {"label": "( 6 . 8 1 4 1 )", "predi": "6 . 8 + 5 4 2 = $ 7 5 $"}, "mask_88f7bd81-bfb0-11ed-9c69-6730a93f2b9d_29.jpg": {"label": "1 1 ) 1 1", "predi": "( ) + 9 = 1 8"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_19.jpg": {"label": "8 = 4 $ $ 2", "predi": "8 = $ * $ 2"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_19.jpg": {"label": "{ 7 } + } $ < $ 8 + 9", "predi": "1 $ = $ 8 + 9"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_23.jpg": {"label": "8 1 = 9 $ $ 6 1", "predi": "8 1 = 9 $ - $ 6 1"}, "mask_88f7bd81-bfb0-11ed-9c69-6730a93f2b9d_19.jpg": {"label": "8 4 ( $ 1 $ ) = 1 2", "predi": "8 + ( $ 4 $ ) = 1 2"}, "mask_a47a0c61-d52d-11ed-8d67-cd2864e724ac_7.jpg": {"label": "F { 1 } { 2 } * F { 1 } { 2 } = F { 1 } { 4 }", "predi": "F { 1 } { 2 } * F { 1 } { 2 } = $ F { 1 } { 4 } $"}, "mask_4f926e11-eef8-11ed-a50a-95597b8116e6_8.jpg": {"label": "F { x - 3 } { 1 . 5 } = $ F { 8 } { 2 . 4 } $", "predi": "F { x - 3 } { 1 . 5 } = $ F { 8 } { 2 . 4 }"}, "mask_e60f33d1-c31c-11ed-9962-ed6e8b90c66e_39.jpg": {"label": "6 3 0 / 6 = $ 1 0 5 $", "predi": "6 3 0 / 6 = $ 1 5 $"}, "mask_1eee7cb1-ed61-11ed-aa27-eba7bfcf8b22_18.jpg": {"label": "5 0 元 = ( $ 3 $ ) 张 1 0 元 + 2 张 5 元 +", "predi": "5 0 元 = ( $ 3 6 $ ) 张 1 0 元 + 2 张 5 元 +"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_31.jpg": {"label": "1 = - 十", "predi": "1 - - 4"}, "mask_2bbd19f0-db60-11ed-94bc-27939ff3335f_42.jpg": {"label": "5 0 - 4 3 2 = $ 1 0 8 $", "predi": "5 4 0 - 4 3 2 = $ 1 0 8 $"}, "mask_2dcd9841-e5c5-11ed-8f55-9718bf1ca423_34.jpg": {"label": "4 . 2 7 m = $ ( ) 0 $", "predi": "4 . 2 7 m = ( ) d m"}, "mask_afef6191-e7d3-11ed-bd30-3bf1fd7a27ca_4.jpg": {"label": "a = $ 0 . 6 m $", "predi": "a = 0 . 6 m"}, "mask_55b413f1-da05-11ed-b765-bd591c6ece53_32.jpg": {"label": "0 . 0 5 1 ^ 2 =", "predi": "0 . 0 5 m ^ 2 ="}, "mask_5f4952f1-e7d3-11ed-92b3-c99ebdbf361f_3.jpg": {"label": "1 . 4 / F { 1 } { 1 0 }", "predi": "1 . 4 / F { 1 } { 1 0 0 } ="}, "mask_d39a8661-c25d-11ed-9313-0be8c6ff5802_3.jpg": {"label": "F { 1 3 } { 2 0 } * 0 . 1 5 =", "predi": "F { 1 3 } { 2 0 } / 0 . 1 5 ="}, "mask_d7220081-d787-11ed-9f08-b1d4a418aed0_20.jpg": {"label": "4 9 + 6 = $ 5 5 $", "predi": "4 + 6 = $ 5 5 $"}, "mask_9e321fa1-cede-11ed-85a4-cf1cd11915cb_30.jpg": {"label": "y * 2 = $ 2 y $", "predi": "* 2 = $ 2 4 $"}, "mask_de76c301-ddce-11ed-bfec-3160512dd987_54.jpg": {"label": "7 2 / 2 * 4", "predi": "7 2 / * 2 * 4 ="}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_25.jpg": {"label": "8 - 9 - 8", "predi": "8 - 9 - 8 4"}, "mask_10ac57f0-ea8a-11ed-8ea0-2726ffd31f5c_5.jpg": {"label": "3 3 * 3 3 = $ 1 0 8 9 $", "predi": "3 3 * 3 3 = 1 0 8 9"}, "mask_a8661011-dea3-11ed-b409-2ff7d2e88091_5.jpg": {"label": "0 . 9 3 ≈ 0 . 9", "predi": "0 . 9 3 ≈ $ 0 . 9 $"}, "mask_dab20130-e9b3-11ed-a5e2-db36ea36819f_8.jpg": {"label": "( ) * F { 1 } { } = F { 5 } { 1 8 } * F { 1 } { 2 }", "predi": "( ) * F { 1 } { 9 } = F { 5 } { 1 8 } * F { 1 } { 2 }"}, "mask_ab81d771-ddd1-11ed-aab7-61f8d8762d95_16.jpg": {"label": "9 9 角 米 $ > $ 1 分 米", "predi": "9 9 厘 米 $ > $ 1 分 米"}, "mask_436df7c0-ea35-11ed-b39d-7d59e631ca90_1.jpg": {"label": "F { 1 } { 3 } - x = $ F { 1 } { 4 } $", "predi": "F { 1 } { 3 } - x = F { 1 } { 4 }"}, "mask_a6752a61-ce2d-11ed-b168-d97fbc0b6edf_41.jpg": {"label": "2 0 . 0 1 4 = $ 2 0 $ + $ 0 . 0 4 $ + $ 0 . 0 0 1 $", "predi": "2 0 . 0 4 1 = $ 2 0 $ + $ 0 . 0 4 $ + $ 0 . 0 0 1 $"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_18.jpg": {"label": "6 = 8 $ $ 8", "predi": "6 = 8 $ * $ 8"}, "mask_5d4fcda1-efba-11ed-828b-296aa3f01d24_7.jpg": {"label": "( $ 8 0 0 0 $ ) - 6 0 0 0 = 2 0 0 0", "predi": "( $ 8 0 0 $ ) - 6 0 0 0 = 2 0 0 0"}, "mask_ff75ea11-d485-11ed-a8dd-a560d74dfb7c_5.jpg": {"label": "7 0 * 6 - 2 3 0", "predi": "7 0 * 6 - 2 3 0 :"}, "mask_e26a41f0-e4ed-11ed-8c22-a53ac075c047_42.jpg": {"label": "8 $ > $ - 3", "predi": "3 $ > $ - 3"}, "mask_1ca7ceb1-dd13-11ed-ab7f-5b28ff4dd439_17.jpg": {"label": "2 5 c m = F { ( $ 2 5 $ ) } { $ 1 0 0 $ ) } m", "predi": "2 5 c m = F { ( $ 2 5 $ ) } { ( $ 1 0 0 $ ) } m"}, "mask_dea7f0a1-d4c4-11ed-9991-cd9f222490b6_16.jpg": {"label": "/ 1 2 5 = $ ( $", "predi": "/ 1 2 5 = ( $ 0 $"}, "mask_2d8e2a81-d5fc-11ed-85d6-13127a39c17e_30.jpg": {"label": "- F { 1 } { 5 } $ < $ - 1 . . 2 %", "predi": "- F { 1 } { 5 } $ < $ - 1 . 2 %"}, "mask_edb967b1-ee62-11ed-9772-a546d28586eb_36.jpg": {"label": "F [ 4 } { 9 } - F { 1 } { 4 } = $ F { 1 6 } { 3 6 } - F { 9 } { 3 6 } = F { 7 } { 3 6 } $", "predi": "F { 4 } { 9 } - F { 1 } { 4 } = $ F { 1 6 } { 3 6 } - F { 9 } { 3 6 } = F { 7 } { 3 6 } $"}, "mask_fa37e861-d47a-11ed-a2d8-c1d651968078_38.jpg": {"label": "7 0 1 八 7", "predi": "7 0 角 7 角"}, "mask_03b36031-cc84-11ed-8608-85b3a488b795_7.jpg": {"label": "1 + 2 + 3 + . . . + 3 9 = $ 8 0 0 $", "predi": "1 + 2 + 3 + P + 3 9 = $ 8 0 0 $"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_20.jpg": {"label": "成 = ( $ 4 0 $ ) %", "predi": "成 = ( $ 1 0 $ ) %"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_26.jpg": {"label": "7 } 1 1 1 1", "predi": "1 1 1 1 1 1 1"}, "mask_fe80c750-eb10-11ed-afef-850259460a0f_9.jpg": {"label": "F { 7 } { 1 5 } + F { 2 } { 1 5 } = $ F { } { 5 } $", "predi": "F { 7 } { 1 5 } + F { 2 } { 1 5 } = $ F { 5 } { 5 } $"}, "mask_dc62d251-e57d-11ed-a52a-850d2fc4cb10_16.jpg": {"label": "4 吨 $ = $ 4 0 0 0 千 克", "predi": "4 吨 $ = $ 4 0 0 0 克"}, "mask_dc8f6901-d123-11ed-8559-8d0ce8d3b4ac_1.jpg": {"label": "( F { 4 } { 5 } + F { 1 } { 4 } ) / ( F { 3 } { 1 0 } + F { 7 } { 1 0 } )", "predi": "( F { 4 } { 5 } + F { 1 } { 4 } ) ) ( F { 3 } { 1 0 } + F { 7 } { 1 0 } )"}, "mask_4c4bd601-c721-11ed-9b76-bfeb8c973b44_21.jpg": {"label": "0 / 1 3 * 3 = $ 1 0 $", "predi": "0 / 1 3 * 3 = $ 1 0 0 $"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_22.jpg": {"label": "三 = $ 5 $", "predi": "= $ 5 $"}, "mask_f84322d1-cd5f-11ed-9dc9-e932b77baa2c_18.jpg": {"label": "6 3 / 7 / 3 = $ 2 $", "predi": "6 3 / 7 / 3 = $ 3 $"}, "mask_e424d371-d9da-11ed-b8f5-21d006063f91_6.jpg": {"label": "F { 1 7 } { 1 9 } = $ F { 1 7 * 2 } { 1 9 * 2 } = F { 3 4 } { 3 8 } $", "predi": "F { 1 7 } { 1 9 } = $ F { 1 7 * 2 } { 1 9 * 2 } = F { 3 + } { 3 8 } $"}, "mask_4f5a3ae0-ea75-11ed-903b-b73ad8ac8cb9_7.jpg": {"label": "1 0 元 $ > $ 9 角 1 9 分", "predi": "1 0 元 $ > $ 9 角 9 分"}, "mask_5ef29bb0-c58a-11ed-ab4f-3f8da19e6b9d_16.jpg": {"label": "F { 3 } { 1 0 0 0 } = $ 0 . 0 3 7 $", "predi": "F { 3 7 } { 1 0 0 0 } = $ 0 . 0 3 7 $"}, "mask_1e925481-e190-11ed-bd28-6fd2da8d9e76_42.jpg": {"label": "1 F { 4 } { 5 } $ = $ 2 . . 8", "predi": "2 F { 4 } { 5 } $ = $ 2 . 8"}, "mask_3eefe9b0-d43c-11ed-91fe-e937538423d3_6.jpg": {"label": "6 . 1 1 ( 1 1 1", "predi": "6 . 1 1 1 1 1"}, "mask_5e87a9b1-e4eb-11ed-a16d-d77d12017e60_3.jpg": {"label": "0 . 8 = $ F { 4 ] { 5 } $", "predi": "0 . 8 = $ F { 4 } { 5 } $"}, "mask_1c7324d1-ed53-11ed-918b-398e7122eb1f_29.jpg": {"label": "F { 7 } { 2 0 } = ( $ 0 . 3 5 $ )", "predi": "F { 7 } { 2 0 } = ( $ 0 . 3 5 $"}, "mask_d333aa01-d921-11ed-b912-2334d9f34950_25.jpg": {"label": "F { 1 4 } * { 5 } * F { 1 5 } { 2 8 } =", "predi": "F { 1 4 } { 5 } * F { 1 5 } { 2 8 } ="}, "mask_2dc72cb1-db5e-11ed-af37-599fd144b11a_24.jpg": {"label": "5 0 0 m l = ( $ 0 . 5 $ ) d m ^ 3", "predi": "5 0 0 m L = ( $ 0 . 5 $ ) d m ^ 3"}, "mask_d8799cf1-d6a5-11ed-a387-771b73519c27_8.jpg": {"label": "1 - F { 4 } { 6 } = $ F { } { 6 } $", "predi": "1 - F { 4 } { 6 } = $ 6 $"}, "mask_2c4ea711-e41d-11ed-8fa4-8bdfce412ea8_0.jpg": {"label": "三 折 = ( $ 3 0 $ ) %", "predi": "5 五 = ( $ 3 0 $ ) %"}, "mask_e0ae4421-d786-11ed-85e6-6332aaa3c89c_27.jpg": {"label": "3 = $ 1 . $", "predi": "3 = $ 1 6 $"}, "mask_fbc4ec91-e637-11ed-9d6c-3d1a33781e77_20.jpg": {"label": "F { 1 } { 5 } + F { 5 } { 6 } = $ F { 6 } { 3 0 } + F { 2 5 } { 3 0 } = F { 3 1 } { 3 0 } $", "predi": "F { 1 } { 5 } + F { 5 } { 6 } = $ F { 6 } { 3 0 } + F { 2 5 } { 3 0 } = F { 3 1 } { 3 0 } $ 1 0 0 } { 3 0 1 } { 3 0 1"}, "mask_0aef3341-c7e5-11ed-90a1-e7985ba124e7_1.jpg": {"label": "6 4 - 8 - 8 - 8 - 8 - 8 - 8 - 8 - 8 = $ 0 $", "predi": "6 4 - 8 - 8 - 8 - 8 - 8 - 8 - 8 - 8"}, "mask_e2c25361-d8c0-11ed-88b8-934e22185e85_46.jpg": {"label": "$ 4 8 5 5 $ 9 0", "predi": "$ 4 2 5 4 0 0 $"}, "mask_6bd7f201-ca42-11ed-9184-4307e57046df_11.jpg": {"label": "6 6 c m ^ 3 = ( $ 0 . 0 6 6 $ ) d m", "predi": "6 6 c m ^ 3 = ( $ 0 . 0 6 6 $ ) d m ^ 3"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_11.jpg": {"label": "七 三 $ $", "predi": "= $ 1 0 2 $"}, "mask_d36e8a20-d210-11ed-b0a7-755723efd073_26.jpg": {"label": "1 角 9 米 = ( $ 1 9 $ ) 分", "predi": "1 角 9 分 = ( $ 1 9 $ ) 分"}, "mask_1b96b631-f082-11ed-bcc7-f39e92579638_16.jpg": {"label": "F { 8 } { 5 } =", "predi": "F { 5 } { 5 } ="}, "mask_4e1b5001-ed9b-11ed-aeeb-1747dd124717_30.jpg": {"label": "F { 1 } { 3 } ≈ ( $ 0 . 3 $ )", "predi": "F { 1 } { 3 } ≈ ( $ 0 . 3 $"}, "mask_6dd2f001-dd0c-11ed-9d84-d32c0689dfde_0.jpg": {"label": "6 . 4 亿 = ( )", "predi": "6 . 4 亿 = ("}, "mask_3e9e6e01-c96a-11ed-8201-4d14fed27d4f_35.jpg": {"label": "6 1 2 / 3 = $ 2 0 4 $", "predi": "6 1 2 / 3 = $ 2 7 4 $"}, "mask_0bea1ad0-dd2a-11ed-ae8b-159c239e60c0_25.jpg": {"label": "2 8 / 1 4 =", "predi": "2 8 + 1 4 ="}, "mask_1a506ee1-e0fd-11ed-8169-412e8bfbbfbf_42.jpg": {"label": "7 9 * 4 ≈ . $ 3 2 0 $", "predi": "7 9 * 4 ≈ : $ 3 2 0 $"}, "mask_2de27e11-c30e-11ed-97ba-275b68e5e2a6_0.jpg": {"label": "1 2 * 5 - 6 = $ 1 $", "predi": "1 2 < 5 - 6 = $ 1 $"}, "mask_2eb2b0e1-db2c-11ed-93f2-4da729f169a6_27.jpg": {"label": "F { 7 0 } { 2 5 } = $ F { 1 4 } { } = F { 1 4 } { 5 } $", "predi": "F { 7 0 } { 2 5 } = $ F { 1 4 } { 2 5 } = F { 1 4 } { 5 } $"}, "mask_df1de3d1-cef9-11ed-b4b4-1bb6f561b485_45.jpg": {"label": "6 1 - 8 角 6", "predi": "5 1 - 5 1 - 5"}, "mask_fe80c750-eb10-11ed-afef-850259460a0f_17.jpg": {"label": "F { 2 8 } { 4 1 } + F { 3 } { 4 1 } = $ F { 3 1 } { 4 1 } $", "predi": "F { 2 8 } { 4 1 } + F { 3 } { 4 1 } = $ F { 3 1 1 } { 4 1 } $"}, "mask_1fcd82d1-c524-11ed-81dc-f92359da3700_24.jpg": {"label": "( $ 1 . 7 6 3 $ ) < 1 . 7 6 4 < ( $ 1 . 7 6 5 $ )", "predi": "( $ 1 . 7 6 5 $ ) < 1 . 7 6 4 < ( $ 1 . 7 6 5 $ )"}, "mask_2e7a6eb1-dea1-11ed-a9f7-b385eb81922e_55.jpg": {"label": "5 - 2 0 =", "predi": "1 5 - 2 0 ="}, "mask_f2584351-d47b-11ed-9a47-8b6f33680ffa_37.jpg": {"label": "F { 2 } { 9 } + + F { 1 } { 8 } = $ F { 2 5 } { 7 2 } $", "predi": "F { 2 } { 9 } + F { 1 } { 8 } = $ F { 2 5 } { 7 2 } $"}, "mask_e4dd60f1-deaf-11ed-8fdd-a93b1b770418_1.jpg": {"label": "2 6 0 m ^ 3 = F { ( ) } { ( ) } d m ^ 3", "predi": "2 6 0 c m ^ 3 = F { ( ) } { ( ) } d m ^ 3"}, "mask_dccbc200-e4e8-11ed-9ae4-595e2d78ad4f_26.jpg": {"label": "5 . 0 2 m ^ 2 = $ ( ) m ^ 2 ( ) d m ^ 2 $", "predi": "5 . 0 2 m ^ 2 = ( ) m ^ 2 ( ) d m ^ 2"}, "mask_85835801-ceea-11ed-ba76-bf49b0c46e99_39.jpg": {"label": "1 { 3 }", "predi": "F { 3 }"}, "mask_93550ba1-e370-11ed-b62a-271c54bc3310_30.jpg": {"label": "1 2 . 0 1 9 4 ≈ 1 2", "predi": "1 2 . 0 1 9 4 ≈ $ 1 2 $"}, "mask_d3c51d31-e98b-11ed-8528-61fbf39c7888_16.jpg": {"label": "3 0 7 = - 2 9 9 + ( $ 8 $ )", "predi": "3 0 7 = 2 9 9 + ( $ 8 $ )"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_6.jpg": {"label": "6 = 9 - 4", "predi": "6 - 9 - 4 4"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_50.jpg": {"label": "2 4 $ > $ 四 + 4", "predi": "2 4 $ > $ + 4"}, "mask_8b4c1480-ee65-11ed-a0ac-d5695dcde8d4_30.jpg": {"label": "6 + 3 + 6 = $ 7 6 $", "predi": "6 7 + 3 + 6 = $ 7 6 $"}, "mask_6d654da1-cb93-11ed-a08a-e946fb5ce8c1_17.jpg": {"label": "F { 3 } {", "predi": "F { 3 } { }"}, "mask_afef6191-e7d3-11ed-bd30-3bf1fd7a27ca_2.jpg": {"label": "0 毫 角 = $ ( ) 0 2 $", "predi": "0 米 角 = ( ) c m"}, "mask_ccc94381-ce24-11ed-ae5e-bbbe8c893dff_20.jpg": {"label": "2 + 9 = 1 1", "predi": "2 + 9 = $ 1 1 $"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_31.jpg": {"label": "9 = - 1 $ * $ 9", "predi": "9 = 1 $ * $ 9"}, "mask_ee5fe771-e73c-11ed-88d0-2d807367a8f2_6.jpg": {"label": "7 5 m l = ( $ 7 5 $ ) c m ^ 3", "predi": "7 5 m L = ( $ 7 5 $ ) c m ^ 3"}, "mask_235bc631-c4c2-11ed-95f7-71d52b2784e3_2.jpg": {"label": "F { 4 } { 9 } - x = $ F { 1 } { 1 1 } $", "predi": "F { 4 } { 9 } - x = F { 1 } { 1 1 }"}, "mask_79cded21-eff8-11ed-a653-9929e98a0b5e_31.jpg": {"label": "6 4 - ( 4 2 + 3 ) = $ 1 4 $", "predi": "6 4 - ( 4 7 + 3 ) = $ 1 4 $"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_5.jpg": {"label": "8 1 = 9 + ( $ 8 $ )", "predi": "8 1 = 9 + ( $ 8 2 $ )"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_39.jpg": {"label": "1 7 = - 4 $ $ 1", "predi": "1 7 = 4 $ + $ 1 1"}, "mask_436df7c0-ea35-11ed-b39d-7d59e631ca90_5.jpg": {"label": "F { 4 } { 1 4 } + F { 2 }", "predi": "F { 4 } { 1 1 } + F { 2 } { 3 } ="}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_11.jpg": {"label": "2 1 = ( 6 + ( $ $ )", "predi": "2 1 = 6 + ( $ 2 $ )"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_28.jpg": {"label": "F { 1 } { 1 4 } + F { 1 } { 2 } + F { 1 } { 7 } = $ F { 1 } { 1 4 } + F { 7 } { 1 4 } + F { 2 } { 1 4 } = F { 1 + 7 + 2 } { 1 4 } = F { 5 } { 7 } $", "predi": "F { 1 } { 1 4 } + F { 1 } { 2 } + F { 1 } { 7 } = $ F { 1 } { 1 4 } + F { 7 } { 1 4 } + F { 2 } { 1 4 } = F { 1 + 7 + 2 } { 1 4 } = F { 5 } { 7 }"}, "mask_fd2ec491-d6c7-11ed-8d26-95957dea9f25_16.jpg": {"label": "4 9 / 6 = $ 8 P 1 $", "predi": "4 9 / 6 = $ 8 - 1 $"}, "mask_1fcd82d1-c524-11ed-81dc-f92359da3700_14.jpg": {"label": "( $ 6 . 8 6 $ ) < 6 . 8 7 < ( $ 6 . 8 8 $ )", "predi": "( $ 6 . 8 6 $ ) < 6 . 8 7 < ( $ 6 . 8 8 $"}, "mask_1bd14c01-e1cd-11ed-bd8a-b9aea5ee9ad1_11.jpg": {"label": "3 5 / 4 = $ 8 P 3 $", "predi": "3 5 / 4 = 8 P 3"}, "mask_eca0bf21-d6d1-11ed-a57c-5ba9d58fc7c9_44.jpg": {"label": "1 m ^ 3 = ( $ 1 0 0 0 0 0 0 $ ) c m ^ 3", "predi": "1 m ^ 3 = ( $ 1 0 0 0 0 0 0 $ ) k m ^ 3"}, "mask_d9fbc371-dab3-11ed-9fed-6def9eb89147_0.jpg": {"label": "4 六 2 0 > 2 5", "predi": "4 5 2 2 2 2"}, "mask_1f520ae1-cee0-11ed-9835-2789202e11c4_42.jpg": {"label": "3 5 厘 张 + 6 5 厘 米 = $ 1 米 $", "predi": "3 5 厘 米 + 6 5 厘 米 = $ 1 米 $"}, "mask_d036ad81-e370-11ed-a281-c9b5564f5a61_29.jpg": {"label": "7 m l = ( $ 0 . 0 0 7 $ ) L", "predi": "7 m L = ( $ 0 . 0 0 7 $ ) L"}, "mask_fe80c750-eb10-11ed-afef-850259460a0f_21.jpg": {"label": "F { 7 } { 4 } + F { 3 } { 4 } = 2 2", "predi": "F { 7 } { 4 } + F { 3 } { 4 } = $ 2 F { } { 2 } $"}, "mask_1e8ee371-ed54-11ed-af4f-1b6df7ae09e2_4.jpg": {"label": "5 1 . 2 0 9 元 ≈ ( $ 5 1 . 2 1 $", "predi": "5 1 . 2 0 9 ≈ ( $ 5 1 . 2 1 $"}, "mask_6a2bdc21-cf86-11ed-b16e-716b3d88ca57_7.jpg": {"label": "a * a = $ a ^ 2 $", "predi": "a * a = a ^ 2"}, "mask_e14d5461-eb3c-11ed-b35b-038568acb555_38.jpg": {"label": "6 * 4 + 2 = $ 2 6 $", "predi": "6 * 4 + 2 = 2 6"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_0.jpg": {"label": "二 成 五 = ( $ 2 5 $ ) %", "predi": "成 成 五 = ( $ 2 5 $ ) %"}, "mask_d678c801-deb5-11ed-81ea-3d89c6fa1628_8.jpg": {"label": "3 3 / ( 1 0 0 - 3 3 ) = $ F { 3 3 } { 6 7 } $", "predi": "3 3 / ( 1 0 0 - 3 3 ) = $ F { 3 3 3 } { 6 7 } $"}, "mask_1c903561-e421-11ed-b442-936a5a5c5800_31.jpg": {"label": "3 5 / 4 = $ 8 P 3 $", "predi": "3 5 / 4 = 8 P 3"}, "mask_5cca42f1-e643-11ed-830d-cbe284fabe89_15.jpg": {"label": "y - 3 * x = $ y - 3 x $", "predi": "a - 3 * x = $ a - 3 x $"}, "mask_f6c2f251-e222-11ed-8e62-e7ec90002be1_20.jpg": {"label": "8 + 8 = $ 1 6 $", "predi": "8 + 8 = $ 1 0 $"}, "mask_4e1b5001-ed9b-11ed-aeeb-1747dd124717_5.jpg": {"label": "5 . 4 9 9 ≈ ( $ 5 . 5 $ )", "predi": "5 . 4 9 9 ≈ ( $ 5 . 5 $"}, "mask_4fb2b3b1-d543-11ed-b02d-5f55ac67201b_22.jpg": {"label": "0 . 3 5 = $ { 7 } { 2 0 } $", "predi": "0 . 3 5 = $ F { 7 } { 2 0 } $"}, "mask_2a1f0201-eff3-11ed-aa3f-57649601d057_38.jpg": {"label": "F { 1 } { 8 } + F { 1 } { 1 0 } = $ F { 1 0 } { 8 0 } + F { 8 } { 8 0 } = F { 9 } { 4 0 } $", "predi": "F { 1 } { 8 } + F { 1 } { 1 0 } = $ F { 6 } { 8 0 } + F { 8 } { 8 0 } = F { 9 } { 4 0 } $"}, "mask_1fce8271-e35b-11ed-a0b0-17b8cfd28887_9.jpg": {"label": "F { 2 } { 3 } / F { 5 } { 6 } = $ F { 2 } { 1 } * F { 2 } { 5 } = F { 4 } { 5 } $", "predi": "F { 2 } { 3 } / F { 5 } { 6 } = $ F { 2 } { 3 } * F { 2 } { 5 } = F { 4 } { 5 } $"}, "mask_e1c2cb71-cf01-11ed-90ca-73f9290a83b4_43.jpg": {"label": "4 5 / 9 = $ 2 . 0 $", "predi": "4 5 / 9 = $ 2 . $"}, "mask_0ed286d1-e1e2-11ed-a3a7-5b542e505f50_34.jpg": {"label": "9 / F { 3 } { } = $ 3 * F { 4 } { } = 1 2 $", "predi": "9 / F { 3 } { } = $ 9 * F { 4 } { } = 1 2 $"}, "mask_faac9cb1-ebf3-11ed-8e02-7125afa8de5c_24.jpg": {"label": "5 7 7 - 4 5 3 - $ 1 2 4 $", "predi": "5 7 7 - 4 5 3 = $ 1 2 4 $"}, "mask_ec687751-d861-11ed-b94c-3f6901cf4bd8_17.jpg": {"label": "F { 3 } { 1 3 } * F { 1 3 } { 1 5 } =", "predi": "F { 3 } { 1 3 } * F { 1 3 } { 1 5 } = )"}, "mask_fd2ec491-d6c7-11ed-8d26-95957dea9f25_32.jpg": {"label": "4 7 / 9 = $ 5 P 2 $", "predi": "4 7 / 9 = $ 5 - 2 $"}, "mask_6bbde7f0-e439-11ed-b301-8764620593f4_8.jpg": {"label": "2 F { 4 } { 5 } = ( $ 2 . 8 $ )", "predi": "2 F { 4 } { 5 } = ( $ 2 . 8 $"}, "mask_f74ab241-efff-11ed-951c-9305a4076d73_27.jpg": {"label": "F { 6 } { 1 1 } $ < $ F { 8 } { 1 3 }", "predi": "F { 6 } { 1 1 } $ < $ F { 8 } { 1 3 } 3 }"}, "mask_c0edbdc1-cd2a-11ed-a960-f7c70e2ccce1_13.jpg": {"label": "5 5 % x + 2 5 % x = $ 1 . 6 $", "predi": "5 5 % x + 2 5 % x = 1 . 6"}, "mask_aefa8c91-d222-11ed-b631-754de6a212c2_9.jpg": {"label": "角 = ( ) d m 3", "predi": "角 = ( ) d m ^ 3"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_5.jpg": {"label": "6 - 2 - 4 1", "predi": "6 - 2 - 4 4"}, "mask_7f1647a1-d10d-11ed-965d-d729da99a3a1_17.jpg": {"label": "F { } { 0", "predi": "F { } { }"}, "mask_d7220081-d787-11ed-9f08-b1d4a418aed0_19.jpg": {"label": "4 + 6 9 = $ 7 3 $", "predi": "+ 6 9 = $ 7 3 $"}, "mask_dac76e31-d9be-11ed-b0e0-459043f77d4a_4.jpg": {"label": "F { 3 } { 4 } - F { 1 } { 4 } = F { 1 } { 2 }", "predi": "F { 3 } { 4 } - F { 1 } { 4 } = $ F { 1 } { 2 } { 1 } { 1 1 } { 1 1 } { 1 1"}, "mask_235bc631-c4c2-11ed-95f7-71d52b2784e3_24.jpg": {"label": "1 8 / 0 . 2 = $ x $", "predi": "1 8 / 0 . 2 = $ 4 $"}, "mask_b96cf8c1-ed70-11ed-8c25-8b4585e61b92_15.jpg": {"label": "1 2 1 0", "predi": "1 1 2 = $"}, "mask_f2fe95c1-f064-11ed-bb50-4ff54aa55f8d_13.jpg": {"label": "+ 4 5 } 7 + * 5 4 = $ 1 0 0 $", "predi": "+ 3 5 + 5 4 = $ 1 0 0 $"}, "mask_f8d484a0-cab2-11ed-8d8a-4dd3c8bd8206_8.jpg": {"label": "F { 1 } { 2 } - F { 1 } { 3 } = F { 1 } { 6 }", "predi": "F { 1 } { 2 } - F { 1 } { 3 } = $ F { 1 } { 6 } $"}, "mask_32f39bf1-debb-11ed-855b-d77f90825804_39.jpg": {"label": "9 0 = ( $ 9 $ ) 角", "predi": "9 0 分 = ( $ 9 $ ) 角"}, "mask_8feeeb21-de6b-11ed-8646-33765f8ba913_41.jpg": {"label": "0 . 0 0 1 = $ F { 1 } { 1 0 0 0 } $", "predi": "0 . 0 0 1 = $ F { 1 } { 1 0 0 } $"}, "mask_e8d707c1-c83d-11ed-ac88-81821b2898bf_19.jpg": {"label": "3 6 9 / 3 = $ 4 2 3 } $", "predi": "3 6 9 / 3 = $ 1 2 3 $"}, "mask_436df7c0-ea35-11ed-b39d-7d59e631ca90_15.jpg": {"label": "5 1 毫 5 1 . 5", "predi": "5 1 5 1"}, "mask_2e64da61-c343-11ed-a657-f5a6b60861f4_4.jpg": {"label": "1 2 > 1 6 - ( $ 5 $ )", "predi": "1 2 > 1 6 - ( $ 5 $"}, "mask_4f926e11-eef8-11ed-a50a-95597b8116e6_2.jpg": {"label": "F { 1 } { 5 } * 2 . 5 = 2 : ( )", "predi": "F { 1 } { 5 } : 2 . 5 = 2 : ( )"}, "mask_9e321fa1-cede-11ed-85a4-cf1cd11915cb_25.jpg": {"label": "x * y = $ x y $", "predi": "x * x = x x"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_20.jpg": {"label": "( 8 - - 8 1", "predi": "8 - - 8 1"}, "mask_7ef42c60-bfc4-11ed-8d6a-1304f93d1dd9_20.jpg": {"label": "4 3 * 3 = $ 2 9 $", "predi": "4 3 * 3 = $ 1 2 9 $"}, "mask_fc414991-d6c2-11ed-84b2-0960ca7d0f2e_18.jpg": {"label": "F { 4 } { 5 } - F { 2 } { 5 } = F { 2 } { 5 }", "predi": "F { 4 } { 5 } - F { 2 } { 5 } = $ F { 2 } { 5 } $"}, "mask_2dcd9841-e5c5-11ed-8f55-9718bf1ca423_37.jpg": {"label": "0 . 4 0 4 t = $ ( ) k g $", "predi": "0 . 4 0 4 t = ( ) k g"}, "mask_2e921f31-e7d2-11ed-92b3-c99ebdbf361f_11.jpg": {"label": "F { 1 3 } { 1 } * F { 1 }", "predi": "F { 1 3 } { 6 0 } * F { 1 5 } { 3 9 } ="}, "mask_7a9f7241-d870-11ed-9c06-c909318bca6a_10.jpg": {"label": "F { 9 } { 1 0 } - F { 1 }", "predi": "F { 9 } { 1 0 } - F { 1 } { 2 } ="}, "mask_bcf33dc1-d460-11ed-a00c-5d66fb9c395b_27.jpg": {"label": "1 6 - 8 0 = $ 6 $", "predi": "6 - 8 0 = $ 6 $"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_27.jpg": {"label": "8 - - 2 1", "predi": "8 - - 2 4"}, "mask_d39a8661-c25d-11ed-9313-0be8c6ff5802_16.jpg": {"label": "F { 3 } { 5 } / F { 1 }", "predi": "F { 3 } { 5 } / F { 9 } { 2 0 } ="}, "mask_272bfdf1-e8e6-11ed-9320-e9fc489509f7_27.jpg": {"label": "8 7 - 5 6 = $ 3 9 $", "predi": "8 7 - 5 6 = $ 3 1 $"}, "mask_de512801-d038-11ed-87cb-e36937b5a277_38.jpg": {"label": "6 8 + ( 5 + 2 ) = $ 7 5 $", "predi": "6 8 + ( 5 + 2 ) = $ 7 . 5 $"}, "mask_c0edbdc1-cd2a-11ed-a960-f7c70e2ccce1_32.jpg": {"label": "9 . 6 * 2 5 % 0 =", "predi": "9 . 6 * 2 5 % ="}, "mask_28810780-e581-11ed-972e-118a866d8662_11.jpg": {"label": "$ - $ $ = $", "predi": "$ - $ = $ = $"}, "mask_ecf7db11-ca34-11ed-843c-01c4cd6400ad_36.jpg": {"label": "0 5 * + 3 = 1 2 6", "predi": "0 5 0 / 2 = $ 1 2 6 $"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_8.jpg": {"label": "七 = ( $ 4 5 $ ) + 0", "predi": "= ( $ 4 5 $ ) + 0"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_15.jpg": {"label": "8 1 = 6 + 6 5", "predi": "8 1 = 6 + 6"}, "mask_c7183621-c8b2-11ed-8c68-d1b4066848bf_43.jpg": {"label": "2 0 % / 2 = $ F { 1 } { 1 0 } $", "predi": "2 0 / 2 = $ F { 1 } { 1 0 } $"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_17.jpg": {"label": "8 = 9 $ $ 8 1", "predi": "8 = 9 $ / $ 8 1"}, "mask_5cf38861-de99-11ed-be0d-b5352b78e6dd_43.jpg": {"label": "5 * 8 - 2 0 = 2 0", "predi": "5 * 8 - 2 0 = $ 2 0 $"}, "mask_d8a2d3e1-d826-11ed-95b5-ed3a74cc5227_12.jpg": {"label": "1 . 9 - 0 . 5 = $ 1 . 2 $", "predi": "1 . 9 - 0 . 7 = $ 1 . 2 $"}, "mask_90f3ddd1-c329-11ed-a6c3-5781877ada00_23.jpg": {"label": "1 1 - 1 = $ 1 0 $", "predi": "1 1 - 4 = $ 1 0 $"}, "mask_81fac9d1-bf0d-11ed-a4f5-6d7c26a354cb_13.jpg": {"label": "3 9 0 * 6 0 = $ 1 3 3 4 0 $", "predi": "3 9 0 * 6 0 = $ 2 3 4 0 5 $"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_3.jpg": {"label": "5 6 = 8 + 8 - 1 1", "predi": "1 6 = 8 + 8 - 1 1"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_33.jpg": {"label": "1 - 9 $ - $ 2 7", "predi": "4 - 9 $ / $ 2"}, "mask_1f79fd31-c8a9-11ed-9c74-9f765749440d_32.jpg": {"label": "1 5 1 1 4 2", "predi": "1 1 1 1 1"}, "mask_e8d8a450-ef1f-11ed-8d4b-9568653269a1_9.jpg": {"label": "F { 5 } { 9 } * F { 2 } { 2 5 } = $ F { 1 } { 1 5 } $", "predi": "F { 5 } { 9 } * F { 3 } { 2 5 } = $ F { 1 } { 1 5 } $"}, "mask_e781d991-c8a1-11ed-9d0a-95ec6217db79_14.jpg": {"label": "2 5 : 0 . 5 = $ 5 $", "predi": "2 . 5 : 0 . 5 = $ 5 $"}, "mask_2b6e6c01-ea7a-11ed-bfc4-173ed6dd6260_39.jpg": {"label": "1 6 : 1 . 5 = $ 4 $", "predi": "6 : 1 . 5 = $ 4 $"}, "mask_5cca42f1-e643-11ed-830d-cbe284fabe89_22.jpg": {"label": "x * 2 = $ 1 6 $", "predi": "x 2 = $ 1 6 $"}, "mask_4f412391-ee65-11ed-a0ac-d5695dcde8d4_35.jpg": {"label": "5 . 7 - 3 4 5", "predi": "5 . 7 - 0 - 3 - 4 5"}, "mask_3c0279b1-ce89-11ed-911e-09d4ed4b9651_4.jpg": {"label": "8 0 0 d m ^ 3 = ( $ 8 0 0 0 0 $ ) m ^ 3", "predi": "8 0 d m ^ 3 = ( $ 8 0 0 0 0 $ ) m ^ 3"}, "mask_e3b49540-d694-11ed-bfec-cdce704b26e6_10.jpg": {"label": "7 . 5 : 4 . 5 = $ { 5 } { 3 } $", "predi": "7 . 5 : 4 . 5 = $ F { 5 } { 3 } $"}, "mask_d931df31-e434-11ed-aa5c-87bc82f7255f_13.jpg": {"label": "F { 2 4 } { 3 6 } = F { 2 4 / ( $ 1 2 $ ) } { 2 3 / ( $ 2 $ ) } = F { ( $ 2 $ ) } { ( $ 3 $ ) }", "predi": "F { 2 4 } { 3 6 } = F { 2 4 / ( $ 1 2 $ ) } { 3 6 / ( $ 1 2 $ ) } = F { ( $ 2 $ ) } { ( $ 3 $ ) }"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_9.jpg": {"label": "五 成 = ( $ 5 0 $ ) x", "predi": "成 成 = ( $ 5 0 $ ) %"}, "mask_d8972341-c1ad-11ed-b768-613d9569d463_24.jpg": {"label": "F { 5 5 } { } / 2 2 =", "predi": "F { 5 5 } { 2 } / 2 2 ="}, "mask_f3e20701-e426-11ed-838e-c158e9a0c5d1_24.jpg": {"label": "5 } 6 = $ 5 $", "predi": "5 6 - 6 = $ 5 $"}, "mask_3b471c30-d78d-11ed-a82a-f5347e681596_1.jpg": {"label": "F { 5 } { 4 } = ( $ 4 $ ) / ( $ 5 $ ) = ( $ 0 . 8 $ )", "predi": "F { 4 } { 5 } = ( $ 4 $ ) / ( $ 5 $ ) = ( $ 0 . 8 $ )"}, "mask_ec2672f0-d92a-11ed-9ed9-8bc57e9268ad_13.jpg": {"label": "8 . 6 * 2 . 5 * 0 . 4 = 8 . 6 * ( $ 2 . 5 $ * $ 0 . 4 $ ) = $ 8 6 $", "predi": "8 . 6 * 2 . 5 * 0 . 4 = 8 . 6 * ( $ 2 5 $ * $ 0 . 4 $ ) = $ 8 6 $"}, "mask_01c17811-e4ea-11ed-b0c1-7b385afaeddc_0.jpg": {"label": "F { 1 } { 2 7 } + F { 8 } { 9 } = $ F { 2 2 4 } { 2 4 3 } $", "predi": "F { 1 } { 2 7 } + F { 8 } { 9 } = $ F { 2 2 4 } { 2 4 3 4 } $"}, "mask_3ae145c1-ed97-11ed-a7cb-c533e0980bd2_42.jpg": {"label": "7 4 * 4 0 = $ 2 8 0 0 $", "predi": "7 2 * 4 0 = $ 2 8 0 0 $"}, "mask_1e5582d1-db5f-11ed-b4d9-f93ec1051164_36.jpg": {"label": "4 9 / 6 = $ 9 P 5 $", "predi": "4 9 / 6 = 9 P 5"}, "mask_dd1136d1-ceec-11ed-bfa5-7982a9d77246_44.jpg": {"label": "1 4 - 1 0", "predi": "1 4 - 4 0"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_26.jpg": {"label": "三 成 = ( $ 3 0 $ ) %", "predi": "成 成 = ( $ 3 0 $ ) %"}, "mask_5b650e31-c484-11ed-a2dd-4f8a69cf87a7_10.jpg": {"label": "2 3 * 1 0 0 = $ 2 3 0 0 $", "predi": "2 3 * 1 0 0 = $ 2 0 0 $"}, "mask_dc5fe520-cfd1-11ed-8036-3719fb7a0815_25.jpg": {"label": "2 0 c m : ( $ 6 0 $ ) m = 1 : 3 0 0 0", "predi": "2 c m : ( $ 6 0 $ ) m = 1 : 3 0 0 0"}, "mask_faa52831-dd85-11ed-a1a8-4532e0a3b4e2_31.jpg": {"label": "3 3 0 / 3 / / 1 1 = $ 1 0 $", "predi": "3 3 0 / 3 / 1 1 = $ 1 0 $"}, "mask_dd057ce1-cdc1-11ed-af75-0d51e24b6e1d_12.jpg": {"label": "0 . 7 7 * F { 2 } { 7 7 } = $ 0 . 2 2 $", "predi": "0 . 7 7 * F { 2 } { 7 7 } = $ 0 . 0 2 $"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_27.jpg": {"label": "七 2 分 5 3 5", "predi": "1 2 + 8 5 = $ 2 3 5 $"}, "mask_f40b3b41-c719-11ed-8c74-cb8055fb8532_12.jpg": {"label": "1 3 5 + 4 0 8 = $ 5 4 4 $", "predi": "1 3 6 + 4 0 8 = $ 5 4 4 $"}, "mask_e0d6a181-bfbd-11ed-a9ee-17d65b4ec678_14.jpg": {"label": "F { 4 }", "predi": "F { 1 }"}, "mask_e75c4571-e356-11ed-b75d-a5942f00d553_6.jpg": {"label": "5 + 7 = $ 1 2 $", "predi": "5 + 7 = $ 7 2 $"}, "mask_3fb92b21-ef2d-11ed-a1e2-39e3bc64e098_18.jpg": {"label": "1 . 0 5 - 3 . 2 4 = $ 4 . 2 9 $", "predi": "1 . 0 5 + 3 . 2 4 = $ 4 . 2 9 $"}, "mask_ff5709d1-ce16-11ed-8d95-871805f46e15_17.jpg": {"label": "4 8 / 8 = 6", "predi": "4 8 / 8 = $ 6 $"}, "mask_5c738711-e438-11ed-8a55-7b0e95c04ea5_26.jpg": {"label": "3 0 0 0 c m ^ 3 = ( $ 0 . 3 $ ) L", "predi": "3 0 0 c m ^ 3 = ( $ 0 . 3 $ ) L"}, "mask_05b2e321-e411-11ed-a860-65512f0b6191_6.jpg": {"label": "4 元 - 8 元 = ( $ 6 $ ) 元", "predi": "1 4 元 - 8 元 = ( $ 6 $ ) 元"}, "mask_e5df3f51-c47e-11ed-8fd7-9d0f3f9c92c1_18.jpg": {"label": "F { 2 } { 5 ] + F { 2 } { 5 } = $ F { 4 } { 5 } $", "predi": "F { 2 } { 5 } + F { 2 } { 5 } = $ F { 4 } { 5 } $"}, "mask_f3f03f81-c092-11ed-932a-cb3a48e9d70b_26.jpg": {"label": "- F { 5 } { 6 } $ < $ F { 1 } { 6 }", "predi": "- F { 5 } { 6 } $ < $ - F { 1 } { 6 }"}, "mask_d333aa01-d921-11ed-b912-2334d9f34950_13.jpg": {"label": "F { 1 } { 1 0 } + F { 6 } { 5 } = $ F { 5 } { 6 0 } = F { 1 } { 1 2 } $", "predi": "F { 1 } { 1 0 } * F { 5 } { 6 } = $ F { 5 } { 6 0 } = F { 1 } { 1 2 } $"}, "mask_6e7a5381-c7d7-11ed-a1e3-915060c0ce49_5.jpg": {"label": "F { 5 } { 9 } - F { 5 } { 1 2 } + F { 4 } { 9 } - F { 1 } { 1 2 } = $ 1 F { 6 } { 1 2 } = F { 1 } { 2 } $", "predi": "F { 5 } { 9 } - F { 5 } { 1 2 } + F { 4 } { 9 } - F { 1 } { 1 2 } = $ 1 - F { 6 } { 1 2 } = F { 1 } { 2 } $"}, "mask_db1614a1-d864-11ed-8917-4fe416191213_16.jpg": {"label": "米 = F { ( $ 6 2 $ ) } { ( ) } 米 = ( $ 0 . 6 2 $ ) 米", "predi": "米 = F { ( $ 6 2 $ ) } { } 米 = ( $ 0 . 6 2 $ ) 米"}, "mask_1ea074b1-e295-11ed-a717-e75fb403b2a4_31.jpg": {"label": "F { 1 } { 6 } - F { 1 } { 1 2 } = $ 2 F { 1 } { 1 2 } $", "predi": "F { 1 } { 6 } - F { 1 } { 1 2 } = $ 2 F { 1 } { 1 1 2 } $"}, "mask_03b781b0-e719-11ed-83a7-ed386574381d_15.jpg": {"label": "9 0 ° < 5 5 ° = $ 3 5 ° $", "predi": "9 0 ° - 5 5 ° = $ 3 5 ° $"}, "mask_1e8ee371-ed54-11ed-af4f-1b6df7ae09e2_14.jpg": {"label": "6 . 7 0 元 ( $ 6 . 7 $ )", "predi": "6 . 7 0 ≈ ( $ 6 . 7 $ )"}, "mask_f2a9fc71-c8ac-11ed-8197-078f223af0f1_33.jpg": {"label": "8 6 / 2 ≈ $ 4 3 $", "predi": "8 6 / 2 = $ 4 3 $"}, "mask_e6c4a331-dd1d-11ed-882d-1bf8f6d61aa0_37.jpg": {"label": "F { 7 } { 9 } = F { ( 6 3 ) } { 8 1 }", "predi": "F { 7 } { 9 } = F { ( $ 6 3 $ ) } { 8 1 } $"}, "mask_6d4c6d61-e9ae-11ed-a0bf-efd6d78c9c51_17.jpg": {"label": "F { 4 } { 3 } - ( F { 2 } { 5 } + F { 3 } { 1 0 } ) = $ F { 4 } { 3 } + ( F { 4 } { 1 0 } + F { 3 } { 1 0 } ) = 1 $", "predi": "F { 4 } { 3 } - ( F { 2 } { 5 } + F { 3 } { 1 0 } ) = $ F { 4 } { 3 } - ( F { 4 } { 1 0 } + F { 3 } { 1 0 } ) = 1 $ 1 $"}, "mask_5ad796e1-e694-11ed-88ae-c1f483842e07_12.jpg": {"label": "6 0 6 9 克 $ < $ 7 吨", "predi": "6 0 6 9 千 克 $ < $ 7 吨"}, "mask_3ab04a51-c8ae-11ed-9bd0-c1b3ae58266c_40.jpg": {"label": "F { 2 } { 9 } * 2 = $ F { 4 } { 9 ] $", "predi": "F { 2 } { 9 } * 2 = $ F { 4 } { 9 } $"}, "mask_0bd6ba11-d86d-11ed-9364-e3df5be37ab1_5.jpg": {"label": "2 2 * { 2 } { 1 1 } =", "predi": "2 2 * F { 2 } { 1 1 } ="}, "mask_2af82621-cdd4-11ed-99fe-e7b1eb1d5fb7_18.jpg": {"label": "( > 分 角 =", "predi": "$ > $ 角 ="}, "mask_3b310851-de81-11ed-bc0c-eff541452801_43.jpg": {"label": "F { 5 } { 8 } > F { 1 } { 4 }", "predi": "F { 5 } { 8 } $ > $ F { 1 } { 4 }"}, "mask_fb8ce771-eb2b-11ed-b66d-b5808319408e_44.jpg": {"label": "1 1 1 0 = ( $ 1 0 0 $ ) + ( $ 1 0 $ )", "predi": "1 1 0 = ( $ 1 0 0 $ ) + ( $ 1 0 $ )"}, "mask_fbc4ec91-e637-11ed-9d6c-3d1a33781e77_33.jpg": {"label": "F { 1 } { 3 } - F { 1 } { 1 0 } = $ F { 1 0 } { 3 0 } - F { 3 } { 1 0 } = F { 7 } { 3 0 } $", "predi": "F { 1 } { 3 } - F { 1 } { 1 0 } = $ F { 1 0 } { 3 0 } - F { 3 } { 3 0 } = F { 7 } { 3 0 } $"}, "mask_5d915051-e6ef-11ed-99a1-61d1f3929e03_40.jpg": {"label": "0 . 1 2 5 = F { ( $ 1 $ } { ( $ 8 $ ) }", "predi": "0 . 1 2 5 = F { ( $ 1 $ } { ( $ 8 $ }"}, "mask_fac6cd71-d1b2-11ed-a7e7-a16bb42ce68e_16.jpg": {"label": "F { 7 } { 9 ] * F { 9 } { 7 } = $ 1 $", "predi": "F { 7 } { 9 } * F { 9 } { 7 } = $ 1 $"}, "mask_ee11bee1-ec07-11ed-9c96-6ded2b8fb180_41.jpg": {"label": "4 1 8 - 3 5 6 = $ 6 3 $", "predi": "4 1 9 - 3 5 6 = $ 6 3 $"}, "mask_1badfaa0-e440-11ed-8511-bdfa9c2f9384_2.jpg": {"label": "4 8 0 0 / ( 8 * 2 ) = $ 2 0 0 $", "predi": "4 8 0 0 / ( 8 * 3 ) = $ 2 0 0 $"}, "mask_5d1a4ae1-d20f-11ed-a61e-2b470e54a03e_25.jpg": {"label": "2 6 2 0", "predi": "1 0 2 0"}, "mask_de0ad581-e979-11ed-b7db-3b4895a947e0_36.jpg": {"label": "F { 3 } { 8 } $ < $ F { 7 } { 8 }", "predi": "F { 3 } { 8 } $ > $ F { 7 } { 8 }"}, "mask_1f64cfc1-cc94-11ed-bb44-3b11b70e4561_9.jpg": {"label": "7 9 2 1 5", "predi": "2 7 / 9 $ = $ 2 4 / 8"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_20.jpg": {"label": "5 4 0 0 / 9 = $ 6 0 0 $", "predi": "5 4 0 0 / 9 = $ 6 0 0 5 $"}, "mask_f7783571-da07-11ed-80d4-0709af6b2159_43.jpg": {"label": "角 * = $ 7 6 5 $", "predi": "2 1 * 6 0 = $ 2 6 . 4 6 $"}, "mask_ebd77c30-edfd-11ed-ae03-3d604d3fdce6_21.jpg": {"label": "5 0 7 k g = ( $ 0 . 5 0 7 $ ) t", "predi": "5 0 7 k g = ( $ 0 . 5 0 7 $"}, "mask_ff60fe11-f006-11ed-9011-cdf127881d05_22.jpg": {"label": "3 2 * 7 0 = $ 2 2 4 0 $", "predi": "3 2 * 7 0 = $ 2 2 4 0 0 $"}, "mask_dda2fb41-db3c-11ed-bbe1-fb5d906afa04_16.jpg": {"label": "6 0 * F { 3 } { 5 } = $ 3 6 $", "predi": "6 0 * F { 3 } { 5 } = $ 3 6 6 $"}, "mask_de7babf1-cf9f-11ed-a4e1-bf87d24a2693_16.jpg": {"label": "4 9 0 / { 7 } { 1 0 } = $ 7 0 0 $", "predi": "4 9 0 / F { 7 } { 1 0 } = $ 7 0 0 $"}, "mask_1ab1d2f1-cf9d-11ed-b0e2-15cbdd81d5fa_27.jpg": {"label": "1 2 * $ F { 1 } { 1 2 } $ = 1", "predi": "1 2 * ( $ F { 1 } { 1 2 } $ ) = 1"}, "mask_2fb82b20-dcba-11ed-92a7-5791ef3bf670_37.jpg": {"label": "9 + 8 - 6 = $ 1 1 $", "predi": "9 + 8 - 6 = $ 1 1 1 $"}, "mask_f1ebd381-d608-11ed-858c-c1970709dc3c_35.jpg": {"label": "1 2 3 5 6 0 0 = ( $ 1 2 4 . 5 6 $ ) 万", "predi": "1 2 3 5 6 0 0 = ( $ 1 2 4 . 5 6 0 $ ) 万"}, "mask_3ab05e01-ce17-11ed-8d95-871805f46e15_35.jpg": {"label": "4 1 4 1 1", "predi": "1 1 4 4 - 3 7 ) = $ 9 $"}, "mask_dc320d31-eb42-11ed-942d-b958113e4192_11.jpg": {"label": "4 9 元 = ( $ 7 $ ) 周", "predi": "4 9 元 = ( $ 7 $ ) 厘"}, "mask_db5f07b1-bffe-11ed-a604-2d243eecc09e_41.jpg": {"label": "1 0 1 + 4 = ( $ - 5 $ ) m", "predi": "1 0 1 + 亿 = ( $ - 5 $ ) 亿"}, "mask_db02dfc1-ee26-11ed-8654-438c1bc7dc2d_2.jpg": {"label": "( 1 ) 十 m", "predi": "( 1 ) 米 公"}, "mask_e25e89d1-dea5-11ed-a862-fbf397740eb1_27.jpg": {"label": ": 4 c m = $ 2 0 0 $", "predi": "m : 4 c m = $ 2 0 0 $"}, "mask_6e9a0670-eda8-11ed-a20f-cf61e17ee98d_5.jpg": {"label": "F { 4 } { 9 } = F { ( ) } { 4 }", "predi": "F { 4 } { 9 } = F { ( ) } { 4 5 }"}, "mask_04b0c2c1-c19e-11ed-810c-c929780a1fd2_76.jpg": {"label": "8 9 - 3 * 6 = $ 7 1 $", "predi": "8 9 - 3 * 6 = $ 7 $"}, "mask_1ea074b1-e295-11ed-a717-e75fb403b2a4_25.jpg": {"label": "F { 3 } { 3 * 5 } { 1 2 1 3 } $ F { 2 } ( $ F { 2 }", "predi": "F { 3 3 } { 2 1 3 } { F { 1 } { 2 } = $ F { 1 6 } { 1 3 } = F { 2 } { 2 6 } $"}, "mask_dc8c1b41-effc-11ed-aaee-151279633bcd_10.jpg": {"label": "3 8 角 = ( $ 3 $ ) 元 = ( $ 8 $ )", "predi": "3 8 角 = ( $ 3 $ ) 元 ( $ 8 $ )"}, "mask_fdd3ce41-ef1d-11ed-be82-1d6d4ce3e9f2_0.jpg": {"label": "$ 5 3 $ $ + $ $ 3 7 $ = $ 9 0 $ ( 个 )", "predi": "$ 5 3 $ + $ + $ $ 3 7 $ = $ 9 0 $ ( 米 )"}, "mask_e747da01-d527-11ed-bcb2-e9f0357515a8_41.jpg": {"label": "5 9 3 6 0 0 0 0 0 元 ( $ 5 9 $", "predi": "5 9 3 6 0 0 0 0 0 ≈ ( $ 5 9 $"}, "mask_e2010910-d9da-11ed-920c-d9bb7d632afa_35.jpg": {"label": "4 9 / 6 = $ 9 P 5 $", "predi": "4 9 / 6 = 9 P 5"}, "mask_d2059ba0-e003-11ed-a8a9-216faef1c6fa_42.jpg": {"label": "( $ 三 七 $ ) 十 一", "predi": "( $ = 秒 $ ) - + -"}, "mask_3b310851-de81-11ed-bc0c-eff541452801_34.jpg": {"label": "F { 7 } 8 } $ < $ F { 8 } { 9 }", "predi": "F { 7 } { 8 } $ < $ F { 8 } { 9 }"}, "mask_5a196081-d7ad-11ed-84a1-adbb35ba96e0_2.jpg": {"label": "6 / 8 = $ F ( { 6 } ) ( { 8 } $ )", "predi": "6 / 8 = F { ( $ 6 $ ) } { ( $ 8 $ ) }"}, "mask_fafc0d31-deb6-11ed-8fa5-c3c8286d53c8_5.jpg": {"label": "2 0 + 1 8 / 8 = $ 2 6 $", "predi": "2 0 + 4 8 / 8 = $ 2 6 $"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_28.jpg": {"label": "1 1 1 1 1 + 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1"}, "mask_e058ecd1-cdf1-11ed-b9de-95ce0f172065_15.jpg": {"label": "1 6 - 8 = $ 8 $", "predi": "1 6 - 8 = $ $ $"}, "mask_dde709e1-d47d-11ed-95da-bf7665cc5d86_28.jpg": {"label": "0 / ( 五 八 7", "predi": "0 / ( 五 5"}, "mask_f1fd98b1-d606-11ed-aa2b-7dd3371e768d_10.jpg": {"label": "0 . 6 4 7 t = ( $ 6 4 7 $ ) k g", "predi": "0 . 6 4 7 t = ( $ 6 4 7 $ ) k g g"}, "mask_d8659441-e973-11ed-8d17-f3444709f1b5_5.jpg": {"label": "( $ 5 $ ) = 米 5 0", "predi": "( $ 5 $ ) = 米 5"}, "mask_da6fa601-ebed-11ed-a081-45533329d7a6_11.jpg": {"label": "F { 5 } 8 } * 3 = $ F { 1 5 } { 8 } $", "predi": "F { 5 } { 8 } * 3 = $ F { 1 5 } { 8 } $"}, "mask_d33fba21-e7d6-11ed-9fdd-9ba7e3381f6c_26.jpg": {"label": "F { } { 9 } {", "predi": "F { 1 } { 2 } = $ F { 9 } { 8 } $"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_26.jpg": {"label": "1 1 1 1 1 1 1 1 4 1 )", "predi": "1 1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_faeec271-ddd4-11ed-ac93-7f3d98aaa450_11.jpg": {"label": "F { 8 } { 7 } * F { 4 } { 3 } = $ F { 2 } { 7 } / F { 3 } { 1 } = F { 2 } { 7 } / 3 = F { 2 } { 7 } * F { 1 } { 3 } = F { 2 } { 6 } = F { 1 } { 2 } $", "predi": "F { 8 } { 7 } * F { 4 } { 3 } = $ F { 2 } { 7 } / F { 3 } { 2 } = F { 2 } { 7 } + 3 F { 2 } { 7 } * F { 2 } { 2 } = F { 2 } { 2 } ="}, "mask_fbb3e991-ef1f-11ed-8d4b-9568653269a1_11.jpg": {"label": "2 2 * F { 5 } { 1 1 } = $ 1 $", "predi": "2 . 2 * F { 5 } { 1 1 } = $ 1 $"}, "mask_6d1f8c31-eeec-11ed-a728-c508ad7b873a_0.jpg": {"label": "F { 2 } { 5 } : F { 3 } { 4 } = ( ) : F { 5 } ( 8 )", "predi": "F { 2 } { 5 } : F { 3 } { 4 } = ( ) : F { 5 } { 8 }"}, "mask_eaf22021-c8ac-11ed-8af2-779d63ef86eb_10.jpg": {"label": "1 8 - 9 $ < $ 1 3 - 6", "predi": "1 8 - 9 $ > $ 1 3 - 6"}, "mask_1e8ee371-ed54-11ed-af4f-1b6df7ae09e2_20.jpg": {"label": "1 3 % ≈ ( $ 1 3 . 0 $ )", "predi": "1 3 % ≈ ( $ 1 3 . 0 $"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_6.jpg": {"label": "三 $ 2 $", "predi": "= $ 3 0 $"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_13.jpg": {"label": "F { 1 } { 6 } + F { 3 } { 4 } - F { 3 } { 8 } = $ F { 4 } { 2 4 } + F { 1 8 } { 2 4 } - F { 9 } { 2 4 } = F { 4 + 1 8 - 9 } { 2 4 } = F { 1 3 } { 2 4 } $", "predi": "F { 1 } { 6 } + F { 3 } { 4 } - F { 3 } { 8 } = $ F { 4 } { 2 4 } + F { 1 8 } { 2 4 } - F { 9 } { 2 4 } = F { 4 + 1 8 - 9 } { 2 4 } { F { 1 3 } { 2 4 } $"}, "mask_1cf93fb1-bfeb-11ed-b860-717f06995049_38.jpg": {"label": "6 * ( 4 9 - 4 0 ) = $ 5 3 $", "predi": "6 * ( 4 9 - 4 0 ) = $ 5 4 $"}, "mask_2feff0b1-d52a-11ed-a0a3-970bbc8adfec_21.jpg": {"label": "1 3 0 ° - 4 0 ° - 3 0 ° = $ 6 0 ° $", "predi": "1 3 0 ° - 4 0 ° - 3 0 ° = $ 6 0 $"}, "mask_1e8ee371-ed54-11ed-af4f-1b6df7ae09e2_21.jpg": {"label": "F { 7 } { 4 0 } ≈ ( $ 0 . 1 8 $ )", "predi": "F { 7 } { 4 0 } ≈ $ 0 . 1 8 $ )"}, "mask_ec106de1-e1c5-11ed-bdc9-a3caf80f747e_19.jpg": {"label": "3 天 = ( $ 7 2 $ ) 时", "predi": "3 千 = ( $ 7 2 $ ) 时"}, "mask_f9c15a01-e0c9-11ed-b5a8-fd85e9fce9a0_25.jpg": {"label": "2 . 5 = F { ( $ 5 $ ) } { ( $ 2 $ ) } $ = 2 F { 1 } { 2 } $", "predi": "2 . 5 = F { ( $ 5 $ ) } { ( $ 2 $ ) } = $ 2 F { 1 } { 2 } $"}, "mask_f7109ac1-dd0c-11ed-b704-e77afdf7f94f_40.jpg": {"label": "6 0 . + 6 - 1 0", "predi": "5 6 / 8 = $ 1 $"}, "mask_d251ac61-e8a1-11ed-be2d-19ecaed774bc_26.jpg": {"label": "1 0 0 * 5 6 . 8 = $ 5 0 8 $", "predi": "1 0 0 * 5 6 . 8 = $ 5 6 8 0 $"}, "mask_ef22e761-ce24-11ed-8f25-fd73d1630bfd_42.jpg": {"label": "4 厘 米 : 2 0 千 米 = $ 1 : 5 0 0 0 0 $", "predi": "4 厘 米 : 2 0 千 米 = $ 1 : 5 0 0 0 0 0 $"}, "mask_4bde2e70-e41d-11ed-b190-a701d8346fd0_9.jpg": {"label": "1 2 4 万 = ( $ 1 2 4 0 0 $ )", "predi": "1 . 2 4 万 = ( $ 1 2 4 0 0 $"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_43.jpg": {"label": "F { 1 } { 3 } + F { 3 } { 5 } = $ F { 5 } { 1 5 } + F { 9 } { 1 5 } = F { 5 + 9 } { 1 5 } = F { 1 4 } { 1 5 } $", "predi": "F { 1 } { 3 } + F { 3 } { 5 } = $ F { 5 } { 1 5 } + F { 9 } { 1 5 } = F { 5 - 4 } { 1 5 } = F { 1 4 } { 1 5 } $"}, "mask_3dbdce41-d79d-11ed-a2b0-7f3df6534f81_14.jpg": {"label": "2 元 = ( $ 2 0 $ ) 分 米 = ( $ 2 0 0 $ ) 厘 米", "predi": "2 米 = ( $ 2 0 $ ) 分 米 = ( $ 2 0 0 $ ) 厘 米"}, "mask_e47006d1-df78-11ed-91fb-a3f3bf3b229c_28.jpg": {"label": "1 2 元 1 角 $ < $ 2 元 1 分", "predi": "2 元 1 角 $ < $ 2 元 1 分"}, "mask_3fa101b1-d201-11ed-84a1-456fdeaec262_35.jpg": {"label": "F { 1 0 } { ( $ 3 6 $ ) }", "predi": "F { 1 0 } { ( $ 3 6 $ }"}, "mask_3a0be5a1-d051-11ed-ab3b-2f95a99ca10b_27.jpg": {"label": "8 * 5 = $ 2 * 2 0 $", "predi": "8 * 5 = 2 * 2 0"}, "mask_e6663c41-e6f1-11ed-9bc4-37d091e04673_14.jpg": {"label": "3 . 5 m ^ 3 ( $ 3 5 0 0 $ ) d m ^ 3", "predi": "3 . 5 ^ 3 = ( $ 3 5 0 0 $ ) d m ^ 3"}, "mask_e4f569e1-e880-11ed-8240-c907d23284e5_19.jpg": {"label": "7 8 0 9 * 万 $ < $ 7 亿", "predi": "7 8 0 9 万 $ < $ 7 亿"}, "mask_36d2c680-d85b-11ed-b61e-ff9d3ca67088_3.jpg": {"label": "1 0 千 克 - 6 千 克 =", "predi": "1 0 千 克 - 6 千 克 = ("}, "mask_36d2c680-d85b-11ed-b61e-ff9d3ca67088_20.jpg": {"label": "1 0 1 / 2 =", "predi": "4 0 1 / 2 ="}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_1.jpg": {"label": "6 9 - 1", "predi": "6 9 - 4"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_43.jpg": {"label": "1 6 - 6 + 0 8 - 0 8", "predi": "6 = 6 + 0 8 - 0 8"}, "mask_da1ed751-c895-11ed-b3d9-7bc3a2f7277e_13.jpg": {"label": "{ 3 } { 1 4 } $ > $ F { 1 } { 1 2 }", "predi": "F { 3 } { 1 4 } $ > $ F { 1 } { 1 2 }"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_20.jpg": {"label": "8 - 9 - 8 4", "predi": "8 - 9 - 8"}, "mask_fa9f46d0-dbfb-11ed-96f2-811906e9f826_39.jpg": {"label": "5 7 + 9 $ $ 5 9 + 7", "predi": "5 7 + 9 5 9 + 7"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_47.jpg": {"label": "0 4 $ < $ +", "predi": "0 4 $ < $ + 4"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_18.jpg": {"label": "1 5 = 6 + 8 - 8 9", "predi": "6 = 6 + 8 - 8 9"}, "mask_d7bcd441-d6b3-11ed-b2a5-17bced46acd8_39.jpg": {"label": "9 3 * 3 0 0 = $ 2 7 9 0 $", "predi": "9 3 * 3 0 0 = $ 2 7 9 9 $"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_32.jpg": {"label": "1 6 - 4 + 4 - 9 1", "predi": "1 6 - 4 + 4 - 8 1"}, "mask_ac46b8c0-befe-11ed-8d46-cb85c19af59e_6.jpg": {"label": "x * y * 7 = $ 7 ( x + y ) $", "predi": "x * x * 7 = $ 7 ( x + 9 ) $"}, "mask_10ac57f0-ea8a-11ed-8ea0-2726ffd31f5c_3.jpg": {"label": "3 3 3 * 3 3 3 = $ 1 1 0 8 8 9 $", "predi": "3 3 3 * 3 3 3 = 1 1 0 8 8 9"}, "mask_2e921f31-e7d2-11ed-92b3-c99ebdbf361f_18.jpg": {"label": "F { 5 } { 6 } / F { 5 }", "predi": "F { 5 } { 8 } / F { 5 } { 2 4 } ="}, "mask_fa9f46d0-dbfb-11ed-96f2-811906e9f826_19.jpg": {"label": "9 + 5 4 $ $ 4 5 + 9", "predi": "9 + 5 4 4 5 + 9"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_41.jpg": {"label": "0 = 8 - 8 8", "predi": "2 = 8 - 8 8"}, "mask_e4f569e1-e880-11ed-8240-c907d23284e5_30.jpg": {"label": "1 6 2 万 - 6 3 万 = $ 9 9 万 $", "predi": "1 6 2 万 - 6 3 万 = $ 9 9 万"}, "mask_82384aa1-e5b2-11ed-b185-593252755f2c_1.jpg": {"label": "8 / 4 + 0 =", "predi": "8 1 4 + 0 ="}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_36.jpg": {"label": "7 1 - $ = $ 9", "predi": "7 1 - $ - $ 9 L"}, "mask_af37b0a1-d9f9-11ed-b227-0deab98c5b54_5.jpg": {"label": "5 1 + 4 8 = $ 6 9 $", "predi": "5 1 + 1 8 = $ 6 9 $"}, "mask_10ac57f0-ea8a-11ed-8ea0-2726ffd31f5c_6.jpg": {"label": "1 0 1 0 1 * 2 2 = $ 2 2 2 2 2 2 $", "predi": "1 0 1 0 1 * 2 2 = 2 2 2 2 2 2"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_28.jpg": {"label": "6 9 - 9 4", "predi": "6 - 9 - 4 4"}, "mask_da1ed751-c895-11ed-b3d9-7bc3a2f7277e_2.jpg": {"label": "F { 9 } { 1 1 } = F { 9 * ( $ 3 $ ) } { 1 1 * ( $ 3 $ ) } = F { ( $ 2 7 $ ) } { ( $ 3 $ }", "predi": "F { 9 } { 1 1 } = F { 9 * ( $ 3 $ ) } { 1 1 * ( $ 3 $ ) } = F { ( $ 2 7 $ ) } { ( $ 3 3 $ ) }"}, "mask_ec5f8ba0-bf10-11ed-a4f5-6d7c26a354cb_1.jpg": {"label": "( 2 3 + 2 5 ) * 4 = $ 2 3 $ * 4 $ + $ $ 2 5 $ * 4", "predi": "( 2 3 + 2 5 ) * 4 = $ 2 3 $ * 4 $ + $ 2 5 $ * 4"}, "mask_665dbd41-eb4a-11ed-b22b-07d39fbc14e0_33.jpg": {"label": "8 8 + 5 = $ 7 2 $", "predi": "8 8 + 5 = $ 7 3 $"}, "mask_f1b84f01-eff4-11ed-ae39-e11c2f0005b3_10.jpg": {"label": "6 + 4 0 + 8 = $ 3 8 $", "predi": "+ 4 0 + 8 = $ 3 8 $"}, "mask_bba55d11-cee6-11ed-9b34-6fe586bc5347_28.jpg": {"label": "1 2 . 5 / 5 = 2 . 5", "predi": "1 2 . 5 / 5 = $ 2 . 5 $"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_30.jpg": {"label": "9 8 - 9 $ - $ 2", "predi": "9 8 = 9 $ - $ 2"}, "mask_93550ba1-e370-11ed-b62a-271c54bc3310_28.jpg": {"label": "4 1 . 0 9 8 ≈ $ 4 1 $", "predi": "4 1 . 0 9 8 ≈ $ 4 1 1 $"}, "mask_0ed286d1-e1e2-11ed-a3a7-5b542e505f50_37.jpg": {"label": "F { 2 } { 5 } / F { 8 } { 7 } = $ F { } { 5 } * F { 7 } { } = F { 7 } { 1 5 } $", "predi": "F { 2 } { 5 } / F { 8 } { 7 } = $ F { 2 } { 5 } * F { 7 } { } = F { 7 } { 1 5 } $"}, "mask_dfc40281-db68-11ed-8612-758c44062f71_34.jpg": {"label": "元 3 角 = (", "predi": "元 3 角 = ( $ 0 $"}, "mask_d251ac61-e8a1-11ed-be2d-19ecaed774bc_42.jpg": {"label": "0 . 0 2 * 1 0 * 1 0 0 = $ 2 0 $", "predi": ". 0 2 * 1 0 * 1 0 0 = $ 2 0 $"}, "mask_dc320d31-eb42-11ed-942d-b958113e4192_15.jpg": {"label": "闰 年 一 年 = ( $ 3 6 6 $ ) 天", "predi": "厘 成 成 成 = ( $ 3 6 6 $ ) 克"}, "mask_0b623ea1-e98a-11ed-b73c-cd507f746a8c_9.jpg": {"label": "3 4 * { 7 } { 8 } - 2 * F { 7 } { 8 } = $ 2 8 $", "predi": "3 4 * F { 7 } { 8 } - 2 * F { 7 } { 8 } = $ 2 8 $"}, "mask_0cedca11-d796-11ed-b6b1-afaf0b1681fe_14.jpg": {"label": "5 } 1 }", "predi": "F { 3 }"}, "mask_e16c4231-d6b0-11ed-811c-c911f1b35dfe_11.jpg": {"label": "3 7 / 7 = $ 5 P 2 $", "predi": "3 7 / 7 = $ 5 - 2 $"}, "mask_e16c4231-d6b0-11ed-811c-c911f1b35dfe_36.jpg": {"label": "2 3 / 5 = $ 4 P 3 $", "predi": "2 3 / 5 = $ 4 - 3 $"}, "mask_ed61e2d1-bf44-11ed-a345-ed3f6b44010c_28.jpg": {"label": "5 6 - 1 2 + 2 3 = $ 6 7 $", "predi": "5 6 - 1 2 + 2 3 = 6 7"}, "mask_f18c0bc1-d6c2-11ed-a18d-d5b6c2685f9d_22.jpg": {"label": "2 . 5 m ^ 2 = ( ) d m", "predi": "2 . 5 c m ^ 2 = ( ) d m"}, "mask_f98714a1-c484-11ed-ae1e-7d11c488cff7_2.jpg": {"label": "1 0 0 + 2 5 = $ 4 $", "predi": "1 0 0 / 2 5 = $ 4 $"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_35.jpg": {"label": "七 成 五 = ( $ 7 5 $ ) %", "predi": "成 成 五 = ( $ 7 5 $ ) %"}, "mask_e8deb711-e2a3-11ed-9e55-a520ea92ec5e_33.jpg": {"label": "3 4 % = ( $ 0 . 3 4 $ )", "predi": "3 4 % = ( $ 0 . 3 4 $"}, "mask_04f53fd1-d086-11ed-b3d0-2f9462fd9d08_23.jpg": {"label": "4 3 / 6 = $ 7 P 1 $", "predi": "4 3 / 6 = $ 7 P 1 P 1"}, "mask_3ad3d201-e4b4-11ed-8987-79eff8960919_3.jpg": {"label": "F { 7 } { 8 } * 6 =", "predi": "F { 7 } { 8 } / 6 ="}, "mask_1f22abb1-cd5a-11ed-a23f-7d30b30b103a_24.jpg": {"label": "3 . 6 c m : 1 8 0 k m = $ 1 : ( $ 1 . 5 $ ) $", "predi": "3 . 6 c m : 1 8 0 k m = 1 : ( $ 1 . 5 $ )"}, "mask_eb522101-efe9-11ed-90ac-9fc756c44b48_15.jpg": {"label": "F { 2 } { 9 } - F + 5 } { 6 } = $ F { 5 7 } { 5 4 } $", "predi": "F { 2 } { 9 } + F + 5 } { 6 } = $ F { 5 7 } { 5 4 } $"}, "mask_eb522101-efe9-11ed-90ac-9fc756c44b48_6.jpg": {"label": "F { 5 } { 1 4 } + F 2 } { } $ F { 9 } { 1 4 } $", "predi": "F { 5 } { 1 4 } + F { 2 } { 7 } = $ F { 9 } { 1 4 } $"}, "mask_4bd75311-deab-11ed-8bb1-1d9304b61fc2_25.jpg": {"label": "4 6 2 6", "predi": "/ 6 = $ 2 6 $"}, "mask_eca0bf21-d6d1-11ed-a57c-5ba9d58fc7c9_27.jpg": {"label": "3 . 0 4 + 0 . 2 ≈ $ 1 5 . 2 $", "predi": "3 . 0 4 / 0 . 2 = $ 1 5 . 2 $"}, "mask_1a492c91-e8f8-11ed-8849-9bd50053a95e_7.jpg": {"label": "F { 1 } { 9 } $ = $ F { 9 } { 8 } }", "predi": "F { 1 } { 9 } $ = $ F { 9 } { 8 }"}, "mask_1a458a41-eb41-11ed-ac4f-5deaca229540_8.jpg": {"label": "b * c * 7 = $ 7 b c $", "predi": "b * c * 7 = $ 7 b t $"}, "mask_3fb92b21-ef2d-11ed-a1e2-39e3bc64e098_37.jpg": {"label": "P 一 = $ 7 $", "predi": "- 1 2 3 = $ 1 3 $"}, "mask_1e192391-e356-11ed-977f-63f155ca3a6c_30.jpg": {"label": "1 4 - ( 3 8 - 8 ) = $ 1 4 $", "predi": "4 4 - ( 3 8 - 8 ) = $ 1 4 $"}, "mask_e54ab4e1-d921-11ed-b912-2334d9f34950_4.jpg": {"label": "F { 3 } { 7 } + F { 4 } { 2 1 } = $ F { 1 3 } { 2 7 } $", "predi": "F { 3 } { 7 } + F { 4 } { 2 1 } = $ F { 1 3 } { 2 1 } $"}, "mask_fc9bc751-c6f8-11ed-975f-4566a2759b4a_13.jpg": {"label": "F { 3 } { 1 0 } * 3 = $ { 9 } { 1 0 } $", "predi": "F { 3 } { 1 0 } * 3 = $ F { 9 } { 1 0 } $"}, "mask_fbc4ec91-e637-11ed-9d6c-3d1a33781e77_37.jpg": {"label": "{ 7 { 5 = $ 0 $", "predi": "- F { 7 } { 0 } = $ 0 $"}, "mask_1d5f2391-c624-11ed-9be4-c721ba52226f_28.jpg": {"label": "( $ F { 6 } { 3 } $ ) * F { 1 } { 8 } = F { 1 } { 2 } * F { 2 } { 3 }", "predi": "( $ F { 6 } { 3 } $ ) * F { 1 } { 6 } = F { 1 } { 2 } * F { 2 } { 3 }"}, "mask_e781d991-c8a1-11ed-9d0a-95ec6217db79_6.jpg": {"label": "F { 4 } { 7 } : F { 1 6 } { 2 1 } = $ F { } { 1 } * F { } { 4 } $", "predi": "F { 4 } { 7 } : F { 1 6 } { 2 1 } = $ F { 4 } { 1 7 } * F { } { } { 4 } $"}, "mask_f35b2c51-c962-11ed-b500-e1f479f02c1c_36.jpg": {"label": "7 米 1 2 5", "predi": "7 米 1 2 5 5"}, "mask_1a1757f1-e0c5-11ed-8d72-afb3f1fce4a2_0.jpg": {"label": "F { 2 } { 5 } * F { 3 } { 7 } + F { 2 } { 5 } * F { 4 } { 7 } = F { 2 } { 5 } = ( $ F { 3 } { 7 } + F { 4 } { 7 } $ )", "predi": "F { 2 } { 5 } * F { 3 } { 7 } + F { 2 } { 5 } * F { 4 } { 7 } = F { 2 } { 5 } * ( $ F { 3 } { 7 } + F { 4 } { 7 } $ )"}, "mask_ed61e2d1-bf44-11ed-a345-ed3f6b44010c_2.jpg": {"label": "$ 8 9 - ( 2 4 + 3 5 ) = 3 0 $", "predi": "$ 8 9 - ( 2 4 + 3 5 ) = 3 0"}, "mask_2f8bc4b1-ee65-11ed-9a13-67095006ad37_41.jpg": {"label": "1 3 4 - ( 1 8 - 8 ) = $ 2 4 $", "predi": "3 4 - ( 1 8 - 8 ) = $ 2 4 $"}, "mask_4bf72851-df75-11ed-9c14-e383df388344_6.jpg": {"label": "2 0 0 c m ^ 3 = ( $ 0 . 0 2 $ ) d m ^ 3", "predi": "2 0 c m ^ 3 = ( $ 0 . 0 2 $ ) d m ^ 3"}, "mask_da65e7d1-e433-11ed-a4ec-d78f5557c6a6_2.jpg": {"label": "F { 5 7 } { 1 0 0 0 } = ( $ 0 . 0 5 7 $ )", "predi": "F { 5 7 } { 1 0 0 0 } = ( $ 0 . 0 5 7 $ ) 7"}, "mask_4f5e4921-bfad-11ed-b1f3-5bb98183b7ba_21.jpg": {"label": "1 1 + 1 1 1 1", "predi": "1 4 4 1 1 1 1"}, "mask_f3b48390-d3ad-11ed-9af3-bba1aa92c1cc_41.jpg": {"label": "( $ F { 1 7 } { 3 } $ ) * F { 1 7 } { 3 } = 1", "predi": "( $ F { 1 7 } { 3 } $ ) * F { 1 7 } { 3 } = $ 1 $"}, "mask_e26a41f0-e4ed-11ed-8c22-a53ac075c047_23.jpg": {"label": "{ 2 * F { 5 } {", "predi": "+ F { 2 } { 7 } F { 5 } { 7 }"}, "mask_1d50d5e1-ea7a-11ed-bfc4-173ed6dd6260_42.jpg": {"label": "F { 1 0 } { 1 3 } : F { 5 } { 1 2 } = $ F { 2 4 } { 1 3 } $", "predi": "F { 1 0 } { 1 3 } : F { 5 } { 1 } = $ F { 2 4 } { 1 3 } $"}, "mask_1c7324d1-ed53-11ed-918b-398e7122eb1f_34.jpg": {"label": "F { 2 } { 5 } = ( $ 0 . 4 $ )", "predi": "F { 2 } { 5 } = ( $ 0 . 4 $"}, "mask_d01662a1-d47a-11ed-b8e7-7d40b1959cd5_5.jpg": {"label": "2 > 9 0", "predi": "1 6 - 9 = $ 7 $"}, "mask_1d5f2391-c624-11ed-9be4-c721ba52226f_0.jpg": {"label": "F { 3 } { 0 } - F 2 0 { 5 } ( $ 6 $ ) * 5", "predi": "F { 3 } { 2 0 } = ( $ 6 $ ) * 5"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_38.jpg": {"label": "= 1 = 1 1", "predi": "1 ≈ 1 1 1"}, "mask_5ef29bb0-c58a-11ed-ab4f-3f8da19e6b9d_3.jpg": {"label": "F { 5 3 } ( 1 0 0 } = $ 0 . 5 3 $", "predi": "F { 5 3 } { 1 0 0 } = $ 0 . 5 3 $"}, "mask_f01ea191-d52c-11ed-8271-af52227f6dad_3.jpg": {"label": "F { 9 } { 2 } F { 2 }", "predi": "F { 9 } { 2 } * F { 6 } { 1 1 } = $ F { 4 } { 3 } $"}, "mask_1c7ddb70-e298-11ed-9fa1-f76a9a73f973_8.jpg": {"label": "4 / 8 $ = F { 4 } { 8 } $", "predi": "4 / 8 = $ F { 4 } { 8 } $"}, "mask_f0310211-e0a6-11ed-b895-e7221fef1edb_10.jpg": {"label": "1 . 2 1 (", "predi": "4 . 2 1"}, "mask_03e0e6b1-e0fa-11ed-badc-7fddddc39ea7_6.jpg": {"label": "9 . 6 + 1 0 0 = $ 0 0 9 6 $", "predi": "9 . 6 / 1 0 0 = $ 0 0 9 6 $"}, "mask_f1e133e1-d873-11ed-83f3-63712f2f1b3e_18.jpg": {"label": "F { 2 } { 3 } = F { 2 * ( $ 1 1 $ ) } { 3 * ( $ 1 1 $ ) } = F { ( $ 2 2 $ ) } { ( $ 3 3 $ }", "predi": "F { 2 } { 3 } = F { 2 * ( $ 1 1 $ ) } { 3 * ( $ 1 1 $ ) } = F { ( $ 2 2 $ ) } { ( $ 3 3 $ ) }"}, "mask_dbfed031-deb5-11ed-99f5-7f21a38539ac_14.jpg": {"label": "1 5 分 $ < $ 1 角 5 分", "predi": "1 5 分 $ = $ 1 角 5 分"}, "mask_e1533201-cc96-11ed-9bf3-1f544e096a87_17.jpg": {"label": "4 3 - 0 . 7 = $ 3 . 6 $", "predi": "4 . 3 - 0 . 7 = $ 3 . 6 $"}, "mask_3b14cf11-c4a9-11ed-b508-db0f7c02ccfa_2.jpg": {"label": "F { 3 } { x } = $ F { 1 } { 1 2 0 0 0 } $", "predi": "F { 3 } { x } = F { 1 } { 1 2 0 0 0 }"}, "mask_fdd803c1-e6a8-11ed-8d6e-1578250edc0a_26.jpg": {"label": "0 . 1 2 5 + F { 3 } { 8 } = $ F { 4 } { 8 } = F { 1 } { 2 } $", "predi": "0 . 1 2 + F { 3 } { 8 } = $ F { 4 } { 8 } = F { 1 } { 2 } $"}, "mask_dd3423b1-ecde-11ed-95e3-2fae6d7ac15a_28.jpg": {"label": "成 * ( > 五 ( $ 3 $ ) )", "predi": "3 元 ( 米 = ( $ 3 7 $ ) 角"}, "mask_0fe92f21-ce27-11ed-a916-bbaf7b45499b_0.jpg": {"label": "1 2 * ( F { 1 } { 3 } - F { 1 } { 4 } ) = $ F { 1 } { 1 2 } * F { 1 } { 1 2 } = 1 $", "predi": "1 2 * ( F { 1 } { 3 } - F { 1 } { 4 } ) = $ F { 5 } { 1 2 } * F { 1 } { 1 2 } = 1 $"}, "mask_0a5ed921-e05a-11ed-aec8-694e69857300_19.jpg": {"label": "0 . 1 6 = ( $ F { 4 } { 2 5 } $ )", "predi": "0 . 1 6 = ( $ F { 4 } { 2 5 } $"}, "mask_5c739961-dd2d-11ed-9858-5fd236f404c6_0.jpg": {"label": "3 . 6 / 3 0 0 = $ 0 . 0 1 7 $", "predi": "3 . 6 / 3 0 0 = $ 0 . 0 1 2 $"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_14.jpg": {"label": "六 成 八 = ( $ 6 8 $ ) %", "predi": "公 成 八 = ( $ 6 8 $ ) %"}, "mask_dccd7fe0-bfbe-11ed-9196-b5061c4bb90c_37.jpg": {"label": "F { 3 } { 1 0 } + F { 7 } { 1 5 } = $ F { 2 3 } { 3 0 } $", "predi": "F { 3 } { 1 0 } + F { 7 } { 1 5 } = $ F { 2 3 0 } { 3 0 } $"}, "mask_1ab1d2f1-cf9d-11ed-b0e2-15cbdd81d5fa_32.jpg": {"label": "1 亿 = ( $ 1 0 0 0 0 0 0 0 0 $ )", "predi": "1 亿 = ( $ 1 0 0 0 0 0 0 0 0 0 $ )"}, "mask_df8f5801-d604-11ed-8731-6d1b62a3e635_0.jpg": {"label": "$ 7 $ > 7 . 2 5 < $ 8 $", "predi": "$ 7 $ < 7 . 2 5 < $ 8 $"}, "mask_2ca835a1-cd5c-11ed-b848-6b3b5f52e16b_15.jpg": {"label": "1 0 - 8 - 1 = $ 1 0 $", "predi": "1 0 - 8 - 1 = $ 1 $"}, "mask_f5b434f1-d53e-11ed-a640-fb9b4f351d70_0.jpg": {"label": "F { 2 } { 4 } $ = $ F { 8 } { 1 6 }", "predi": "F { 2 } { 4 } $ = $ F { 8 } { 1 6 } ="}, "mask_1fcd82d1-c524-11ed-81dc-f92359da3700_21.jpg": {"label": "( $ 7 4 . 5 4 4 $ ) < 7 4 . 5 4 5 > ( $ 7 4 . 5 4 $ )", "predi": "( $ 7 4 . 5 4 4 $ ) < 7 4 . 5 4 5 < ( $ 7 4 . 5 4 $ )"}, "mask_2c4ea711-e41d-11ed-8fa4-8bdfce412ea8_9.jpg": {"label": "四 五 折 = ( $ 4 5 $ ) %", "predi": "成 五 成 = ( $ 4 5 $ ) %"}, "mask_5cca42f1-e643-11ed-830d-cbe284fabe89_41.jpg": {"label": "1 * y = $ y $", "predi": "1 * x = $ a $"}, "mask_3d9b1111-cef7-11ed-bac1-7d2cb46e64a6_43.jpg": {"label": "2 0 分 =", "predi": "2 0 分 = ("}, "mask_3f88ec71-e763-11ed-8d33-db10af627f88_35.jpg": {"label": "1 . 0 2 % * 5 = $ 0 8 5 1 $", "predi": "1 . 0 2 % * 5 = $ 0 0 5 1 $"}, "mask_fdab26d1-d793-11ed-9987-efe79d5696c6_28.jpg": {"label": "1 3 * 4 0 = $ 5 2 0 $", "predi": "1 3 * 4 0 = 5 2 0"}, "mask_d9fc1940-ea64-11ed-b66d-7d6e024aeccb_12.jpg": {"label": "2 4 分 = ( $ 2 $ ) 角", "predi": "2 4 分 = ( $ 2 $ ) 角 ("}, "mask_fcee4d71-ea81-11ed-ab7f-3ff5f3943557_23.jpg": {"label": "7 8 + 9 - 6 0 = $ 2 $", "predi": "7 8 + 9 - 6 0 = $ 2 7 $"}, "mask_f7109ac1-dd0c-11ed-b704-e77afdf7f94f_51.jpg": {"label": "1 . 1 ( P P", "predi": "1 1 1 1 1"}, "mask_1fb27f31-e6ee-11ed-9901-954cfd85eb53_32.jpg": {"label": "0 . 0 5 = F { ( $ 1 $ ) } { ( $ 2 0 } $ )", "predi": "0 . 0 5 = F { ( $ 1 $ ) } { ( $ 2 0 $ ) }"}, "mask_e7d01591-e99b-11ed-a77e-dbc29b72e147_33.jpg": {"label": "$ 1 3 $ + 5 = 1 8", "predi": "$ 1 3 $ ) + 5 = 1 8"}, "mask_6e7a5381-c7d7-11ed-a1e3-915060c0ce49_3.jpg": {"label": "F { 3 } { 4 } - F { 1 0 } { 1 1 } + F { 1 } { 1 1 } = $ F { 4 } { 3 } - 1 = F { 1 } { 3 } $", "predi": "F { 4 } { 3 } - F { 1 0 } { 1 1 } + F { 1 } { 1 1 } = $ F { 4 } { 3 } - 1 = F { 1 } { 3 } $"}, "mask_f9c6cd71-ed9c-11ed-aeeb-1747dd124717_5.jpg": {"label": "F { 1 } { 3 } - F { 2 } { 3 } - F { 7 } { 1 5 } = $ F { 8 } { 1 5 } $", "predi": "F { 1 } { 3 } + F { 2 } { 3 } - F { 7 } { 1 5 } = $ F { 8 } { 1 5 } $"}, "mask_1edb5781-ce20-11ed-98f0-d16f20d74b1b_21.jpg": {"label": "6 1 + 6 9 = $ 9 0 $", "predi": "2 1 + 6 9 = $ 9 0 $"}, "mask_e9643cd1-e1c9-11ed-866d-cb8697564574_53.jpg": {"label": "8 * $ 4 $ = 1 2", "predi": "3 * $ 4 $ = 1 2"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_16.jpg": {"label": "三 = $ $", "predi": "= $ = $"}, "mask_e0c02ba1-cd58-11ed-91cd-318a51e8efc2_42.jpg": {"label": "1 2 / 6 = $ 3 5 3 + 7 $", "predi": "1 2 / 6 = $ 2 5 3 + 7"}, "mask_e1f80bc1-effc-11ed-b082-010fd81ff601_1.jpg": {"label": "4 角 = ( $ 4 $ ) 元 ( $ 4 $ ) 角", "predi": "4 4 角 = ( $ 4 $ ) 元 ( $ 4 $ ) 角"}, "mask_05a78171-dc0e-11ed-9068-4147899280f3_7.jpg": {"label": "F { 3 } 8 } - F { 1 } { 5 } = $ F { 7 } { 4 0 } $", "predi": "F { 3 } { 8 } - F { 1 } { 5 } = $ F { 7 } { 4 0 } $"}, "mask_fbbceb51-d375-11ed-9d5c-0dc46d4c0738_29.jpg": {"label": "1 0 0 元 = ( $ 2 $ ) 张 2 0 元 + 1 张 5 0 元 + 1 张", "predi": "1 0 0 元 = ( $ 2 $ ) 张 2 0 元 + 1 张 5 0 元 + 1 张 1"}, "mask_d9de1a61-df76-11ed-84f7-3f172b345776_0.jpg": {"label": "7 * ( 1 7 0 - 6 6 ) = $ 4 $", "predi": "7 * ( 7 2 - 6 6 ) = $ 4 $"}, "mask_5aad13d1-c19a-11ed-a4b4-ed0a2df21331_37.jpg": {"label": "9 0 6 / 2 = $ 3 0 2 $", "predi": "9 0 6 / 3 = $ 3 0 2 $"}, "mask_da6fa601-ebed-11ed-a081-45533329d7a6_7.jpg": {"label": "0 = ( 4 . 5 c m P", "predi": "a = 4 . 5 c m"}, "mask_e25e89d1-dea5-11ed-a862-fbf397740eb1_24.jpg": {"label": "1 8 * 9 = ( $ F { 5 7 } { 3 6 } $ ) * 3 6", "predi": "1 . 8 * 3 2 = ( $ F { 5 7 } { 3 6 } $ ) * 3 6"}, "mask_3da010b1-dc09-11ed-9217-9fd681f6bdc1_14.jpg": {"label": "F { 4 } { 7 } * F { 3 } { 4 } = $ F { 3 } { 7 ] $", "predi": "F { 4 } { 7 } * F { 3 } { 4 } = $ F { 3 } { 7 } $"}, "mask_0a5ed921-e05a-11ed-aec8-694e69857300_8.jpg": {"label": "2 5 = ( $ F { 5 } { 2 } $ )", "predi": "2 . 5 = ( $ F { 5 } { 2 } $ )"}, "mask_2f91ea21-e27b-11ed-80da-517f895d0ef9_22.jpg": {"label": "9 7 - 3 3 = $ 6 5 $", "predi": "9 7 - 3 3 = $ 6 6 4 $"}, "mask_faeec271-ddd4-11ed-ac93-7f3d98aaa450_44.jpg": {"label": "F { 7 } { 5 } * 1 0 = $ F { 7 0 } { 5 } = F { 1 4 } { 1 } = 1 4 $", "predi": "F { 7 } { 5 } * 1 0 = $ F { 1 0 } { 5 } = F { 1 0 } { 1 } = 1 4 $"}, "mask_f1e133e1-d873-11ed-83f3-63712f2f1b3e_2.jpg": {"label": "F { 5 } } { 7 } $ < $ F { 7 } { 8 }", "predi": "F { 5 } { 9 } $ < $ F { 7 } { 8 }"}, "mask_1e8ee371-ed54-11ed-af4f-1b6df7ae09e2_30.jpg": {"label": "F { 1 } { 3 } ≈ ( $ 0 . 3 $ )", "predi": "F { 1 } { 3 } ≈ ≈ $ 0 . 3 $ )"}, "mask_6eac5f81-d2d9-11ed-9c3c-adb24bcec076_10.jpg": {"label": "3 0 . 4 m ^ 2 = ( $ 0 . 3 0 4 $ ) d m ^ 2", "predi": "3 0 . 4 c m ^ 2 = ( $ 0 . 3 0 4 $ ) d m ^ 2"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_29.jpg": {"label": "1 1 1 1 1 1 7 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1"}, "mask_d5732201-e34e-11ed-89fd-5b96792f49c5_24.jpg": {"label": "1 0 P ( P P", "predi": "1 0 P P P"}, "mask_d59db621-ee5f-11ed-a88d-1b65076756ce_12.jpg": {"label": "1 2 0 ° + 3 5 ° + 2 5 = $ 1 8 0 ° $", "predi": "1 2 0 ° + 3 5 ° + 2 5 ° = $ 1 8 0 ° $"}, "mask_5e87a9b1-e4eb-11ed-a16d-d77d12017e60_44.jpg": {"label": "1 0 . 0 8 =", "predi": "0 . 0 8 ="}, "mask_00b10841-d52d-11ed-8271-af52227f6dad_0.jpg": {"label": "F { 2 } { 3 } * F { 1 } { 4 } = F { ( $ 1 $ ) } { ( $ 6 $ }", "predi": "F { 2 } { 3 } * F { 1 } { 4 } = F { ( $ 1 $ ) } { ( $ 6 $ ) }"}, "mask_e7d61241-d9f3-11ed-8830-1b82f9d4360b_14.jpg": {"label": "1 0 0 分 米 = ( $ 1 0 $ )", "predi": "1 0 0 分 米 = ( $ 1 0 $ ) 米"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_28.jpg": {"label": "4 9 天 = ( $ 7 $ ) 周", "predi": "4 9 五 = ( $ 7 $ ) 厘"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_23.jpg": {"label": "四 成 五 = ( $ 4 5 $ ) %", "predi": "成 成 五 = ( $ 4 5 $ ) %"}, "mask_3a902bb1-d210-11ed-8482-25f80a3eb141_13.jpg": {"label": "F { 4 } { 9 } * F { 1 } { 6 } = $ F { 8 } { 3 } $", "predi": "F { 4 } { 9 } / F { 1 } { 6 } = $ F { 8 } { 3 } $"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_33.jpg": {"label": "4 ( 4 分 5", "predi": "4 - ( $ 4 $ ) 4 5"}, "mask_f5b434f1-d53e-11ed-a640-fb9b4f351d70_7.jpg": {"label": "F { 2 0 } { 8 0 } = F { 4 } { 1 6 }", "predi": "F { 2 0 } { 8 0 } $ = $ F { 4 } { 1 6 }"}, "mask_0a7c0bc1-c3d7-11ed-82f9-a12f3266b254_26.jpg": {"label": "5 6 = $ 9 $", "predi": "5 - 6 = $ 9 $"}, "mask_faeec271-ddd4-11ed-ac93-7f3d98aaa450_41.jpg": {"label": "F { 4 } { 5 } + F { 1 } { 4 } = $ F { 4 * 4 } { 3 * 4 } + F { 1 * 3 } { 4 * 3 } = F { 1 9 } { 1 2 } $", "predi": "F { 4 } { 3 } + F { 1 } { 4 } = $ F { 4 * 4 } { 2 * 4 } + F { 1 * 3 } { 4 * 3 } = F { 1 9 } { 1 2 } $"}, "mask_5af43a51-d9d5-11ed-9064-19b9b8c76ddb_21.jpg": {"label": "7 0 . 8 毫 米 = F { ( $ 7 0 8 $ ) } { ( $ 1 0 $ ) } 厘 米 = ( $ 7 . 0 8 $ ) 厘 米", "predi": "7 0 . 8 毫 米 = F { ( $ 7 0 3 $ ) } { ( $ 1 0 $ ) } 厘 米 = ( $ 7 . 0 8 $ ) 厘 米"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_10.jpg": {"label": "F { 3 } { 2 } - F { 2 } { 5 } - F { 3 } { 5 } = $ F { 3 } { 2 } - ( F { 2 } { 5 } + F { 3 } { 5 } ) = F { 3 } { 2 } - 1 = F { 3 } { 2 } - F { 2 } { 2 } = F { 3 - 2 } { 2 } = F { 1 } { 2 } $", "predi": "F { 3 } { 2 } - F { 2 } { 5 } - F { 3 } { 5 } = $ F { 3 } { 2 } - ( F { 2 } { 5 } + F { 3 } { 5 } ) = F { 3 } { 2 } - 1 = F { 3 } { 2 } - 2 - 2 - F { 2 } { 2 } = F { 1 } { 2 } $"}, "mask_fd943c41-e661-11ed-be88-c30ba7980042_21.jpg": {"label": "5 0 + 4 3 = $ 8 7 $", "predi": "5 0 + 3 7 = $ 8 7 $"}, "mask_ed7c7ff1-e505-11ed-a388-552c31106341_11.jpg": {"label": "5 7 5 * 1 0 0 = $ 5 7 5 $", "predi": "5 . 7 5 * 1 0 0 = $ 5 7 5 $"}, "mask_1e5582d1-db5f-11ed-b4d9-f93ec1051164_34.jpg": {"label": "5 4 / 7 = $ 7 $", "predi": "5 4 / 7 = 7"}, "mask_d8972341-c1ad-11ed-b768-613d9569d463_3.jpg": {"label": "F { 2 2 } { 5 } / 5 5", "predi": "F { 2 2 } { 5 } / 5 5 = $ 5 $"}, "mask_1a492c91-e8f8-11ed-8849-9bd50053a95e_21.jpg": {"label": "$ 0 0 $", "predi": "2 2 = $ 6 $"}, "mask_5f435420-e320-11ed-99f6-81f8f4ba29d8_3.jpg": {"label": "0 = $ 2 0 m . 6 = $ 2 0 m . 1 = $ 6 c m $", "predi": "0 = 2 c m . 6 = 2 c m . 1 = 6 c m"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_3.jpg": {"label": "八 成 八 = ( $ 8 8 $ ) %", "predi": "公 成 公 = ( $ 8 8 $ ) %"}, "mask_fd73be91-dea3-11ed-bd8b-115959bdf4b9_28.jpg": {"label": "9 6 角 = ( $ 9 $ ) 元 ( $ 6 $ ) 角", "predi": "6 6 角 = ( $ 9 $ ) 元 ( $ 6 $ ) 角"}, "mask_1f64cfc1-cc94-11ed-bb44-3b11b70e4561_11.jpg": {"label": "2 8 / 4 $ = $ 2 6 4", "predi": "2 8 / 4 $ = $ 3 6 / 4"}, "mask_5cca42f1-e643-11ed-830d-cbe284fabe89_3.jpg": {"label": "* 3 . 6 = $ 3 . 6 y $", "predi": "* 3 . 6 = $ 3 . 6 a $"}, "mask_1f79fd31-c8a9-11ed-9c74-9f765749440d_30.jpg": {"label": "1 3 3 1 8 9", "predi": "1 3 * 3 = $ 1 8 9 $"}, "mask_ff42bd31-d6d7-11ed-a6ca-b943d166e6f5_20.jpg": {"label": "2 7 / 9 = 3", "predi": "2 7 / 9 = $ 3 $"}, "mask_5a116f41-dca5-11ed-912e-f724c0c2a1a0_38.jpg": {"label": "1 0 0 * 0 5 2 = $ 0 . 0 0 5 2 $", "predi": "1 0 0 * 0 . 5 2 = $ 0 . 0 0 5 2 $"}, "mask_4d9e94b1-ddde-11ed-a8ae-f7ba1c3f5d5f_43.jpg": {"label": "6 5 $ 8 $ ) 5 5", "predi": "6 5 5 4 $ > $ 5 5"}, "mask_fbc4ec91-e637-11ed-9d6c-3d1a33781e77_39.jpg": {"label": "F { 7 } { } - F { 5 } { 8 } = $ F { 1 4 } { 1 8 } - F { 5 } { 1 8 } = F { 1 } { 2 } $", "predi": "7 - F { 7 } { } - F { 5 } { 8 } = $ F { 1 4 } { 1 8 } - F { 1 } { 1 8 } = F { 1 } { 2 } $"}, "mask_fee6e7d1-ebef-11ed-be8b-7fe711c44f9e_16.jpg": {"label": "3 4 / 8 = $ 4 $ P $ 2 $", "predi": "3 4 / 8 = $ 4 P 2 $"}, "mask_e8144521-e988-11ed-bee9-a3529571f462_29.jpg": {"label": "F { 1 } { 5 } * F { 3 } { 5 } = ( $ F { 1 } { 4 } $ ) : F { 3 } { 4 }", "predi": "F { 1 } { 5 } : F { 3 } { 5 } = ( $ F { 1 } { 4 } $ ) : F { 3 } { 4 }"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_13.jpg": {"label": "3 天 = ( $ 7 2 $ ) 时", "predi": "3 千 = ( $ 7 2 $ ) 时"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_16.jpg": {"label": "F { 1 } { 4 } + F { 5 } { 1 2 } - F { 1 } { 3 } = $ F { 3 } { 1 2 } + F { 5 } { 1 2 } - F { 3 + 5 - 4 } { 1 2 } = F { 1 } { 3 } $", "predi": "F { 1 } { 4 } + F { 5 } { 1 2 } - F { 1 } { 3 } = $ F { 3 } { 1 2 } + F { 5 } { 1 2 } - F { 4 + 5 - 4 } { 1 2 } { 1 2 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { } { 1 } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } {"}, "mask_ec106de1-e1c5-11ed-bdc9-a3caf80f747e_22.jpg": {"label": "闰 年 一 年 = ( 3 6 6 ) 天", "predi": "角 秒 成 成 = ( $ 3 6 6 $ ) 千"}, "mask_1fcd82d1-c524-11ed-81dc-f92359da3700_27.jpg": {"label": "( $ 4 6 5 6 0 $ ) < 4 6 . 5 6 1 > ( $ 4 6 . 5 $", "predi": "( $ 4 6 5 6 0 $ ) < 4 6 . 5 6 1 < ( $ 4 6 . 5 $"}, "mask_1bd14c01-e1cd-11ed-bd8a-b9aea5ee9ad1_24.jpg": {"label": "4 9 / 6 = $ 9 P 5 $", "predi": "4 9 / 6 = 9 P 5"}, "mask_5b4c45b1-ebfa-11ed-9a2c-1fe9fa50ec60_5.jpg": {"label": "4 8 时 = ( $ 2 $ ) 日", "predi": "4 8 时 = ( $ 2 $ ) m"}, "mask_fee728a1-d095-11ed-a34e-810c1e9c0e59_21.jpg": {"label": "四 成 五 = ( $ 4 5 $ ) %", "predi": "成 成 五 = ( $ 4 5 $ ) %"}, "mask_6e7a5381-c7d7-11ed-a1e3-915060c0ce49_2.jpg": {"label": "F { 1 7 } { 1 0 } - ( F { 3 } { 4 } + F { 1 } { 5 ) = $ F { 1 7 } { 1 0 } - F { 1 9 } { 2 0 } = F { 1 5 } { 2 0 } $", "predi": "F { 1 7 } { 1 0 } - ( F { 3 } { 4 } + F { 1 } { 5 } ) = $ F { 1 7 } { 1 0 } - F { 1 9 } { 2 0 } = F { 1 5 } { 2 0 } $"}, "mask_1f520ae1-cee0-11ed-9835-2789202e11c4_39.jpg": {"label": "1 米 - 7 厘 米 = $ 9 3 厘 米 $", "predi": "1 米 - 7 厘 米 = $ 9 3 厘 米"}, "mask_f7932931-cd5e-11ed-96b9-2369537faf35_6.jpg": {"label": "3 4 * 7 = $ 2 3 8 $", "predi": "3 4 * 7 = $ 2 2 8 $ )"}, "mask_6dd2f001-dd0c-11ed-9d84-d32c0689dfde_22.jpg": {"label": "1 2 * F ( $ { } { 1 2 } $ ) = 1", "predi": "1 2 * ( $ F { } { 1 2 } $ ) = 1"}, "mask_dfc40281-db68-11ed-8612-758c44062f71_30.jpg": {"label": "角 = ( $ 5 $ ) 元", "predi": "角 = ( $ 5 $ ) 元 ("}, "mask_fa59ded1-cca4-11ed-8388-2be924829a12_32.jpg": {"label": "F { 8 } = $ 2 F { 2 } { 3 } $", "predi": "F { 8 } { 3 } = $ 2 F { 2 } { 3 } $"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_12.jpg": {"label": "六 成 八 = ( $ 6 8 $ ) %", "predi": "公 成 八 = ( $ 6 8 $ ) %"}, "mask_fe660dd1-c8aa-11ed-81c2-ad6785b79d8f_47.jpg": {"label": "0 0 1 0 1 1", "predi": "0 0 0 / 6 = $ 1 2 $"}, "mask_dd057ce1-cdc1-11ed-af75-0d51e24b6e1d_33.jpg": {"label": "( 6 2 8 1 2", "predi": "2 8 . 1 2"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_10.jpg": {"label": "( 八 七 1", "predi": "( ) 亿"}, "mask_e8806a91-c8b1-11ed-a824-af0dea8b9499_41.jpg": {"label": "9 $ / * $ 3 = 3", "predi": "9 $ / $ 3 = 3"}, "mask_1b6ecc31-d541-11ed-8f3b-c1e5fb0428f2_11.jpg": {"label": "1 0 * F { 2 } { 5 } = $ 4 $", "predi": "1 0 / F { 2 } { 5 } = $ 4 $"}, "mask_6e7d6651-d926-11ed-ba38-1bf6c3912b8b_15.jpg": {"label": "F { 7 } { 2 } + F { 5 } { 2 } = $ F { 1 2 ] { 2 } $", "predi": "F { 7 } { 2 } + F { 5 } { 2 } = $ F { 1 2 } { 2 } $"}, "mask_e0379c01-c708-11ed-8807-173aea3c7e70_22.jpg": {"label": "6 0 0 $ < $ 6 千 米", "predi": "6 0 0 米 $ < $ 6 千 米"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_32.jpg": {"label": "六 成 五 = ( $ 6 5 $ ) %", "predi": "公 成 五 = ( $ 6 5 $ ) %"}, "mask_debad8b1-d38d-11ed-a78c-1fb3baefd98b_45.jpg": {"label": "F { 8 } 2 * 6 3 4 0 0", "predi": "$ F { 8 } { 4 0 0 0 0 0 0 0 0 $"}, "mask_ed61e2d1-bf44-11ed-a345-ed3f6b44010c_19.jpg": {"label": "3 2 / ( 1 1 - 3 ) = $ 4 $", "predi": "$ 3 2 / ( 1 1 - 3 ) = 4 $"}, "mask_f25ca981-dc65-11ed-9e2a-654c8a600ea8_24.jpg": {"label": "F { 2 3 } { 1 0 } = $ 2 F { 3 } { 1 0 } $", "predi": "F { 2 3 } { 1 0 } = $ 2 F { 3 } { 1 0 0 } $"}, "mask_dfb08741-e7ad-11ed-acec-a30475fb8bda_32.jpg": {"label": "5 ( - 3", "predi": "5 ( - )"}, "mask_1bf50501-df7a-11ed-bac7-fb2f45b6f854_17.jpg": {"label": "F { 7 } { 8 } 8 . 7 5", "predi": "F { 7 } 8 8 . 7 5"}, "mask_fe220661-dde7-11ed-b98d-b121d22f8930_24.jpg": {"label": "1 0 6 5 0 2 4 3 ≈ ( $ 1 0 6 5 . 0 $ ) 万", "predi": "1 0 6 5 0 2 4 3 ≈ ( $ 1 0 0 5 . 0 $ ) 万"}, "mask_e25e89d1-dea5-11ed-a862-fbf397740eb1_0.jpg": {"label": "5 m : 1 0 m = ( $ 5 0 0 $ ) : 1", "predi": "5 m : 1 0 m m = ( $ 5 0 0 $ ) : 1"}, "mask_ec73a221-d9e6-11ed-a28b-4f5aa8dad57c_43.jpg": {"label": "4 0 + 4 2 =", "predi": "4 0 + 4 2 = $ 8 $"}, "mask_4e1a9a71-eda0-11ed-9242-11a75308479a_21.jpg": {"label": "5 6 0 0 2 0 9 8 ≈ ( $ 5 6 0 0 . 2 1 $ ) 万", "predi": "5 6 0 0 2 0 9 8 ≈ $ 5 6 0 0 . 2 1 $ ) 万"}, "mask_4bde2e70-e41d-11ed-b190-a701d8346fd0_3.jpg": {"label": "0 . 3 亿 = ( $ 3 0 0 0 0 0 0 0 $ )", "predi": "0 . 3 亿 = ( $ 3 0 0 0 0 0 0 0 $"}, "mask_0f8e41d1-d3a1-11ed-b91e-d7d3eedf17f4_16.jpg": {"label": "1 2 0 0 d m ^ 3 $ < $ 3 1 8 0 0", "predi": "1 2 0 0 d m ^ 3 $ < $ 3 1 8 0 0 L"}, "mask_3fb92b21-ef2d-11ed-a1e2-39e3bc64e098_33.jpg": {"label": "8 * 3 * 9 * 1 2 6 = $ 2 7 0 0 0 $", "predi": "8 * 3 * 9 * 1 2 5 = $ 2 7 0 0 0 $"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_30.jpg": {"label": "1 1 1 1 1 1 1 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_df353551-dc3f-11ed-80a4-451c59fab8cd_21.jpg": {"label": "F { 1 7 2 0 + 5 = $ 3 4 $", "predi": "F { 1 7 + 9 + 5 = $ 3 4 $"}, "mask_f74ab241-efff-11ed-951c-9305a4076d73_0.jpg": {"label": "F { 2 0 0 7 } { 2 0 0 8 } $ = $ F { 2 0 0 8 } { 2 0 0 9 }", "predi": "F { 2 0 0 7 } { 2 0 0 8 } $ = $ F { 2 0 0 8 } { 2 0 0 9 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8"}, "mask_e6e2a681-e4f4-11ed-b274-7d49d3347fd3_7.jpg": {"label": "1 1 P 7", "predi": "1 1 / 7 = $ 5 $ P $ 6 $"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_37.jpg": {"label": "5 1 1 5 五", "predi": "0 . 5 2 1 = ( $ 5 2 0 $ ) g"}, "mask_d24cc831-d9ec-11ed-a484-1b963266065c_2.jpg": {"label": "七 = 5 0 1 . 6 = 3 0 m . 1 = $ 5 0 P $", "predi": "a = 5 c m . 6 = 3 c m . 1 = $ 5 0 $"}, "mask_e12ecd31-ddc5-11ed-8f88-0be076fb966d_21.jpg": {"label": "9 0 = 2 0 = $ 7 0 $", "predi": "9 0 - 2 0 = $ 7 0 $"}, "mask_ebfeb211-e41f-11ed-8d9a-5fe783565793_34.jpg": {"label": "5 4 / 7 = $ 7 $", "predi": "5 4 / 7 = 7"}, "mask_f437de91-c7df-11ed-89db-09b66e187465_23.jpg": {"label": "( $ 5 $ ) 一 0 0", "predi": "( $ 5 $ ) * 2 0"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_30.jpg": {"label": "= = $ 2 $", "predi": "= $ 1 2 0 $"}, "mask_4b6a7660-cd66-11ed-a2bf-17561ea6eab9_24.jpg": {"label": "( $ F ( { 5 } { 4 } ) $ * 0 . 8 = 1", "predi": "( $ F { 5 } { 4 } $ ) * 0 . 8 = 1"}, "mask_e7dcf541-c659-11ed-a5f4-69b0e68cef5f_38.jpg": {"label": "F { 3 } { 1 4 } + F { 5 } { 2 8 } = $ F { 1 } { 2 } $", "predi": "F { 3 } { 1 4 } + F { 5 } { 2 8 } = $ F { 1 } { 2 7 } $"}, "mask_1bd14c01-e1cd-11ed-bd8a-b9aea5ee9ad1_23.jpg": {"label": "4 8 / 6 = $ 7 P 6 $", "predi": "4 8 / 6 = 7 P 6"}, "mask_1f520ae1-cee0-11ed-9835-2789202e11c4_34.jpg": {"label": "1 4 厘 米 - 9 厘 米 = $ 5 厘 米 $", "predi": "1 4 厘 米 - 9 厘 米 = $ 5 厘 米"}, "mask_07a96731-d935-11ed-97d1-ef33d95ffe1a_4.jpg": {"label": "F { 1 } { 3 } : F { 5 } { 6 } = ( $ F { 3 } { 2 5 } $ ) : F { 3 } { 1 0 }", "predi": "F { 1 } { 3 } : F { 5 } { 6 } = ( $ F { 6 } { } { 0 } : F { 3 } { 1 0 } } : F { 3 } { 1 0 }"}, "mask_e09e7140-d929-11ed-b301-7b407328673d_20.jpg": {"label": "( $ 6 . 8 6 $ ) < 6 . 8 7 > ( $ 6 . 8 8 $ )", "predi": "( $ 6 . 8 6 $ ) < 6 . 8 7 < ( $ 6 . 8 8 $ )"}, "mask_e82e14e1-dac5-11ed-b39b-5dc06b025163_28.jpg": {"label": "5 8 + 2 3 = $ 7 9 $", "predi": "5 6 + 2 3 = $ 7 9 $"}, "mask_1f4d12d1-e41e-11ed-88e2-79dec01dc83c_40.jpg": {"label": "3 4 / 7 = $ 5 P 1 $", "predi": "3 4 / 7 = 5 P 1"}, "mask_e14d5461-eb3c-11ed-b35b-038568acb555_41.jpg": {"label": "3 4 / 7 = $ 5 P 1 $", "predi": "3 4 / 7 = 5 P 1"}, "mask_3c067400-e353-11ed-b9b9-7f3b532fbb2b_35.jpg": {"label": "2 4 - 2 0 = 1 1", "predi": "2 4 - 2 0 = $ 1 1 $"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_8.jpg": {"label": "1 0 2 + 8 0 8 - = $ 9 1 0 $", "predi": "1 0 2 + 8 0 8 = $ 9 1 0 $"}, "mask_3ac2d931-dead-11ed-b020-c14f959f2d67_44.jpg": {"label": "6 0 角 = ( $ 6 $ ) 元", "predi": "0 角 = ( $ 6 $ ) 元"}, "mask_e29c9341-e1d1-11ed-aa71-0bbe3ffbd1f9_44.jpg": {"label": "F { 4 0 } { 5 6 } = $ F { 5 } { 7 } $", "predi": "F { 4 0 0 } { 5 6 } = $ F { 5 } { 7 } $"}, "mask_f519b381-cc47-11ed-a7ad-ff031fc9473a_30.jpg": {"label": "$ $ * $ $ = $ $", "predi": "$ $ * $ = $ $"}, "mask_dc320d31-eb42-11ed-942d-b958113e4192_13.jpg": {"label": "3 天 = ( $ 7 2 $ ) 时", "predi": "3 元 = ( $ 7 2 $ ) 时"}, "mask_3b471c30-d78d-11ed-a82a-f5347e681596_24.jpg": {"label": "F { 1 } { 8 } = ( $ 1 $ ) / ( $ 8 $ ) = ( $ 0 . 1 2 $ )", "predi": "F { 1 } { 8 } = ( $ 1 $ ) / ( $ 8 $ ) = ( $ 0 . 1 2 $"}, "mask_5d186051-e43d-11ed-b93b-57534a6bb968_4.jpg": {"label": "F ( 3 } { 1 0 0 } = $ 0 . 0 3 $", "predi": "F { 3 } { 1 0 0 } = $ 0 . 0 3 $"}, "mask_e8c49181-e426-11ed-b8c3-5f838100e704_41.jpg": {"label": "F { ( $ 2 2 $ ) } { 3 0 } = F { 7 2 } { 8 8 }", "predi": "F { ( $ 2 2 $ ) } { 3 0 } = F { 7 2 } { 8 0 }"}, "mask_dbf1b4e1-ca3d-11ed-bf5c-9d1f51333b2f_2.jpg": {"label": "2 4 厘 米 = ( $ 2 $ ) 分 米 ( $ 4 $ ) 厘", "predi": "2 4 厘 米 = ( $ 2 $ ) 分 米 ( $ 4 $ ) 厘 米"}, "mask_5a5fe721-ce30-11ed-a033-bf7dfd9d8c37_38.jpg": {"label": "0 . 0 6 8 m ^ 2 = ( $ 0 . 0 0 0 6 8 $ ) d m ^ 2", "predi": "0 . 0 6 8 m ^ 2 = ( $ 0 . 0 0 0 8 $ ) d m ^ 2"}, "mask_4f6d35b1-e69b-11ed-a49f-e5fd73654b31_4.jpg": {"label": "F { 7 } { 9 } : F { 1 8 } { 2 7 } = $ 7 : $", "predi": "F { 7 } { 9 } : F { 1 8 } { 2 7 } = $ 7 : : $"}, "mask_db5f07b1-bffe-11ed-a604-2d243eecc09e_44.jpg": {"label": "1 0 个 一 亿 = ( $ 十 $ ) 亿", "predi": "1 0 分 - 亿 = ( $ + $ ) 亿"}, "mask_faac9cb1-ebf3-11ed-8e02-7125afa8de5c_41.jpg": {"label": "3 8 0 + 7 2 = $ 4 5 2 $", "predi": "8 8 0 + 7 2 = $ 4 5 2 $"}, "mask_ec106de1-e1c5-11ed-bdc9-a3caf80f747e_13.jpg": {"label": "1 年 零 3 月 = ( $ 4 $ ) 月", "predi": "1 成 2 3 万 = ( $ 4 $ ) 万"}, "mask_df9bfda1-dd14-11ed-b08c-9b32cf8c2c50_25.jpg": {"label": "3 5 - 8 = $ 2 7 $", "predi": "3 5 - 8 = $ 2 7 7 $"}, "mask_f97e3961-e983-11ed-bac1-db1612922de1_22.jpg": {"label": "( $ 4 1 $ ) / 6 = 6 P ( $ 5 $ )", "predi": "( $ 4 1 $ ) / 6 = 6 P ( $ 5 $"}, "mask_1ba19d91-c48e-11ed-94be-ab70e1d7b3a9_35.jpg": {"label": "$ 五 5 $", "predi": "$ F { } { }"}, "mask_f02ae1a1-ca2b-11ed-8d7b-4b03830502be_27.jpg": {"label": "1 . 2 : ( $ 0 . 0 3 2 $ ) = 3 0 : 0 . 8", "predi": "1 . 2 : ( $ 0 . 0 3 2 $ ) 2 3 0 : 0 . 8"}, "mask_6a071911-d929-11ed-b5bd-1f044f4f54b3_14.jpg": {"label": "$ 8 0 0 $ ) 克 = 8 千 克", "predi": "$ 8 0 0 0 $ ) 克 = 8 千 克"}, "mask_dc320d31-eb42-11ed-942d-b958113e4192_16.jpg": {"label": "平 年 一 年 = ( $ 3 6 5 $ ) 天", "predi": "成 时 成 成 = ( $ 3 6 5 $ ) 万"}, "mask_5af43a51-d9d5-11ed-9064-19b9b8c76ddb_24.jpg": {"label": "7 3 克 = F { ( $ 7 3 $ ) } { ( $ 1 0 0 0 $ ) } 千 克 = ( $ 0 . 0 7 3 $ ) 千 克", "predi": "7 3 克 = F { ( $ 7 3 $ ) } { ( $ 1 0 0 0 $ ) } 千 克 = ( $ 0 . 0 7 3 $ ) 千 克 克"}, "mask_d69e4070-deac-11ed-af3b-d19d1131ae48_8.jpg": {"label": "F { 3 } { 5 } : F { 3 } { 1 0 } = ( $ 5 $ ) : F { 5 } { 6 }", "predi": "F { 3 } { 5 } : F { 9 } { 1 0 } = ( $ 5 $ ) : F { 5 } { 6 }"}, "mask_0cdd2591-cc90-11ed-a231-692348c94de2_4.jpg": {"label": "7 5 0 c m ^ 3 = ( 0 . 7 5 ) L", "predi": "7 5 0 c m ^ 3 = ( $ 0 . 7 5 $ ) L"}, "mask_fee728a1-d095-11ed-a34e-810c1e9c0e59_24.jpg": {"label": "三 成 = ( $ 3 0 $ ) %", "predi": "成 成 = ( $ 3 0 $ ) %"}, "mask_f40b3b41-c719-11ed-8c74-cb8055fb8532_9.jpg": {"label": "0 9 * 9 = $ 9 8 1 $", "predi": "9 9 * 9 = $ 9 8 1 $"}, "mask_d8659441-e973-11ed-8d17-f3444709f1b5_3.jpg": {"label": "五 ( $ 七 1 $ ) = 米 4 ( 4 0 0", "predi": "4 ( $ 7 1 $ ) = 米 4 4 4 0 0"}, "mask_ff3307a1-c250-11ed-8390-15967ebdcb8c_19.jpg": {"label": "8 + 5 米 7 分 - ( $ 8 7 7 $ )", "predi": "8 + 5 米 7 分 - 元 ( $ 8 7 7 $ )"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_45.jpg": {"label": "1 . 4 - 5", "predi": "1 . 成 - 成 0"}, "mask_3f8a5150-dc05-11ed-b374-395e26d72b1a_16.jpg": {"label": "F { 2 3 } { 1 1 } = $ 2 F { 1 } { 1 1 } $", "predi": "F { 2 3 } { 1 1 } = $ 2 F { 1 } { 1 1 1 } $"}, "mask_1cd48711-c8a6-11ed-a216-cfa1e1619377_8.jpg": {"label": "F { 1 1 } { 4 9 } * 9 = $ F { 1 1 * 9 } { 4 5 } = F { 9 9 } { 4 5 } $", "predi": "F { 1 1 } { 4 5 } * 9 = $ F { 1 1 * 9 } { 4 5 } = F { 9 9 } { 4 5 } $"}, "mask_f2928141-c324-11ed-b851-df3ce4b813ae_18.jpg": {"label": "7 + F { 2 } { 1 5 } =", "predi": "7 + <PERSON> { 2 } { 1 5 }"}, "mask_4a1f3ec0-d366-11ed-b357-85dfe2ae731c_21.jpg": {"label": "2 6 * 3 0 = $ 1 8 0 $", "predi": "2 6 * 3 0 = $ 7 8 0 $"}, "mask_f76dfc01-c71e-11ed-8440-dd86e7360887_26.jpg": {"label": "4 6 9 9 / 3 = $ 2 3 4 $", "predi": "6 9 9 / 3 = $ 2 3 4 $"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_35.jpg": {"label": "1 . 0 1 1 = ( $ $ ) )", "predi": "1 . 0 1 1 = ( $ 6 0 $ ) d m"}, "mask_d931df31-e434-11ed-aa5c-87bc82f7255f_12.jpg": {"label": "F { 1 2 } { 2 } = $ 1 2 5 0 $", "predi": "F { 1 2 } { 3 0 } = $ 1 2 /"}, "mask_f9844de1-e4e3-11ed-9aff-cdd043c02f10_20.jpg": {"label": "2 0 元 = ( $ $ ) 张 1 元 + 1 张 1 0 元 + 1 克", "predi": "2 0 元 = ( $ 1 元 $ ) 张 1 张 1 张 1 张 1"}, "mask_ed61e2d1-bf44-11ed-a345-ed3f6b44010c_15.jpg": {"label": "$ 5 * ( 2 8 / 7 ) = 2 0 $", "predi": "$ 5 * ( 2 8 / 7 ) = 2 0"}, "mask_1d2d1021-d935-11ed-bc64-6be99973e898_28.jpg": {"label": "2 4 7 + 3 7 = $ 2 8 4 $", "predi": "2 4 7 + 3 7 = $ 2 8 4 4 4 $"}, "mask_1e8ee371-ed54-11ed-af4f-1b6df7ae09e2_17.jpg": {"label": "2 5 . 1 1 % ≈ ( $ 2 5 . 1 $ )", "predi": "2 5 . 1 1 % ≈ ( $ 2 5 . 1 $"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_32.jpg": {"label": "1 1 1 1 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_1a25c051-c18b-11ed-926e-7b1c74eae2d9_2.jpg": {"label": "3 c m : 6 d m = $ 1 : : 2 0 $", "predi": "3 c m : 6 d m = $ 1 : 2 0 $"}, "mask_4bcd4fb1-c8ac-11ed-b858-e997ff37de5d_40.jpg": {"label": "9 7 0 - 6 0 0 = $ 3 1 0 $", "predi": "9 7 0 - 6 6 0 = $ 3 1 0 $"}, "mask_d01662a1-d47a-11ed-b8e7-7d40b1959cd5_3.jpg": {"label": "4 { $ 3 $", "predi": "$ 1 3 $"}, "mask_dc8f6901-d123-11ed-8559-8d0ce8d3b4ac_14.jpg": {"label": "$ 0 . 5 x / 0 . 5 = $", "predi": "$ 0 . 5 x / 0 . 5 ="}, "mask_1ca7ceb1-dd13-11ed-ab7f-5b28ff4dd439_23.jpg": {"label": "4 / 4 0 = F { ( $ 4 $ ) } { ( $ 4 0 $ }", "predi": "4 / 4 0 = F { ( $ 4 $ } { ( $ 4 0 $ }"}, "mask_4d3094d1-ec7d-11ed-8646-d7f4b76360f5_29.jpg": {"label": "F { 3 5 } { 8 2 } = ( $ 3 5 $ ) / ( $ 8 2 $ )", "predi": "F { 3 5 } { 8 2 } = ( $ 3 5 $ ) / ( $ 8 5 $ )"}, "mask_dd413da1-d043-11ed-af06-b70f51ba59d9_0.jpg": {"label": "七 四 5 = $ 1 0 2 $", "predi": "五 五 = $ 1 0 2 $"}, "mask_ff60fe11-f006-11ed-9011-cdf127881d05_9.jpg": {"label": "1 6 * 3 + 2 0 = $ 6 8 $", "predi": "1 6 * 3 + 2 0 = $ 6 8 1 $"}, "mask_e6d8b7a1-cf4c-11ed-a851-c7174b1f6160_33.jpg": {"label": ": F { 8 } { 1 5 } = $ F { 1 } { 1 } * F { } { 4 } = F { 3 } { 4 } $", "predi": ": F { 8 } { 1 5 } = $ F { } { 5 } * F { 1 } { 4 } = F { 3 } { 4 } $"}, "mask_d2774d31-d613-11ed-9deb-a1d831da24bb_17.jpg": {"label": "0 . 4 5 / 0 .", "predi": "0 . 4 5 / 0 . 5 -"}, "mask_00ab4401-dd11-11ed-b69a-d76908d4f989_36.jpg": {"label": "2 0 万 = ( $ 2 0 0 0 0 0 $ )", "predi": "2 0 万 = ( $ 2 0 0 0 0 0 $"}, "mask_dbf1b4e1-ca3d-11ed-bf5c-9d1f51333b2f_8.jpg": {"label": "3 0 米 = ( $ 3 0 $ ) 分 米", "predi": "3 0 米 = ( $ 3 0 $ ) 分 米 3 0 0"}, "mask_3c067400-e353-11ed-b9b9-7f3b532fbb2b_39.jpg": {"label": "5 - 6 = $ 2 0 $", "predi": "- 5 - 5 = $ 2 0 $"}, "mask_de424501-e65f-11ed-ab77-fd2449fd0b42_5.jpg": {"label": "4 9 - 4 0 = $ 4 $", "predi": "4 9 - 4 0 = $ 9 $"}, "mask_4b60f9f1-d21b-11ed-a137-e75cd9a33ba9_5.jpg": {"label": "5 t 7 2 0 k g = ( $ 5 . 7 2 $ ) t", "predi": "5 t 7 2 0 k g = ( $ 5 . 7 2 $ ) t t"}, "mask_e1301d41-c25c-11ed-a2e9-914bbd2010a7_0.jpg": {"label": "$ 1 5 - 8 $ = $ 7 $ ( 个 )", "predi": "$ 1 5 - 8 $ = $ 7 $ ( 分 )"}, "mask_ebdc0621-cb79-11ed-a145-01c7965c6742_16.jpg": {"label": "9 2 0 / 8 = $ 1 1 5 $", "predi": "9 2 0 / 8 = $ 1 5 $"}, "mask_2ecb1421-ee62-11ed-93cc-db66e90ada16_37.jpg": {"label": "F { 2 } { 5 } + F { 4 } { 1 5 } = $ F { 6 } { 1 5 } + = F { 1 0 } { 1 5 } = F { 2 } { 3 } $", "predi": "F { 2 } { 5 } + F { 4 } { 1 5 } = $ F { 6 } { 1 5 } + F { 1 0 } { 1 5 } = F { 2 } { 3 } $"}, "mask_fb6dcf21-c0d8-11ed-b20d-c51d3b5ab303_41.jpg": {"label": "3 6 = ( $ 7 $ ) + ( $ 6 $ )", "predi": "3 6 = ( $ 7 $ ) + ( $ 6 $"}, "mask_0bedc2f1-e1cd-11ed-b72c-89f65f93903e_13.jpg": {"label": "F { 4 } { 9 } = F { 4 * ( $ 6 $ ) } { 9 * ( $ 6 $ ) } = $ F { ( $ 2 4 $ ) } { ( $ 5 4 $ ) } $", "predi": "F { 4 } { 9 } = F { 4 * ( $ 6 $ ) } { 9 * ( $ 6 $ ) } = F { ( $ 2 4 $ ) } { ( $ 5 4 $ ) }"}, "mask_1ab1d2f1-cf9d-11ed-b0e2-15cbdd81d5fa_18.jpg": {"label": "2 * ( $ F { 1 } { 2 } $ = 1", "predi": "2 * ( $ F { 1 } { 2 } $"}, "mask_d83ca591-c20d-11ed-82ce-e3ec7feaad83_13.jpg": {"label": "6 * ( $ F { 1 } { 1 6 } $ ) = 1", "predi": "1 6 * ( $ F { 1 } { 1 6 } $ ) = 1"}, "mask_6a2bdc21-cf86-11ed-b16e-716b3d88ca57_0.jpg": {"label": "3 ) ( a + b ) * c = $ a $ * $ c $ + $ b $ * $ c $", "predi": "( a + b ) * c = $ a $ * $ c $ + $ b $ * $ c $"}, "mask_df647181-df46-11ed-a078-0b8b0a1f8d54_20.jpg": {"label": "0 . 1 4 d m ^ 2 = ( $ 1 4 0 $ ) c m ^ 3", "predi": "0 . 1 4 d m ^ 3 = ( $ 1 4 0 $ ) c m ^ 3"}, "mask_ed61e2d1-bf44-11ed-a345-ed3f6b44010c_11.jpg": {"label": "$ ( 5 0 - 4 2 ) * 7 = 5 6 $", "predi": "$ 1 5 0 - 4 2 ) * 7 = 5 6 $"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_43.jpg": {"label": "1 * 1 0", "predi": "1 1 0 * 1 0 0 = $"}, "mask_e9fba181-c322-11ed-9932-a3c37050e59d_24.jpg": {"label": "8 2 0 * 3 0 = $ 2 4 6 6 0 $", "predi": "8 2 0 * 3 0 = $ 2 4 6 0 0 $"}, "mask_1c480f91-d35c-11ed-b6fe-cd04d425a1d6_21.jpg": {"label": "1 * 2 0 = $ 1 4 2 0 $", "predi": "7 1 * 2 0 = $ 1 4 2 0 $"}, "mask_dabde841-c539-11ed-ad58-eb1b37953a89_9.jpg": {"label": "F { 4 } { 3 } = F { 1 6 } { ( $ 1 7 $ ) }", "predi": "F { 4 } { 3 } = F { 1 6 } { ( $ 1 2 $ ) }"}, "mask_f67793d1-d3b0-11ed-902a-fb4c93e5ba0a_29.jpg": {"label": "F { 7 } { 5 } - 0 . 8 7 5 - F { 1 } { 8 } = $ F { 2 } { 5 } $", "predi": "F { 7 } { 5 } - 0 . 8 7 5 5 F { 1 } { 8 } = $ F { 2 } { 5 } $"}, "mask_dd692261-e296-11ed-9bdc-1bcda24cade3_2.jpg": {"label": "5 + 5 +", "predi": "角 + 5"}, "mask_dc5fe520-cfd1-11ed-8036-3719fb7a0815_22.jpg": {"label": "m : 5 6 k m = $ 1 * 8 0 0 0 0 $", "predi": "m : 5 6 k m = 1 : 8 0 0 0 0"}, "mask_feb59f71-df8a-11ed-a7c3-9d1dc07b7dd5_0.jpg": {"label": "9 5 7 3 2 1 = ( $ 9 5 . 7 3 2 $ 万", "predi": "9 5 7 3 2 1 = ( $ 9 5 . 7 3 2 $ ) 万"}, "mask_d41302c0-dddd-11ed-a552-ef5633f5ca16_4.jpg": {"label": "2 八 m 1 成 = ( $ 8 2 $", "predi": "3 米 米 = ( $ 8 3 $"}, "mask_f3c52aa1-d2d7-11ed-aafc-0fcd97472051_44.jpg": {"label": "F { 1 5 } { 7 2 } = F { 5 } { ( $ 2 1 6 $ ) }", "predi": "F { 1 5 } { 7 2 } = F { 5 } { ( $ 1 6 $ ) }"}, "mask_0eee0b11-d51e-11ed-a9ba-e1227669c37a_0.jpg": {"label": "6 8 - $ 1 0 $ = 5 8", "predi": "6 8 - $ 1 0 = 5 8"}, "mask_fee728a1-d095-11ed-a34e-810c1e9c0e59_4.jpg": {"label": "八 成 八 = ( $ 8 8 $ ) x", "predi": "公 成 顷 = ( $ 8 8 $ ) %"}, "mask_e069fad1-f082-11ed-bcc7-f39e92579638_8.jpg": {"label": "F { 4 } { 5 } F - F { 1 } { 2 0 } =", "predi": "F { 4 } { 5 } - F { 1 } { 2 0 } ="}, "mask_1bb5c031-d605-11ed-94d1-39b632be4445_26.jpg": {"label": "1 5 米 = ( $ 1 $ ) 角 ( $ 5 $ ) 分", "predi": "1 5 分 = ( $ 1 $ ) 角 ( $ 5 $ ) 分"}, "mask_e6c4a331-dd1d-11ed-882d-1bf8f6d61aa0_33.jpg": {"label": "F { 5 } { } - F { 3 } 5 }", "predi": "F { 1 5 } { 8 } = F { 1 5 } { 4 0 }"}, "mask_f363f141-c193-11ed-8732-dbd4e48b5136_8.jpg": {"label": "9 6 / 6 = $ 1 6 $", "predi": "9 6 / 6 = $ 1 6 P $"}, "mask_5cacf9f1-efe6-11ed-baaf-3f66f3380149_22.jpg": {"label": "2 5 4 公 顷 = $ 2 . 5 4 $ k m ^ 2", "predi": "2 5 4 公 顷 = $ 2 . 5 4 $ ) 厘 顷"}, "mask_4cb4b2c1-eda9-11ed-818f-37b3cf889621_39.jpg": {"label": "3 2 - 2 = $ 2 0 $", "predi": "3 2 - 2 = $ 3 0 $"}, "mask_05ce6d81-ce30-11ed-8529-718a4781f781_21.jpg": {"label": "1 8 厘 米 $ > $ 1 8 毫", "predi": "1 8 厘 米 $ > $ 1 8 毫 米"}, "mask_5ca40f21-d812-11ed-8d5f-a117d81ad554_27.jpg": {"label": "4 0 * 5 7 0 = $ 5 7 0 0 $", "predi": "1 0 * 5 7 0 = $ 5 7 0 0 $"}, "mask_faeec271-ddd4-11ed-ac93-7f3d98aaa450_27.jpg": {"label": "F { 5 } { 1 4 } / F { 5 } { 6 } = $ F { 1 } { 7 } * 3 $ = $ F { 3 } { 7 } $", "predi": "F { 5 } { 1 4 } / F { 5 } { 6 } = $ F { 1 } { 7 } $"}, "mask_dc9ed0f1-d355-11ed-8a4d-e91e87a6a96b_22.jpg": {"label": "5 角 0 $ $ 7 角", "predi": "5 角 $ < $ 7 角"}, "mask_5b9a4f51-d91a-11ed-b621-b5a720b0269b_22.jpg": {"label": "1 1 0 5", "predi": "1 0 5 - 1"}, "mask_4f5e4921-bfad-11ed-b1f3-5bb98183b7ba_28.jpg": {"label": "3 1 / 4 - 1 1", "predi": "3 1 / 4 = $ 7 P 3 $"}, "mask_db5f07b1-bffe-11ed-a604-2d243eecc09e_37.jpg": {"label": "1 0 个 百 亿 = ( $ 一 千 $ ) 亿", "predi": "1 0 分 5 亿 = ( $ - 4 $ ) 亿"}, "mask_dce3f871-e439-11ed-adc3-3d6fe67e48df_23.jpg": {"label": "3 . 7 d m ^ 3 = ( $ 3 . 7 $ ) L", "predi": "3 . 7 d m ^ 3 = ( $ 3 . 7 $ ) L L"}, "mask_e188edb1-e892-11ed-b354-81265384e906_27.jpg": {"label": "1 角 3 分 $ > $ 2 角 1 角", "predi": "1 角 3 分 $ > $ 2 角 1 分"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_38.jpg": {"label": "八 成 = ( $ 8 0 $ ) %", "predi": "公 成 = ( $ 8 0 $ ) %"}, "mask_df4128f0-d9de-11ed-a03c-b77097dacfcb_21.jpg": {"label": ") 千 克 十 9 吨 = 1 1 吨", "predi": ") 千 克 + 9 吨 = 1 1 吨"}, "mask_0fa42041-d1df-11ed-aeea-878a79c0e391_38.jpg": {"label": "0 * 8 * 3 = $ 9 6 0 $", "predi": "4 0 * 8 * 3 = $ 9 6 0 $"}, "mask_e329b981-c400-11ed-8138-85003604b0b1_38.jpg": {"label": "2 * 8 1", "predi": "2 8 * 3 = $ 8 4 $"}, "mask_e3020711-d534-11ed-a881-dd60a0497853_42.jpg": {"label": "4 . 7 2 ^ 2 = ( $ 4 $ ) m ^ 2 ( $ 7 2 $ ) d m ^ 2", "predi": "4 . 7 2 m ^ 2 = ( $ 4 $ ) m ^ 2 ( $ 7 2 $ ) d m ^ 2"}, "mask_2e22bd81-debc-11ed-8cf0-a7cf1a8c1d09_39.jpg": {"label": "F { 2 0 0 } { 1 2 5 } = $ F { 8 } { 5 } $", "predi": "F { 2 0 0 } { 2 5 } = $ F { 8 } { 5 } $"}, "mask_fffbd571-e9da-11ed-9602-d72fa9454144_7.jpg": {"label": "F [ 2 } { 3 } - F { 3 } { 5 } =", "predi": "F { 2 } { 3 } - F { 3 } { 5 } ="}, "mask_f250ddb1-cbd2-11ed-af73-c1e352e274ef_27.jpg": {"label": "F { 2 } { 9 } + F { 4 } { 5 } + F { 6 } { 5 } + F { 7 } { 9 } =", "predi": "F { 1 } { 9 } + F { 4 } { 5 } + F { 6 } { 5 } + F { 7 } { 9 } ="}, "mask_04d36130-e0f3-11ed-b680-ddaa959e4fe1_28.jpg": {"label": "7 . 0 3 * 1 0 0 / 1 0 = $ 7 0 3 $", "predi": "7 . 0 3 * 1 0 0 0 / 1 0 = $ 7 0 3 $"}, "mask_de0ad581-e979-11ed-b7db-3b4895a947e0_33.jpg": {"label": "5 0 - 0 0 0 9", "predi": "1 9 0 . 0 9"}, "mask_3a9d27c0-c64e-11ed-8114-c13ab2e2caa7_15.jpg": {"label": "F { 7 } { 2 6 } * 1 3 = $ F { 9 1 } { 1 6 } = F { 7 } { 2 } $", "predi": "F { 7 } { 2 6 } * 1 3 = $ F { 9 1 } { 2 6 } = F { 7 } { 2 } $"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_31.jpg": {"label": "F { 1 7 } { 1 3 } - F { 3 } { 8 } - F { 5 } { 8 } = $ F { 1 1 } { 1 3 } - ( F { 3 } { 8 } + F { 5 } { 8 } ) = F { 1 7 } { 1 3 } - 1 = F { 1 7 } { 1 3 } - F { 1 3 } { 1 3 } = F { 1 7 - 1 3 } { 1 3 } = F { 4 } { 1 3 } $", "predi": "F { 1 7 } { 1 3 } - F { 3 } { 8 } - F { 5 } { 8 } = $ F { 1 1 } { 1 3 } - F { 3 } { 1 8 } + F { 5 } { 8 } ) - F { 1 7 } { 1 3 } - F { 1 } { 1 } = F { 1 7 } { 1 3 } - F { 1 } { 1 3 } = F { 1 7 } { 1 3 } = F { 3 } { 1 3 } = F { 1 3 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 3 } { 1 3 } { 1 3 } { 1 - 1 - 1 - 1 - 1 - 1 - 1 - 1 - 1 - 1 } { 1 } {"}, "mask_0a404f41-c7dd-11ed-9ab5-85355db4ca8a_22.jpg": {"label": "2 : 7 = $ 8 : ( ) $", "predi": "2 : 7 = 8 : ( )"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_35.jpg": {"label": "7 1 1 1 1 0 1", "predi": "4 1 1 1 1 1 1"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_22.jpg": {"label": "F { 1 3 } { 1 5 } - F { 1 } { 3 } - F { 1 } { 5 } = $ F { 1 3 } { 1 5 } - F { 5 } { 1 5 } - F { 3 } { 1 5 } = F { 1 3 - 5 - 3 } { 1 5 } = F { 1 } { 3 } $", "predi": "F { 1 3 } { 1 5 } - F { 1 } { 3 } - F { 1 } { 5 } = $ F { 1 3 } { 1 5 } - F { 5 } { 1 5 } - F { 3 } { 1 5 } = F { 1 3 - 5 - 1 5 } = F { 1 } { 3 } $ F { 1 } { 1 5 } $"}, "mask_6d0fcd21-caef-11ed-aa67-bb7753555405_27.jpg": {"label": "( ) * 9 < 5 2", "predi": "( $ 5 $ ) * 9 < 5 2"}, "mask_d7715f41-cf53-11ed-9f11-29237218da95_1.jpg": {"label": "5 . 4 / F { 2 } { 9 } = $ 1 . 2 $", "predi": "5 . 4 * F { 2 } { 9 } = $ 1 . 2 $"}, "mask_de3a3bd1-c96d-11ed-9eba-796a9df15281_18.jpg": {"label": "1 5 / 8 ≈ 5 2 P", "predi": "1 5 / 8 ≈ $ 5 2 P $"}, "mask_e8144521-e988-11ed-bee9-a3529571f462_39.jpg": {"label": "0 . 6 : ( $ F { 1 5 } { 2 } $ ) = 0 . 8 : 1 0", "predi": "0 . 6 : ( $ F { 1 } { 2 } $ ) = 0 . 8 : 1 0 0"}, "mask_fee728a1-d095-11ed-a34e-810c1e9c0e59_6.jpg": {"label": "六 成 = ( $ 6 0 $ ) %", "predi": "公 成 = ( $ 6 0 $ ) %"}, "mask_4f6d35b1-e69b-11ed-a49f-e5fd73654b31_21.jpg": {"label": "F { 3 } { 2 } : F { 4 } { 7 } = $ 2 1 : 8 $", "predi": "F { 3 } { 2 } : F { 4 } { 7 } = $ 2 1 : 8 8 $"}, "mask_fcd249f0-db2b-11ed-a212-ab3d590e8bfb_21.jpg": {"label": "5 . 8 m ^ 3 = ( $ 5 8 0 0 $ ) c m ^ 3", "predi": "5 . 8 d m ^ 3 = ( $ 5 8 0 0 $ ) c m ^ 3"}, "mask_06b9e290-efe8-11ed-8246-5f78d0e8dbed_38.jpg": {"label": "7 k m ^ 2 = ( $ 7 0 0 $ ) 公 顷", "predi": "7 k ^ 2 = ( $ 7 0 0 $ ) 公 顷"}, "mask_6aa1ac41-dd17-11ed-b636-5358753be9ad_41.jpg": {"label": "3 6 / ( 3 * 2 ) = $ 4 $", "predi": "3 6 / ( 3 * 3 ) = $ 4 $"}, "mask_fe018001-d79a-11ed-a341-1f15794d7f0c_11.jpg": {"label": "F { 8 } { 7 } = $ 4 $", "predi": "F { 8 } { 2 } = $ 4 $"}, "mask_04b0c2c1-c19e-11ed-810c-c929780a1fd2_72.jpg": {"label": "2 5 / 5 + 8 7 = $ 0 $ )", "predi": "2 5 / 5 + 8 7 = $ 9 $ )"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_12.jpg": {"label": "5 = $ 三 元 0 $", "predi": "5 = $ $"}, "mask_1bf50501-df7a-11ed-bac7-fb2f45b6f854_38.jpg": {"label": "F { 9 } { 8 } $ < $ { 3 6 } { 3 2 }", "predi": "F { 9 } { 8 } $ < $ F { 3 6 } { 3 2 }"}, "mask_ed61e2d1-bf44-11ed-a345-ed3f6b44010c_8.jpg": {"label": "$ 7 * ( 3 6 / 4 ) = 6 3 $", "predi": "7 * ( 3 6 / 4 ) = 6 3 $"}, "mask_d66c80a0-d08b-11ed-b88a-f35c3433537e_3.jpg": {"label": "5 = ( $ 6 0 0 ) 2 $ )", "predi": "5 = ( $ 6 0 0 m ^ 2 $"}, "mask_4fdbd911-dbf2-11ed-b388-9514631c1fbd_24.jpg": {"label": "2 5 : 1 . 5 = $ F { 5 0 } { } = F { 5 0 } { 3 } $", "predi": "2 5 : 1 . 5 = $ F { 5 0 } { 1 5 } = F { 5 0 } { 3 } $"}, "mask_4f45e251-c493-11ed-8815-5536b41bcebc_22.jpg": {"label": "4 9 : 6", "predi": "9 1 9 * 6 ="}, "mask_eab6bf81-ca46-11ed-a69e-6d9c916ad9aa_7.jpg": {"label": "= ( $ 1 0 0 0 $ ) d m ^ 2", "predi": "2 = ( $ 1 0 0 0 $ ) d m ^ 2"}, "mask_4b4e5511-ce25-11ed-8f25-fd73d1630bfd_43.jpg": {"label": "4 厘 米 : 1 0 0 0 0 米 = 1 : 2 5 0 0 0 0", "predi": "4 厘 米 : 1 0 0 0 0 米 = $ 1 : 2 5 0 0 0 0 $"}, "mask_d931df31-e434-11ed-aa5c-87bc82f7255f_9.jpg": {"label": "五 = ( 5", "predi": "7 = ("}, "mask_dc0125c1-dde7-11ed-b0a7-dd5a2087fb58_43.jpg": {"label": "1 0 0 分 = (", "predi": "1 0 0 分 = ( $ 1 $"}, "mask_f1b13ad0-cd5d-11ed-a627-335e1c5ee477_9.jpg": {"label": "6 m ^ 2 5 0 c m 2 = ( $ 6 . 5 $ ) d m ^ 2", "predi": "6 m ^ 2 5 0 c m ^ 2 = ( $ 6 . 5 $ ) d m ^ 2"}, "mask_f67793d1-d3b0-11ed-902a-fb4c93e5ba0a_15.jpg": {"label": "F { 9 } { 1 3 } - F { 7 } { 1 6 } - F { 4 } { 1 3 } = $ F { 9 } { 1 6 } $", "predi": "F { 9 } { 1 3 } - F { 7 } { 1 6 } + F { 4 } { 1 3 } = $ F { 9 } { 1 6 } $"}, "mask_5cd20531-cfc8-11ed-86ea-e7bbdf0f3034_0.jpg": {"label": "F { 4 } { 1 8 } = F { 4 $ / $ ( $ 2 $ ) } { 1 8 $ / $ ( $ 2 $ ) = F { ( $ 2 $ ) } { ( $ 9 $ ) }", "predi": "F { 4 } { 1 8 } = F { 4 $ / $ ( $ 2 $ ) } { 1 8 $ / $ ( $ 2 $ ) } = F { ( $ 2 $ ) } { ( $ 9 $ ) }"}, "mask_db1e1371-bfc4-11ed-a01c-a1500e978aba_7.jpg": {"label": "F { 9 } { 5 0 } + F { 3 } { 2 0 } = $ F { 4 2 } { 2 5 } $", "predi": "F { 9 } { 5 0 } + F { 3 } { 2 0 } = $ F { 2 2 } { 2 5 } $"}, "mask_dd692261-e296-11ed-9bdc-1bcda24cade3_8.jpg": {"label": "2 9 3 = ( $ 9 $", "predi": "- 9 3 = ( $ 9 $"}, "mask_02fe9231-cb9f-11ed-b1c7-716f86ffc085_7.jpg": {"label": "6 $ * $ 3 = 2 4 $ - $ 2 6", "predi": "6 $ * $ 3 = 2 4 $ - $ 6"}, "mask_1c0d6441-cbcc-11ed-821d-a9dd0e75b623_17.jpg": {"label": "0 . 1 9 + 0 . 5 6 + 9 . 8 1 = ( $ 0 . 1 0 $ + $ 9 . 8 1 $ ) + 0 . 5 6 = $ 1 0 0 0 . 5 0 $", "predi": "0 . 1 9 + 0 . 5 6 + 9 . 8 1 = ( $ 0 . 1 $ + $ 1 . 8 7 $ ) + 0 . 5 6 = $ 1 0 0 0 . 5 0 $"}, "mask_dec8e451-d4ff-11ed-bf65-dfcd56e91550_19.jpg": {"label": "8 4 0 / 1 0 0 * 1 0 = $ 8 $", "predi": "8 4 0 / 1 0 0 * 1 0 = $ 8 . $"}, "mask_2faba8e1-d202-11ed-884e-4725597cd5ee_8.jpg": {"label": "2 元 十 8 角 = ( $ 2 $ ) 元 ( $ 8 $ ) 角", "predi": "2 元 + 8 角 = ( $ 2 $ ) 元 ( $ 8 $ ) 角"}, "mask_fcf067b0-ed9f-11ed-8210-6f45cd680d03_21.jpg": {"label": "2 0 - ( 8 6 ) = $ 1 8 $", "predi": "2 0 - ( 8 - 6 ) = $ 1 8 $"}, "mask_db256ec1-c55c-11ed-9b95-4d563cc62028_35.jpg": {"label": "3 . 1 4 * 5 { * 4 = $ 3 1 4 $", "predi": "3 . 1 4 * 5 ^ 2 * 4 = $ 3 1 4 $"}, "mask_da00ace1-d77b-11ed-8952-5187987ac217_13.jpg": {"label": "9 - 5 $ 2 3 $", "predi": "9 - 5 $ > $ 3"}, "mask_e16c4231-d6b0-11ed-811c-c911f1b35dfe_34.jpg": {"label": "1 7 / 6 = $ 2 P 5 $", "predi": "1 7 / 6 = $ 2 - 5 $"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_6.jpg": {"label": "六 成 = ( $ 6 0 $ ) %", "predi": "公 成 = ( $ 6 0 $ ) %"}, "mask_e0d6a181-bfbd-11ed-a9ee-17d65b4ec678_12.jpg": {"label": "F { { 1 * }", "predi": "F { 1 }"}, "mask_fd717de1-e4f4-11ed-b274-7d49d3347fd3_32.jpg": {"label": "2 6 / 5 = = $ 5 $ P $ 1 $", "predi": "2 6 / 5 = $ 5 $ P $ 1 $"}, "mask_d3366e80-d934-11ed-9b79-2bcfae589645_26.jpg": {"label": "F { 3 } { 5 } : F { 9 } { 1 0 } = ( $ { 5 } { 9 } $ ) : F { 5 } { 6 }", "predi": "F { 3 } { 5 } : F { 9 } { 1 0 } = ( $ F { 5 } { 9 } $ ) : F { 5 } { 6 }"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_41.jpg": {"label": "八 成 = ( $ 8 0 $ ) %", "predi": "公 成 = ( $ 8 0 $ ) %"}, "mask_e6c4a331-dd1d-11ed-882d-1bf8f6d61aa0_21.jpg": {"label": "F { 3 } { 1 1 } = F { ( $ 9 $ ) } { 3 }", "predi": "F { 3 } { 1 1 } = F { ( $ 9 $ ) } { 3 3 }"}, "mask_d0535601-bfbd-11ed-a9ee-17d65b4ec678_7.jpg": {"label": "F { 4 5 } { 4 6 } - F { 2 2 } { 2 3 } = $ 4 6 $", "predi": "F { 4 5 } { 4 6 } - F { 2 2 } { 2 3 } = $ F { } { 4 6 } $"}, "mask_e907be41-d47e-11ed-920b-3ba87a6c5884_25.jpg": {"label": "F { 5 } { 5 }", "predi": "F { 3 } { 5 }"}, "mask_5a5d1f31-ec04-11ed-b390-194dc1528b1c_3.jpg": {"label": "F { 3 } 1 0 } = $ 0 . 3 $", "predi": "F { 3 } { 1 0 } = $ 0 . 3 $"}, "mask_ff3307a1-c250-11ed-8390-15967ebdcb8c_25.jpg": {"label": "9 + + . 2 + 5 厘 6 分 4 ( $ 9 2 6 0 $ )", "predi": "9 + + . 2 + 5 米 6 分 + 元 ( $ 9 2 6 0 $ )"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_22.jpg": {"label": "1 1 1 1 0 1 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_4d9bedf1-d35c-11ed-b6fe-cd04d425a1d6_24.jpg": {"label": "4 9 0 / 7 * 1 3 = $ 9 1 0 $", "predi": "4 9 0 / 7 * 1 3 = $ 9 9 0 $"}, "mask_2c1f30d1-c70c-11ed-a97b-053ab8e3f111_25.jpg": {"label": "5 千 米 = ( $ 5 0 0 0 $ ) 米", "predi": "5 千 米 = ( $ 5 0 1 0 $ ) 米"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_17.jpg": {"label": "一 成 = ( $ 1 0 $ ) %", "predi": "厘 成 = ( $ 1 0 $ ) %"}, "mask_d8659441-e973-11ed-8d17-f3444709f1b5_0.jpg": {"label": "六 折 4 . 折 ( 五 ( $ 0 0 8 1 $ ) =", "predi": "米 4 4 4 4 4 ( ( $ 0 0 8 1 $ ) ="}, "mask_ec2672f0-d92a-11ed-9ed9-8bc57e9268ad_29.jpg": {"label": "7 8 . 5 * 0 . 1 2 5 * 8 0 = 7 8 . 5 * ( $ 0 . 1 2 5 $ * $ 8 0 $ ) = $ 7 8 5 $", "predi": "7 8 . 5 * 0 . 1 2 5 * 8 0 = 7 8 . 5 * ( $ 0 . 1 5 5 $ * $ 8 0 $ ) = $ 7 8 5 $"}, "mask_06b9e290-efe8-11ed-8246-5f78d0e8dbed_17.jpg": {"label": "1 2 5 0 0 0 m 2 = ( $ 1 $ ) k m ^ 2", "predi": "1 2 5 0 0 0 m ^ 2 = ( $ 1 $ ) k m ^ 2"}, "mask_2fb82b20-dcba-11ed-92a7-5791ef3bf670_43.jpg": {"label": "1 5 - 9 + 5 = $ 1 1 $", "predi": "1 5 - 9 + 5 = $ 1 1 1 $"}, "mask_4fdbd911-dbf2-11ed-b388-9514631c1fbd_10.jpg": {"label": "F { 3 } { 4 } : F { 9 } { 2 8 } = $ F { 1 } { 1 } * F { 7 } { 3 } = F { 7 } { 3 } $", "predi": "F { 3 } { 4 } : F { 9 } { 2 8 } = $ F { 1 3 } { 1 4 } * F { 2 8 } { 3 } = F { 7 } { 3 } $"}, "mask_6f7adad1-d802-11ed-b0a4-19f011fc32f1_17.jpg": {"label": "F { 5 } { 9 } / F { 1 7 } { 1 8 } = $ F { 5 } { } * F { 2 } { 1 7 } = F { 1 0 } { 1 7 } $", "predi": "F { 5 } { 9 } / F { 1 7 } { 1 8 } = $ F { 5 } { 9 } * F { 2 } { 1 7 } = F { 1 0 } { 1 7 } $"}, "mask_dc0cfff1-d831-11ed-a57c-3540540925d8_24.jpg": {"label": "1 8 / 4 = $ 4 $ P $ 2 $", "predi": "1 8 / 4 = $ 4 $ P $ 2"}, "mask_fb197ba1-df37-11ed-a794-0bd03935def7_37.jpg": {"label": "F { 1 } { 4 } * F { 5 } { 8 } = ( $ F { 3 } { 4 } $ ) * F { 5 } { 4 }", "predi": "F { 1 } { 4 } * F { 5 } { 8 } = ( $ F { 3 } { 4 } $ ) * F { 5 } { 2 4 }"}, "mask_f17c2bf1-dd12-11ed-82bc-917d586ab4f9_42.jpg": {"label": "2 0 c m ^ 3 = F { ( $ 2 0 $ ) } { ( $ 1 0 0 0 $ ) } d m ^ 3", "predi": "2 0 c m ^ 3 ( $ 2 0 $ ) } { ( $ 1 0 0 0 $ ) } d m ^ 3"}, "mask_1f4d12d1-e41e-11ed-88e2-79dec01dc83c_32.jpg": {"label": "3 7 / 5 = $ 7 P 2 $", "predi": "3 7 / 5 = 7 P 2"}, "mask_6f1ae801-ca42-11ed-8312-b108a48d87db_2.jpg": {"label": "2 m : 5 m = ( $ 4 0 0 $ ) : 1", "predi": "2 m : 5 m m = ( $ 4 0 0 $ ) : 1"}, "mask_fc18d331-ed2d-11ed-b8ce-ff2a340f5f5a_13.jpg": {"label": "六 五 折 = ( $ 6 5 $ ) %", "predi": "公 成 顷 = ( $ 6 5 $ ) %"}, "mask_4d84fa01-ee78-11ed-99a8-251dc8b4978a_39.jpg": {"label": "1 0 0 0 克 + 8 0 0 0 克 = ( $ 9 $ ) 千 克", "predi": "1 0 0 0 克 + 8 0 0 0 克 = ( $ 9 $ ) 千 克 克"}, "mask_fa7c6471-e0fc-11ed-8169-412e8bfbbfbf_18.jpg": {"label": "4 5 / 2 * 8 = $ 4 0 $", "predi": "4 5 / 9 * 8 = $ 4 0 $"}, "mask_f7892e51-c951-11ed-9e3e-61ffe2d03345_16.jpg": {"label": "0 . 0 6 = $ 0 . 1 8 $", "predi": "* 0 . 0 6 = $ 0 . 1 8 $"}, "mask_2c4ea711-e41d-11ed-8fa4-8bdfce412ea8_32.jpg": {"label": "0 . 9 = ( $ F { 7 } { 1 0 0 } $ )", "predi": "0 . 0 9 = ( $ F { 7 } { 1 0 0 } $ )"}, "mask_5e66ee31-e4de-11ed-a463-a13c60a05132_28.jpg": {"label": "F { 1 5 } { 8 } - F { 4 } { 5 } - F { 3 } { 8 } - F { 1 } { 5 } = $ F { 1 5 } { 8 } - F { 3 } { 8 } - ( F { 4 } { 5 } + F { 1 } { 5 } ) $", "predi": "F { 1 5 } { 8 } - F { 4 } { 5 } - F { 3 } { 8 } - F { 1 } { 5 } = $ F { 1 5 } { 8 } - F { 3 } { 8 } - ( F { 4 } { 5 } + F { 1 } { 5 } )"}, "mask_5d948d80-d21b-11ed-a137-e75cd9a33ba9_9.jpg": {"label": "6 0 k m 3 0 m = ( $ 6 0 . 3 $ ) d m", "predi": "6 0 k m 3 0 m = ( $ 6 0 . 3 $ ) k m"}, "mask_3eefe9b0-d43c-11ed-91fe-e937538423d3_35.jpg": {"label": "4 * 4 1 0", "predi": "9 4 * 4 = $ 3 7 . 6 $"}, "mask_d328ae71-d9da-11ed-b8f5-21d006063f91_1.jpg": {"label": "三 8 0 0 8", "predi": "= 8 0 0"}, "mask_de11f8f1-c3ff-11ed-b594-775abc41496b_5.jpg": {"label": "三 六 = $ 5 P $", "predi": "/ 3 = $ 5 0 P 1 $"}, "mask_d036ad81-e370-11ed-a281-c9b5564f5a61_39.jpg": {"label": "4 . 2 * 4 0 = $ 1 6 8 $", "predi": "4 . 2 * 4 0 = $ 1 6 8 8 $"}, "mask_fe665720-eb34-11ed-876c-956317a0e3d6_31.jpg": {"label": "5 元 7 角 = ( 5 . 7 ) 元", "predi": "5 元 7 角 = ( $ 5 . 7 $ ) 元"}, "mask_1fc71ca1-df6f-11ed-8a8c-1ba383900ad8_9.jpg": {"label": "1 元 1 0 0", "predi": "1 元 1 0 0 分"}, "mask_d90dea81-ddf4-11ed-a357-31030dd34dfc_4.jpg": {"label": "3 > 3 * 0 4", "predi": "3 * 0 *"}, "mask_da6fa601-ebed-11ed-a081-45533329d7a6_0.jpg": {"label": "1 = ( $ 7 5 $ ) m", "predi": "1 = ( $ 7 5 $ ) c"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_1.jpg": {"label": "= $ 7 $", "predi": "= $ 3 0 $"}, "mask_df202bc1-cc9e-11ed-9c9d-9b3ee4d1c8e0_45.jpg": {"label": "七 七 五 角 角 % m 十", "predi": "角 顷 角 角 角 角"}, "mask_e1ac27a0-d8f0-11ed-bfe2-1b1542ddad09_8.jpg": {"label": "F { 3 5 } { } = $ F { 7 } { 5 } $", "predi": "F { 3 5 } { 5 } = $ F { 7 } { 5 } $"}, "mask_d39e8d91-bfb6-11ed-94e5-d390e73abda8_36.jpg": {"label": "F { 7 } { 8 } + F { 3 } { 1 8 } = $ F { 1 7 } { 1 6 } $", "predi": "F { 7 } { 8 } + F { 3 } { 1 6 } = $ F { 1 7 } { 1 6 } $"}, "mask_ff60fe11-f006-11ed-9011-cdf127881d05_19.jpg": {"label": "2 4 0 * 3 / 9 = $ 8 0 $", "predi": "2 4 0 * 3 / 9 = $ 8 . 0 $"}, "mask_dee65991-efe9-11ed-82c8-558d9754312f_7.jpg": {"label": "F { 5 } { 1 2 } * 5 = $ F { 1 } { 1 2 } $", "predi": "F { 5 } { 1 2 } / 5 = $ F { 1 } { 1 2 } $"}, "mask_df647181-df46-11ed-a078-0b8b0a1f8d54_8.jpg": {"label": "3 . 4 c m ^ 3 = ( $ 0 . 0 0 3 4 $ ) L", "predi": "3 . 4 c m ^ 3 = ( $ 0 . 0 0 3 $ ) L"}, "mask_d328ae71-d9da-11ed-b8f5-21d006063f91_15.jpg": {"label": "( 八 1 0", "predi": "( ( 1 0 $ ) $"}, "mask_e8f4ab31-c195-11ed-9ac7-172e357d7a5a_10.jpg": {"label": "5 5 0 0 > 1 $ 4 $ 9 5", "predi": "1 5 0 0 > 1 $ 4 $ 9 5"}, "mask_f6396c30-d68b-11ed-8c3e-eb6bdc295c8a_21.jpg": {"label": "/ 8 / 2 = $ 4 $", "predi": "/ 8 / 2 = $ 1 4 $"}, "mask_3b471c30-d78d-11ed-a82a-f5347e681596_27.jpg": {"label": "F { 3 } { 1 0 } = ( $ 3 $ ) / ( $ 1 0 $ ) = ( $ 0 . 3 $ )", "predi": "F { 3 } { 1 0 } = ( $ 3 $ ) / ( $ 1 0 $ ) = ( $ 0 . 3 $"}, "mask_dc8c1b41-effc-11ed-aaee-151279633bcd_33.jpg": {"label": "4 9 角 = ( $ 4 $ ) = ( $ 9 $ ) 角", "predi": "4 9 角 = ( $ 4 $ ) 元 ( $ 9 $ ) 角"}, "mask_ff8cf1c1-c7d2-11ed-b100-bb959104a54a_37.jpg": {"label": "2 0 元 = ( $ 5 $ ) 张 1 元 + 1 张 1 0 元 + 1 张 5 元", "predi": "2 0 元 = ( $ 5 $ ) 张 1 元 + 1 张 1 0 元 + 1 张 5 米"}, "mask_5c739961-dd2d-11ed-9858-5fd236f404c6_40.jpg": {"label": "2 0 0 0 * F { 1 } { 1 0 0 0 } = $ 2 0 $", "predi": "2 0 0 0 0 * F { 1 } { 1 0 0 0 } = $ 2 0 $"}, "mask_5c0fc841-e7d2-11ed-92b3-c99ebdbf361f_28.jpg": {"label": "F { 2 5 } { 4 } = F { 1 } { 3 0 0 0 0 }", "predi": "F { 2 5 } { x } = F { 1 } { 3 0 0 0 0 }"}, "mask_ea046181-df65-11ed-8deb-294b6f177377_35.jpg": {"label": "1 6 8 - 8 8 = $ 8 0 $", "predi": "1 6 8 - 8 8 = ( $ 8 0 $"}, "mask_ff3307a1-c250-11ed-8390-15967ebdcb8c_21.jpg": {"label": "1 分 5 厘 0 9 分 - ( $ 1 9 1 $ )", "predi": "1 分 5 米 0 9 分 - ( $ 1 9 1 $ )"}, "mask_5b650e31-c484-11ed-a2dd-4f8a69cf87a7_5.jpg": {"label": "5 4 / 2 = 2 7", "predi": "5 4 / 2 = $ 2 7 $"}, "mask_4e1a9a71-eda0-11ed-9242-11a75308479a_0.jpg": {"label": "1 0 0 5 6 0 9 3 2 0 0 ≈ ( 1 0 0 . 5 6 ) 亿", "predi": "1 0 0 5 6 0 9 3 2 0 0 ≈ ( $ 1 0 0 . 5 6 $ ) 亿"}, "mask_d59db621-ee5f-11ed-a88d-1b65076756ce_9.jpg": {"label": "4 8 + 5 2 ° + 8 0 ° = 1 8 0 ° 3 6 0 ° - 1 8 0 ° - 9 0 ° = $ 9 0 ° $", "predi": "4 8 + 5 2 ° + 8 0 ° = $ 1 8 0 ° 3 6 0 ° - 1 8 0 ° - 9 0 ° = $ 9 0 ° $"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_34.jpg": {"label": "七 成 = ( $ 7 0 $ ) %", "predi": "成 成 = ( $ 7 0 $ ) %"}, "mask_f437de91-c7df-11ed-89db-09b66e187465_29.jpg": {"label": "2 8 / 3 = $ 9 P $", "predi": "2 8 / 3 = $ 9 P 1 $"}, "mask_e329b981-c400-11ed-8138-85003604b0b1_37.jpg": {"label": "七 4 5 0", "predi": "4 5 6"}, "mask_e9fc3f00-d38f-11ed-9792-f533b72429c3_5.jpg": {"label": "4 3 * 3 9 7 = $ 1 7 0 7 0 $", "predi": "4 3 * 3 9 7 ≈ $ 1 7 0 7 0 $"}, "mask_fdb0bc41-c333-11ed-8517-67fa62417c1c_12.jpg": {"label": "6 6 6 / 3 = $ 2 2 $", "predi": "6 6 6 / 3 = $ 2 2 2 $"}, "mask_5af43a51-d9d5-11ed-9064-19b9b8c76ddb_19.jpg": {"label": "4 0 4 千 克 = F { ( $ 4 0 4 $ ) } { ( $ 1 0 0 0 $ ) } 吨 = ( $ 0 . 4 0 4 $ ) 吨", "predi": "4 0 4 千 克 = F { ( $ 4 0 4 $ ) } { ( $ 1 0 0 0 $ ) } 吨 = ( $ 0 . 4 4 4 $ ) 吨"}, "mask_f221be21-d859-11ed-81be-8fe636ed0259_6.jpg": {"label": "7 7 - 9 =", "predi": "7 7 - 9 = $ 6 9 $"}, "mask_e2aa5a20-e0b9-11ed-8e97-a7657e854705_4.jpg": {"label": "F { 5 }", "predi": "F { 2 }"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_29.jpg": {"label": "1 年 零 3 月 = ( $ 1 5 $ ) 月", "predi": "1 角 克 3 顷 = ( $ 1 5 $ ) 顷"}, "mask_4f412391-ee65-11ed-a0ac-d5695dcde8d4_37.jpg": {"label": "七 5 + ( 6 + 2 ) 3 5", "predi": "6 5 + ( 5 + 2 ) - 7 5"}, "mask_3b3c4161-c3f2-11ed-afa5-e3fd537b6828_41.jpg": {"label": "5 6 0 L $ = $ 0 . 5 6 m ^ 3", "predi": "5 6 0 L $ = $ 0 . 5 6 m ^ 3 m ^ 3"}, "mask_d66c80a0-d08b-11ed-b88a-f35c3433537e_18.jpg": {"label": "4 5 0 0 m ^ 2 = ( $ 0 . 7 1 0 1 $", "predi": "4 5 0 c m ^ 2 = ( $ 0 . 7 $ ) d"}, "mask_ff60fe11-f006-11ed-9011-cdf127881d05_44.jpg": {"label": "2 5 + 1 0 * 2 = $ 4 5 $", "predi": "2 5 + 1 0 * 2 = $ 4 5 0 $"}, "mask_f8a929f1-cc96-11ed-9bf3-1f544e096a87_6.jpg": {"label": "F { 2 } { 3 } + F { 1 } ( 2 ) = $ F { 7 } { 6 } $", "predi": "F { 2 } { 3 } + F { 1 } { 2 } = $ F { 7 } { 6 } $"}, "mask_f76dfc01-c71e-11ed-8440-dd86e7360887_16.jpg": {"label": "9 5 7 / 3 = $ 3 1 4 0 $", "predi": "9 5 7 / 3 = $ 3 1 9 0 $"}, "mask_e7d75d71-df67-11ed-a963-7dd382b5cd44_6.jpg": {"label": "3 6 = $ F { 1 } { 4 0 } $", "predi": "3 0 = $ F { 1 } { 4 0 } $"}, "mask_fbca5771-e1dc-11ed-948e-4be3080be794_41.jpg": {"label": "4 4 / 6 = $ 7 $ P $ 2 $", "predi": "4 4 / 6 = $ 7 P 2 $"}, "mask_1f64cfc1-cc94-11ed-bb44-3b11b70e4561_3.jpg": {"label": "1 4 8 1", "predi": "/ 9 $ < $ 2 8 / 4"}, "mask_2d7e4d51-e0c3-11ed-9715-7968c989cc56_17.jpg": {"label": "F { 1 } { 2 } + F { 1 } { 3 } = $ F { 5 } [ 6 } $", "predi": "F { 1 } { 2 } + F { 1 } { 3 } = $ F { 5 } { 6 } $"}, "mask_0a7c0bc1-c3d7-11ed-82f9-a12f3266b254_17.jpg": {"label": "9 $ + $ 2 = $ 1 1 $", "predi": "9 $ + $ 2 = 1 1"}, "mask_ebd9aba1-d09c-11ed-a299-619d1869f797_27.jpg": {"label": "F { 2 5 } { 1 0 0 } = F { 1 } { ( $ 2 5 0 0 $ ) }", "predi": "F { 2 5 } { 1 0 0 } = F { 1 } { ( $ 5 5 0 0 $ ) }"}, "mask_6a2365c1-d46a-11ed-aa95-878fc944c866_19.jpg": {"label": "3 0 0 / F { 1 } { 1 0 0 0 } = $ 3 0 0 0 0 0 $", "predi": "3 0 0 / F { 1 } { 1 0 0 0 } = $ 3 0 0 0 0 0 0 $"}, "mask_ed374521-cc91-11ed-864f-093b784e691a_8.jpg": {"label": "F { 1 } { 2 } * F { 2 } { 5 } = $ F { 9 } { 1 0 } $", "predi": "F { 1 } { 2 } + F { 2 } { 5 } = $ F { 9 } { 1 0 } $"}, "mask_6e9a0670-eda8-11ed-a20f-cf61e17ee98d_18.jpg": {"label": "F { 6 } { 7 } = F { ( $ 4 2 $ ) } { 4 9 }", "predi": "F { 6 } { 7 } = F { ( $ 4 2 $ ) ) } { 4 9 }"}, "mask_dc9ed0f1-d355-11ed-8a4d-e91e87a6a96b_1.jpg": {"label": "5 = ( $ $", "predi": "5 = ("}, "mask_1cd48711-c8a6-11ed-a216-cfa1e1619377_17.jpg": {"label": "* F { 7 } { 9 } = $ F { 1 0 5 } 9 } = F { 1 5 * 7 } { 9 } = F { F { 3 5 } { 3 } $", "predi": "* F { 7 } { 9 } = $ F { 1 0 5 } { 9 } = F { 1 5 * 7 } { 9 } = F { 3 5 } { 3 } $"}, "mask_fabea251-eb28-11ed-8d86-b3bb64710e97_12.jpg": {"label": "3 3 0 + 4 8 0 = $ 8 1 0 $", "predi": "3 3 0 + 4 8 0 = $ 8 1 0 0 $"}, "mask_1b7cee61-e9b8-11ed-b02d-dbc19e7fd366_2.jpg": {"label": "5 分 米 1 厘 米 = ( $ 5 1 $ ) 分", "predi": "5 分 米 1 厘 米 = ( $ 5 . 1 $ ) 分"}, "mask_dc320d31-eb42-11ed-942d-b958113e4192_18.jpg": {"label": "1 周 = ( $ 7 $ ) 元", "predi": "1 厘 = ( $ 7 $ ) 元"}, "mask_d85e83f1-ecd7-11ed-b9ed-cdb333d97bc7_12.jpg": {"label": "9 0 * F { 2 } { 3 } = $ 6 0 $", "predi": "9 0 * F { 2 } { 3 } = $ 6 0 0 $"}, "mask_eb4733e1-e359-11ed-8ee1-a5ea66eaebff_23.jpg": {"label": "6 0 + 1 4 0 + 1 2 2 + 7 8 = $ 4 6 $", "predi": "6 0 + 1 4 0 + 1 2 2 + 7 8 = $ 4 6 0 $"}, "mask_1c7324d1-ed53-11ed-918b-398e7122eb1f_16.jpg": {"label": "2 % = ( $ F { 1 } { 5 0 } $ )", "predi": "2 % = ( $ F { 1 } { 5 0 } $"}, "mask_fd717de1-e4f4-11ed-b274-7d49d3347fd3_8.jpg": {"label": "6 2 / 8 = $ 7 P 6 $", "predi": "6 2 / 8 = $ 7 $ P $ 6 $"}, "mask_3a1e86c1-cef2-11ed-8b85-913393ad2cd9_45.jpg": {"label": "F { 2 5 5", "predi": "2 5"}, "mask_5b4c45b1-ebfa-11ed-9a2c-1fe9fa50ec60_9.jpg": {"label": "8 年 = ( $ 9 6 $ ) 月", "predi": "8 秒 = ( $ 9 6 $ ) 万"}, "mask_5cacf9f1-efe6-11ed-baaf-3f66f3380149_29.jpg": {"label": "1 6 8 7 2 0 0 m ^ 2 = ( $ 1 . 6 8 7 2 $ ) k m ^ 2", "predi": "1 8 7 7 2 0 0 m ^ 2 = ( $ 1 . 6 8 7 2 $ ) k m ^ 2"}, "mask_faeec271-ddd4-11ed-ac93-7f3d98aaa450_14.jpg": {"label": "F { 1 1 } { 3 2 } * 8 = $ F { 1 1 } { 3 2 } / F { 1 } { 8 } = F { 1 1 } { 4 } $", "predi": "F { 1 1 } { 3 2 } * 8 = $ F { 1 1 } { 3 2 2 } / F { 1 } { 8 } = F { 1 1 } { 4 } $"}, "mask_e04c9450-c95d-11ed-a976-5bd0362850c0_43.jpg": {"label": "5 1 $ > $ 4 5", "predi": "5 4 $ > $ 4 5"}, "mask_faeec271-ddd4-11ed-ac93-7f3d98aaa450_5.jpg": {"label": "F { 1 1 } { 9 0 } / 2 2 = $ F { 1 1 } { 9 0 } * F { 1 * 4 5 } { 2 0 * 4 5 } = F { 4 6 } { 9 0 } = { 2 3 } { 4 5 } $", "predi": "F { 1 1 } { 9 0 } / 2 2 = $ F { 1 4 } { 9 0 } * F { 1 * * 5 } { 2 0 * 4 5 } = F { 4 6 } { 9 0 } = F { 1 3 } { 4 5 } $ F { 3 } { 4 } $"}, "mask_dc1a9901-d938-11ed-bf18-75839265abea_27.jpg": {"label": "3 千 克 = ( $ 3 0 0 0 $ ) 克", "predi": "3 千 克 = ( $ 3 0 0 $ ) 克"}, "mask_88f7bd81-bfb0-11ed-9c69-6730a93f2b9d_30.jpg": {"label": "0 4 1 6 1 1 5", "predi": "0 + ( $ 6 $ ) = 1 5"}, "mask_d8972341-c1ad-11ed-b768-613d9569d463_20.jpg": {"label": "F { } { 1 5 } / 3 8 8 = $ 5 7 0 $", "predi": "F { } { 1 5 } / 3 8 = $ 5 7 0 $"}, "mask_2eedff91-cd5f-11ed-af97-a5c0c75aab50_6.jpg": {"label": "0 . 4 0 2 * 1 0 0 / 1 0 = $ 4 . . 0 2 $", "predi": "0 . 4 0 2 * 1 0 0 / 1 0 = $ 4 . 0 2 $"}, "mask_fefbaa31-dc4a-11ed-b661-4709e35cec04_0.jpg": {"label": "5 . 8 1 m L = ( $ 5 . 8 $ ) c m ^ 3", "predi": "5 . 8 m L = ( $ 5 . 8 $ ) c m ^ 3"}, "mask_d83ca591-c20d-11ed-82ce-e3ec7feaad83_46.jpg": {"label": "1 . 3 1 . 8", "predi": "1 . 3 1 . - 3 1"}, "mask_df4128f0-d9de-11ed-a03c-b77097dacfcb_17.jpg": {"label": "+ 7 0 0 0", "predi": "0 + 7 0 0 0"}, "mask_1c5fa2e1-c53d-11ed-86da-8f0aa91cc175_8.jpg": {"label": "F { 7 7 } { 2 1 } = F { 7 7 $ / $ ( $ 7 $ ) } { 2 1 $ / $ ( 7 ) } = F { ( $ 1 1 $ ) } { ( $ 3 $ ) }", "predi": "F { 7 7 } { 2 1 } = F { 7 7 $ / $ ( $ 7 $ ) } { 2 1 $ / $ ( $ 7 $ ) } = F { ( $ 1 1 $ ) } { ( $ 3 $ ) }"}, "mask_facb43e1-ce89-11ed-8861-4104c479a3ee_37.jpg": {"label": "8 = ( $ 5 0 2 $", "predi": "8 = ( $ 5 0 m $"}, "mask_dce216f0-eded-11ed-8c89-b1c119c99ae4_26.jpg": {"label": "4 7 1 2", "predi": "1 7 1 1"}, "mask_db1e1371-bfc4-11ed-a01c-a1500e978aba_44.jpg": {"label": "5 4 - 5 0", "predi": "4 4 - 5 0"}, "mask_ff3307a1-c250-11ed-8390-15967ebdcb8c_26.jpg": {"label": "5 分 4 . 8 分 5 厘 4 分 - 2 ( $ 5 8 4 $ )", "predi": "5 分 千 . 8 分 5 米 4 分 - 元 ( $ 5 8 0 4 $ )"}, "mask_f73a4a60-ebd4-11ed-8a74-65673759874b_26.jpg": {"label": "7 . 0 5 - 3 . 8 = $ 3 . 2 5 $", "predi": "7 . 0 5 - 3 . 8 = $ 3 2 5 $"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_4.jpg": {"label": "七 = $ 5 5 $", "predi": "6 . 5 / 1 0 = $ 1 . 5 5 $"}, "mask_1d761d01-e42b-11ed-adf6-598ab8d5baa1_26.jpg": {"label": "0 + 4 0 + 2 0 = $ 7 0 $", "predi": "1 0 + 4 0 + 2 0 = $ 7 0 $"}, "mask_1c6245e1-db81-11ed-9c51-899126f8e9d4_38.jpg": {"label": "1 0 0 0 元 = ( $ 1 $ ) 千 克", "predi": "1 0 0 0 克 = ( $ 1 $ ) 千 克"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_27.jpg": {"label": "4 1 1 1 1 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_6e9a0670-eda8-11ed-a20f-cf61e17ee98d_35.jpg": {"label": "F { 3 5 } { 5 0 } = $ F { 7 } { ( ) ) } $", "predi": "F { 3 5 } { 5 0 } = F { 7 } { ( ) }"}, "mask_4cc0a160-cbc2-11ed-b0e2-6332b9c024a5_28.jpg": {"label": "F { 1 2 3 } $ 0 $", "predi": "F { 3 } { 8 0 } * $ F { 9 } { 2 } $"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_19.jpg": {"label": "1 1 1 1 1 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_0f8e41d1-d3a1-11ed-b91e-d7d3eedf17f4_1.jpg": {"label": "5 . 2 5 * 0 . 8 = $ 1 $", "predi": "1 . 2 5 * 0 . 8 = $ 1 $"}, "mask_5cca42f1-e643-11ed-830d-cbe284fabe89_10.jpg": {"label": "x * y = $ x y $", "predi": "x * a = $ x 亿 $"}, "mask_df6fdf91-d387-11ed-8768-51795f6dee33_30.jpg": {"label": "2 $ 4 0 4 5 0 4 1 2 $", "predi": "( $ 4 0 4 5 0 4 $ ) 亿"}, "mask_fddb44e1-c8a8-11ed-a810-c33f01b315fa_25.jpg": {"label": "F { 5 } { 8 } * 1 4 = $ 3 5 $", "predi": "F { 5 } { 2 } * 1 4 = $ 3 5 $"}, "mask_000d9331-e34d-11ed-a079-c955e3ac3fcb_45.jpg": {"label": "2 十 5 0 {", "predi": "5 5 5 0 0"}, "mask_fa34c921-eb39-11ed-b18a-69d6e2f7024b_45.jpg": {"label": "6 ≈ 4 > 5 2", "predi": "6 - 4 5 5"}, "mask_0d63cf91-e59b-11ed-afb0-1b7f0c684913_15.jpg": {"label": "F { 1 2 5 } { $ 1 0 0 0 0 0 $ } = F { 1 } { 8 0 0 }", "predi": "F { 1 2 5 } { ( $ 1 0 0 0 0 0 $ ) } = F { 1 } { 8 0 0 }"}, "mask_e29c9341-e1d1-11ed-aa71-0bbe3ffbd1f9_36.jpg": {"label": "F { 3 } { 2 7 } =", "predi": "F { 2 } { 2 } ="}, "mask_f3f04d51-dd1f-11ed-9099-59422c627afd_57.jpg": {"label": "1 0 0 0 克 + 8 0 0 0 元 = ( $ 1 8 $ ) 千", "predi": "1 0 0 0 克 + 8 0 0 0 克 = ( $ 1 8 $ ) 千"}, "mask_1e074301-f06c-11ed-b061-dfd21a6071b0_26.jpg": {"label": "F { 1 1 } { 2 7 } $ < $ F { 4 } { }", "predi": "F { 1 1 } { 2 7 } $ < $ F { 4 } {"}, "mask_2a1f0201-eff3-11ed-aa3f-57649601d057_27.jpg": {"label": "F { 1 0 } { 1 3 } + F { 1 4 } { 3 9 } = $ F { 3 0 } { 3 9 } + F { 1 4 } { 3 9 } = F { 4 4 } { 3 9 } $", "predi": "F { 1 0 } { 1 3 } + F { 1 4 } { 3 9 } = $ F { 3 0 } { 3 9 } + F { 4 } { 3 9 } = F { 4 4 } { 3 9 } $"}, "mask_fb4a2fb1-c016-11ed-8bee-e364d72c9fa8_10.jpg": {"label": "5 0 0 * 0 . 1 4 = $ 9 8 $", "predi": "7 0 0 * 0 . 1 4 = $ 9 8 $"}, "mask_ec2672f0-d92a-11ed-9ed9-8bc57e9268ad_27.jpg": {"label": "2 1 * 0 . 4 * 2 5 = 2 . 1 * ( $ 0 . 4 $ * $ 2 5 $ ) = $ 2 1 $", "predi": "2 . 1 * 0 . 4 * 2 5 = 2 . 1 * ( $ 0 . 4 $ * $ 2 5 $ ) = $ 2 1 $"}, "mask_6a9f9da1-ef24-11ed-a9a8-a5c5140488dd_7.jpg": {"label": "F 3 1 + 4 = $ 9 7 $", "predi": "F 3 3 + 4 = $ 9 7 $"}, "mask_ee0e6a21-ce24-11ed-899c-9d30f923938c_12.jpg": {"label": "8 0 0 一 2 2 0 = $ 5 8 0 $", "predi": "8 0 0 - 2 2 0 = $ 5 8 0 $"}, "mask_5d186051-e43d-11ed-b93b-57534a6bb968_21.jpg": {"label": "F { 4 } { 1 0 0 } = $ 0 . 4 5 $", "predi": "F { 4 5 } { 1 0 0 } = $ 0 . 4 5 $"}, "mask_6a21c811-d3ba-11ed-8ec0-adc48813d352_12.jpg": {"label": "7 6 0 / 5 / 2 0 =", "predi": "7 6 0 / 5 / 2 ="}, "mask_1edb5781-ce20-11ed-98f0-d16f20d74b1b_45.jpg": {"label": "7 * 3 = $ 2 1 $", "predi": "7 * 3 = $ 2 $"}, "mask_1a4b6e61-dc32-11ed-8723-4d11c01b87fc_35.jpg": {"label": "8 3 - 3 9 + 1 1 = ( (", "predi": "8 3 - 3 9 + 1 1 = ( <"}, "mask_d8659441-e973-11ed-8d17-f3444709f1b5_7.jpg": {"label": "4 1 7 9", "predi": "4 4 4 9"}, "mask_1a6d3701-ddce-11ed-a57a-8bb878721b0c_12.jpg": {"label": "2 元 = ( $ 2 0 0 $ } 角", "predi": "2 元 = ( $ 2 0 0 $ ) 角"}, "mask_3a8732f0-c647-11ed-8211-e3a1f37cb8da_14.jpg": {"label": "( $ 七 ) $ ) 五 十 六", "predi": "( $ 毫 $ ) 千 米 米"}, "mask_1ff099b1-d784-11ed-b62c-c3a421864d04_42.jpg": {"label": "1 7 - 9 + 3 = $ 1 1 $", "predi": "1 7 - 9 + 3 = $ 1 1 1 1 1 1 1 1 1 1 1"}, "mask_dc320d31-eb42-11ed-942d-b958113e4192_26.jpg": {"label": "1 天 = ( $ 2 4 $ ) 时", "predi": "1 元 = ( $ 2 4 $ ) 时"}, "mask_fc781e71-bfc0-11ed-a147-f3cbab19d534_43.jpg": {"label": "4 . 5 9 d m ^ 3 = ( $ 4 $ ) L ( $ 5 9 0 $ ) m L", "predi": "4 . 5 9 d m ^ 3 = ( $ 4 $ ) L $ 5 9 9 $ ) m L"}, "mask_1c5fa2e1-c53d-11ed-86da-8f0aa91cc175_5.jpg": {"label": "F { 2 7 } { 5 4 } = F { 2 7 $ / $ ( $ 2 7 $ ) } { 5 4 $ / $ ( $ 2 7 $ ) } = F { ( $ 1 $ ) } { ( $ 2 $ }", "predi": "F { 2 7 } { 5 4 } = F { 2 7 $ / $ ( $ 2 7 $ ) } { 5 4 $ / $ ( $ 2 7 $ ) } = F { ( $ 1 $ ) } { ( $ 2 $ ) }"}, "mask_ee0e6a21-ce24-11ed-899c-9d30f923938c_26.jpg": {"label": "1 2 . 3 m ^ 3 = ( 1 2 3 0 0 ) d m ^ 3", "predi": "1 2 . 3 m ^ 3 = ( $ 1 2 3 0 0 $ ) d m ^ 3"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_24.jpg": {"label": "1 1 1 1 1 0 1 1 = ( ) 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1"}, "mask_d41302c0-dddd-11ed-a552-ef5633f5ca16_18.jpg": {"label": "2 . 1 6 4 毫", "predi": "2 . 4 6 4"}, "mask_d83ca591-c20d-11ed-82ce-e3ec7feaad83_45.jpg": {"label": "2 . 1 1 1 1 1 1", "predi": "2 . 1 1 1 - 1 1 1"}, "mask_4a865511-bfca-11ed-bd6a-87a16b2a6809_7.jpg": {"label": "2 0 m m . 8 0 k m = $ 1 : 8 0 0 0 0 0 0 0 $", "predi": "2 0 m m . 8 0 k m = $ 1 : 8 0 0 0 0 0 0 0 0 $"}, "mask_4cc0a160-cbc2-11ed-b0e2-6332b9c024a5_43.jpg": {"label": "F { 5 } { 1 2 } * { 2 } { 5 } = $ F { 1 } { 6 } $", "predi": "F { 5 } { 1 2 } * F { 2 } { 5 } = $ F { 1 } { 6 } $"}, "mask_1da085f1-c01c-11ed-b744-6d6bcb4a1870_13.jpg": {"label": "4 8 / 6 * 8 = $ 6 4 $", "predi": "4 8 / 6 * 8 = $ 6 4 1 $"}, "mask_fd861df1-df7b-11ed-95d8-8b749a27b0d9_18.jpg": {"label": "( $ 7 3 0 $ ) - 1 9 0 - 5 4 0", "predi": "( $ 7 3 0 $ ) - 1 9 0 = 5 4 0"}, "mask_e45ce9c1-c339-11ed-afb1-ff14ca9830ab_0.jpg": {"label": "0 . 5 * 0 . 4 + { 1 } { 4 } =", "predi": "0 . 5 * 0 . 4 + <PERSON> { 1 } { 4 } ="}, "mask_da6fa601-ebed-11ed-a081-45533329d7a6_4.jpg": {"label": "1 1 = ( $ 3 6 $ ) m", "predi": "1 1 = ( $ 3 6 $ ) c m"}, "mask_0d913131-e427-11ed-a1de-0f7bf69dbaab_5.jpg": {"label": "F { 1 5 } { 2 0 } $ < $ F { 2 0 } { 3 0 }", "predi": "F { 1 5 } { 2 0 } $ < $ F { 2 0 0 } { 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0"}, "mask_ff4340c1-c7e1-11ed-9555-35b476d8b484_22.jpg": {"label": "1 7 = $ 3 4 0 $", "predi": "1 7 = $ 3 4 6 $"}, "mask_fef40571-d08b-11ed-a238-33aad93bf372_9.jpg": {"label": "8 4 - 6 9 - 1 = 8 4 - ( $ 6 9 $ + $ 1 $ ) = $ 1 4 $", "predi": "8 4 - 6 9 - 1 = 8 4 - ( $ 6 9 $ + $ 1 $ ) = $ 4 $"}, "mask_fdf9fb50-ee56-11ed-9d54-7560ba3d313d_27.jpg": {"label": "- 5 = $ 4 9 $", "predi": "P - 5 = $ 4 9 $"}, "mask_4cc0a160-cbc2-11ed-b0e2-6332b9c024a5_32.jpg": {"label": "F { 5 } { 1 3 } * F { 3 } { 5 } * F { 1 3 } { 1 8 } = $ F { 1 } { 6 } $", "predi": "F { 5 } { 1 3 } * F { 3 } { 5 } * F { 1 0 } { 1 8 } = $ F { 1 } { 6 } $"}, "mask_fbd98461-e1b5-11ed-b785-f3a681c68f28_7.jpg": {"label": "3 7 / 7 = $ 5 $ P $ 2 $", "predi": "3 7 / 7 = $ 5 P 2 $"}, "mask_dc8f6901-d123-11ed-8559-8d0ce8d3b4ac_5.jpg": {"label": "= 2 4 *", "predi": "$ = 2 4 * $"}, "mask_feab8181-c076-11ed-abf9-61e66d7d6dc1_25.jpg": {"label": "/ 4 = $ 1 7 0 $", "predi": "0 / 4 = $ 1 7 0 $"}, "mask_f5039341-dd39-11ed-b5b6-91df6f4f3ca1_14.jpg": {"label": "1 2 5 ° + 1 5 ° + 4 0 ° = $ 1 8 0 $", "predi": "1 2 5 ° + 1 5 ° + 4 0 ° = $ 1 8 0 ° $"}, "mask_baccebd1-dea7-11ed-8a6e-a3554a8f3cfa_44.jpg": {"label": "8 - 4 = $ 8 4 $", "predi": "8 8 - 4 = $ 8 4 $"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_2.jpg": {"label": "5 - 6 - 8 4", "predi": "5 - - 8 4"}, "mask_d7955d40-cc7a-11ed-83c3-1f3748e6cf6e_52.jpg": {"label": "( $ 1 5 $ ) - 8 = $ 7 $", "predi": "( $ 1 5 $ ) - 8 = 7"}, "mask_c36c7071-dde0-11ed-8607-49e437fc1546_43.jpg": {"label": "2 8 + 9 = $ 3 8 $", "predi": "2 9 + 9 = $ 3 8 $"}, "mask_0d750bd1-e0f4-11ed-b188-237e03a1d496_31.jpg": {"label": "1 2 - 3 = $ 3 5 7 ) $", "predi": "1 2 - 3 = $ 3 6 7 7 $"}, "mask_7a9f7241-d870-11ed-9c06-c909318bca6a_15.jpg": {"label": "五 = $ 2 7 $", "predi": "+ 4 7 = $ 7 7 $"}, "mask_e4f569e1-e880-11ed-8240-c907d23284e5_26.jpg": {"label": "6 3 0 - 1 8 = $ 9 1 2 $", "predi": "9 3 0 - 1 8 = $ 9 1 2 $"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_19.jpg": {"label": "6 - 6 - 2 4", "predi": "6 - 8 - 2 4"}, "mask_ecf7db11-ca34-11ed-843c-01c4cd6400ad_16.jpg": {"label": "2 5 = 3 3", "predi": "2 = 2 $ > $"}, "mask_2c8ad3b1-c8a8-11ed-bf9c-9bac90c71228_12.jpg": {"label": "6 2 * 1 0 0 = $ 3 1 0 0 $", "predi": "6 ^ 2 * 1 0 0 = $ 3 1 0 0 $"}, "mask_1c7324d1-ed53-11ed-918b-398e7122eb1f_31.jpg": {"label": "0 . 1 2 5 = ( $ F { 1 2 5 } { 1 0 0 0 } $ )", "predi": "0 . 1 2 5 = ( $ F { 1 2 5 } { 1 0 0 0 } $"}, "mask_5c739961-dd2d-11ed-9858-5fd236f404c6_24.jpg": {"label": "4 c m : 3 2 k m = 1 : ( $ 8 0 0 0 0 0 $ )", "predi": "4 c m : 3 2 k m = 1 : ( $ 8 0 0 0 0 0 $"}, "mask_5d4fcda1-efba-11ed-828b-296aa3f01d24_18.jpg": {"label": "( ) + 1 8 = 8 3 2", "predi": "( $ 8 5 $ ) + 1 8 = 8 3 2"}, "mask_fce25c41-e4ff-11ed-b20a-59db44123f4d_24.jpg": {"label": "0 . 0 9 = $ F { 9 } { 1 0 0 } $", "predi": "0 . 0 9 9"}, "mask_1babfb31-ec09-11ed-96f5-81c53d06ab5a_7.jpg": {"label": "8 2 - 5 = $ 7 8 $", "predi": "8 3 - 5 = $ 7 8 $"}, "mask_6a0ab7e1-d5ce-11ed-b7c5-4794a5632b19_40.jpg": {"label": "1 0 6 5 0 2 4 3 元 ≈ ( $ 1 0 6 5 . 0 $ ) 万", "predi": "1 0 6 5 0 2 4 3 ≈ ( $ 1 0 6 5 . 0 $ ) 万"}, "mask_5d4fcda1-efba-11ed-828b-296aa3f01d24_22.jpg": {"label": "( $ 3 0 9 $ ) - 4 0 = 1 6 9", "predi": "( $ 2 0 3 $ ) - 4 0 = 1 6 9"}, "mask_1c0d6441-cbcc-11ed-821d-a9dd0e75b623_0.jpg": {"label": "9 * 2 . 1 5 + 2 . 1 5 = ( $ 2 . 1 5 $ + $ 9 $ ) * 2 . 1 5 = $ 1 1 . 1 5 $", "predi": "9 * 2 . 1 5 + 2 . 1 5 = ( $ 2 . 1 1 $ + $ 9 $ ) * 2 . 1 5 = $ 1 1 . 1 5 $"}, "mask_ec106de1-e1c5-11ed-bdc9-a3caf80f747e_24.jpg": {"label": "平 年 一 年 = ( $ 3 6 5 $ ) 天", "predi": "4 成 成 成 = ( $ 3 6 5 $ ) 千"}, "mask_1fcd82d1-c524-11ed-81dc-f92359da3700_1.jpg": {"label": "( $ 9 . 0 0 $ ) > 9 . 0 1 < ( $ 9 . 0 2 $ )", "predi": "( $ 9 . 0 0 $ ) < 9 . 0 1 < ( $ 9 . 0 2 $ )"}, "mask_f6515dd1-c2ff-11ed-8505-cb42a645c6e7_8.jpg": {"label": "2 4 0 * 9 0 = $ 2 1 5 0 0 $", "predi": "2 4 0 * 9 0 = $ 2 1 5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0"}, "mask_dc62d251-e57d-11ed-a52a-850d2fc4cb10_4.jpg": {"label": "五 = ( $ 2 0 1 张 $", "predi": "= ( $ 2 0 吨 $ ) 吨"}, "mask_1ecd8a80-bfeb-11ed-98e1-8bbe6bee5ebd_44.jpg": {"label": "3 7 / 3 = $ 1 2 P 1 $", "predi": "3 7 / 3 = $ 1 2 P $"}, "mask_6e7d6651-d926-11ed-ba38-1bf6c3912b8b_10.jpg": {"label": "1 } { 3 }", "predi": "F { } { 3 0 }"}, "mask_eab01231-c6f8-11ed-975f-4566a2759b4a_11.jpg": {"label": "F { 3 } { 8 } * 1 1 = $ F { 3 3 } { 8 } $", "predi": "F { 3 } { 8 } * 1 1 = $ F { 3 3 3 } { 8 } $"}, "mask_e65f7e20-dd30-11ed-8d79-4332d1ec06e4_40.jpg": {"label": "3 4 / 7 = $ 5 P 1 $", "predi": "3 4 / 7 = 5 P 1"}, "mask_e166ba01-e500-11ed-81f9-a7a05c30a856_21.jpg": {"label": "3 0 0 + 6 1 2 = $ 9 4 2 $", "predi": "3 3 0 + 6 1 2 = $ 9 4 2 $"}, "mask_1ca7ceb1-dd13-11ed-ab7f-5b28ff4dd439_39.jpg": {"label": "9 / 1 5 = F { ( $ 9 $ ) } { ( $ 1 5 $ ) }", "predi": "9 / 1 5 = F { ( $ 9 $ } { ( $ 1 5 $ }"}, "mask_4e1a9a71-eda0-11ed-9242-11a75308479a_29.jpg": {"label": "1 0 8 5 5 0 ≈ ( $ 1 0 . 8 6 $ ) 万", "predi": "1 0 8 5 5 0 ≈ ( $ 1 0 . 8 8 $ ) 万"}, "mask_3f51ba50-c86b-11ed-ac62-f3a1653b3c30_3.jpg": {"label": "8 ^ 2 * 4 = $ 6 4 ^ 4 = 2 5 6 $", "predi": "8 ^ 2 * 4 = $ 6 4 * 4 = 2 5 6 $"}, "mask_0ec44251-ed90-11ed-9788-8dc10d0c82a5_14.jpg": {"label": "1 3 . 5 - 0 . 1 6 = $ - 1 3 . 3 4 $", "predi": "1 3 . 5 - 0 . 1 6 = $ 7 3 . 3 4 $"}, "mask_ff3307a1-c250-11ed-8390-15967ebdcb8c_15.jpg": {"label": "1 分 十 米 1 分 5 元 ( $ 1 1 0 0 $ )", "predi": "1 分 千 米 1 分 5 元 ( $ 1 1 0 0 $ )"}, "mask_6fef31f1-ceb6-11ed-b656-59d635ff4dae_5.jpg": {"label": "8 0 d m ^ 3 = ( ( $ 0 . 0 8 $ ) m ^ 3", "predi": "8 0 d m ^ 3 = ( $ 0 . 0 8 $ ) m ^ 3"}, "mask_e0ae4421-d786-11ed-85e6-6332aaa3c89c_23.jpg": {"label": "0 ( 1 F { 5 9", "predi": "0 1 - 5 9"}, "mask_4a8a5cd1-d86d-11ed-bc58-f37ee0cdc360_9.jpg": {"label": "2 - F { 5 } { 6 } = F { 7 } { 6 }", "predi": "2 - F { 5 } { 6 } = $ F { 7 } { 6 } $"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_20.jpg": {"label": "1 1 1 1 1 1 1 1 1 1 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_6ac08011-dac0-11ed-b855-e13f205ce6c4_3.jpg": {"label": "1 9 9 / 9 0 = $ 2 P 1 0 $", "predi": "1 9 9 / 9 0 = $ 2 P 1 9 $"}, "mask_d60505c1-d47c-11ed-ad0c-bbd87a87a925_26.jpg": {"label": "1 1 1 5", "predi": "1 1 1 1 1"}, "mask_ef9c3f91-cef4-11ed-a171-8da2f75df540_33.jpg": {"label": "F { 1 } { 4 } * F { 5 } { 8 } = ( $ F { 1 } { 1 . 5 } $ ) * F { 5 } { 2 4 }", "predi": "F { 1 } { 4 } * F { 5 } { 8 } = ( $ F { 1 } { 1 5 } $ ) * F { 5 } { 2 4 }"}, "mask_e6e2a681-e4f4-11ed-b274-7d49d3347fd3_9.jpg": {"label": "1 1 6 P", "predi": "1 1 / 6 = $ 1 $ P"}, "mask_de7c7121-bffa-11ed-bdf5-b1183f85a2a0_41.jpg": {"label": "1 . 5 * 8 0 = $ 1 2 0 $", "predi": "1 . 5 * 8 0 = $ 1 2 0 . $"}, "mask_3c279121-e633-11ed-98f0-3f9fcd65488e_43.jpg": {"label": "3 . 1 4 * ( 0 . 2 / 2 ) ^ 2 = $ 0 . 0 3 1 4 $", "predi": "3 . 1 4 * ( 0 . 2 / 2 ^ 2 = $ 0 . 0 3 1 4 $"}, "mask_f5ad9b51-dc3b-11ed-a3da-e1b329483088_43.jpg": {"label": "4 2 3 0 7 0 3 3", "predi": "2 3 - 3 0 - 7 0 = $ 2 3 2 $"}, "mask_e0ae4421-d786-11ed-85e6-6332aaa3c89c_14.jpg": {"label": "F { 2 { 1 } { 6 } {", "predi": "F { 2 . } { 2 1 } { 1 5 5"}, "mask_fd2fe421-e5bc-11ed-9313-b3d4c912328d_39.jpg": {"label": "8 7 ° - 6 5 ° = $ 2 2 ° $", "predi": "8 7 ° - 6 5 ° = $ 2 2 0 $"}, "mask_2a5b03f1-ed5e-11ed-8973-89889703213c_43.jpg": {"label": "4 0 4 / 4", "predi": "4 0 4 / 4 ="}, "mask_f6c2f251-e222-11ed-8e62-e7ec90002be1_26.jpg": {"label": "5 - 8 - 4 = $ 9 $", "predi": "5 + 8 - 4 = $ 9 $"}, "mask_fe660dd1-c8aa-11ed-81c2-ad6785b79d8f_52.jpg": {"label": "5 1 1 1", "predi": "7 1 / 4 = $ 1 8 $"}, "mask_dc320d31-eb42-11ed-942d-b958113e4192_28.jpg": {"label": "1 年 = ( $ 1 2 $ ) 个 月", "predi": "1 秒 = ( $ 1 2 $ ) 千 顷"}, "mask_6f73ee01-e9be-11ed-9bfc-cf4455f8232a_23.jpg": {"label": "6 6 6 $ $ 6 0 + 6", "predi": "6 6 + 6 $ > $ 6 0 + 6"}, "mask_ed452870-c278-11ed-acbf-b3579151b686_12.jpg": {"label": "5 ^ 3 * 4 = $ 1 0 0 $", "predi": "5 ^ 2 * 4 = $ 1 0 0 $"}, "mask_dc1a9901-d938-11ed-bf18-75839265abea_4.jpg": {"label": "五 七 五 4 1 2 0 0 米 成 = ( $ 2 $ ) 4", "predi": "0 0 0 + 1 2 0 0 千 克 = ( $ 2 $ ) 吨"}, "mask_db1e1371-bfc4-11ed-a01c-a1500e978aba_9.jpg": {"label": "F { 1 } }", "predi": "F { 1 }"}, "mask_1fcd82d1-c524-11ed-81dc-f92359da3700_16.jpg": {"label": "( $ 1 0 . 4 4 7 $ ) < 1 0 . 4 4 8 < ( $ 1 0 . 4 4 9 $ )", "predi": "( $ 1 0 . 4 4 7 $ ) < 1 0 . 4 4 8 < ( $ 1 0 . 4 4 9 $"}, "mask_f96e7a91-d5fe-11ed-ab2a-997217dc166f_42.jpg": {"label": "5 6 / ( 5 1 - 4 4 ) = $ 8 $", "predi": "5 6 / ( 5 1 - 4 4 = $ 8 $"}, "mask_1fdb8e31-bfe2-11ed-86ae-37e9b0c5870b_13.jpg": {"label": "F { 1 } { 2 } - F { 3 } { 1 0 } = $ F { 2 } { 1 0 } = F { 1 } { 5 } $", "predi": "F { 1 } { 2 } - F { 3 } { 1 0 } = $ F { 2 } { 1 0 } = F { 1 5 } { 5 } F { 1 } { 1 } { 1 } { 1 1 } {"}, "mask_1bb85071-db64-11ed-bfe7-dfd377be3e7a_43.jpg": {"label": "F { 8 } { 5 0 } = $ F { 8 } { } = F { 4 } { 2 5 } $", "predi": "F { 8 } { 5 0 } = $ F { 8 } { 5 } = F { 4 } { 2 5 } $"}, "mask_dd3bf510-c590-11ed-86b8-a3e4d2635a18_18.jpg": {"label": "1 8 0 ° - 6 7 ° - 4 3 ° = $ 7 0 ° $", "predi": "1 8 0 ° - 6 7 ° + 4 3 ° = $ 7 0 ° $"}, "mask_de739fa0-d8f5-11ed-b909-8dd851d4ca9d_3.jpg": {"label": "F { 1 } { 8 } + F { 3 } { 8 } = $ F { 4 } { 8 ] $", "predi": "F { 1 } { 8 } + F { 3 } { 8 } = $ F { 4 } { 8 } $"}, "mask_d052eed0-e633-11ed-83fb-19e9e0104357_26.jpg": {"label": "0 . 1 8 = $ 1 0 $", "predi": "0 . 1 8 = $ F { } { 1 0 0 } $"}, "mask_6dd2f001-dd0c-11ed-9d84-d32c0689dfde_17.jpg": {"label": "2 4 5 2 6 0 0 0 = ( $ 2 4 5 2 . 6 $ )", "predi": "2 4 5 2 6 0 0 0 = ( $ 2 4 5 2 . 6 $"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_16.jpg": {"label": "七 七 ( ( > $ m 七 ( $ ) 1", "predi": "2 5 6 8 7 3 5 9 8 ≈ ( ) 亿"}, "mask_eda85d81-d2d3-11ed-abda-29d3b0564b69_51.jpg": {"label": "F { 6 } 0 > 5", "predi": "F { 5 } { 角 0 1 0"}, "mask_2e22bd81-debc-11ed-8cf0-a7cf1a8c1d09_14.jpg": {"label": "F { 6 } { 2 1 } = $ F { 1 6 8 } { 5 8 8 } $", "predi": "F { 6 } { 2 1 } = $ F { 1 6 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8"}, "mask_0cf98c21-d470-11ed-9db4-a5279d1f44e3_29.jpg": {"label": "3 5 7 / 7 / 3 = $ 1 7 $", "predi": "3 5 7 / 7 / 3 = $ 1 7 . $"}, "mask_eb6cf4d0-eda0-11ed-a5f2-3d0645728940_0.jpg": {"label": "9 5 ° + 4 5 ° + 4 0 ° = $ 1 8 0 ° $", "predi": "9 5 ° + 4 5 ° + 4 0 ° = $ 1 8 ° $"}, "mask_dbfed031-deb5-11ed-99f5-7f21a38539ac_1.jpg": {"label": "4 4 角 = ( $ 1 5 $", "predi": "4 4 角 = ( $ 1 1 $"}, "mask_4d3d6441-c337-11ed-ad12-879f3e6234a2_10.jpg": {"label": "9 - ( $ 1 0 $ ) =", "predi": "1 9 - ( $ 1 0 $ ) ="}, "mask_dda2fb41-db3c-11ed-bbe1-fb5d906afa04_4.jpg": {"label": "1 = ( $ 3 6 P $", "predi": "1 = ( $ 3 6 $"}, "mask_3c279121-e633-11ed-98f0-3f9fcd65488e_6.jpg": {"label": "( 1 / 2 ) ^ 2 = $ 0 . 2 5 $", "predi": "( 1 / 2 ^ 2 = $ 0 . 2 5 $"}, "mask_ec106de1-e1c5-11ed-bdc9-a3caf80f747e_16.jpg": {"label": "4 9 天 = ( $ 7 $ ) 周", "predi": "4 9 千 = ( $ 7 $ ) 厘"}, "mask_ebdc0621-cb79-11ed-a145-01c7965c6742_19.jpg": {"label": "2 0 5 * 4 0 = $ 8 2 0 $", "predi": "2 0 5 * 4 0 = $ 8 2 0 0 0 $"}, "mask_3b471c30-d78d-11ed-a82a-f5347e681596_26.jpg": {"label": "F { 1 1 } { 2 0 } = ( $ 1 1 $ ) / ( $ 2 0 $ ) = ( $ 0 . 5 5 $ )", "predi": "F { 1 1 } { 2 0 } = ( $ 1 1 $ ) / ( $ 2 0 $ ) = ( $ 0 . 5 5 $"}, "mask_f94b5f81-cd69-11ed-a051-ff1632008687_13.jpg": {"label": "= $ P $", "predi": "= $ $"}, "mask_1fb27f31-e6ee-11ed-9901-954cfd85eb53_16.jpg": {"label": "$ = F { 8 } { 1 3 } $", "predi": "F { 8 } { 1 3 }"}, "mask_e35fa061-e80c-11ed-9dd3-81376b617343_14.jpg": {"label": "5 + 4 - 9 = $ 1 0 $", "predi": "1 5 + 4 - 9 = $ 1 0 $"}, "mask_5c739961-dd2d-11ed-9858-5fd236f404c6_2.jpg": {"label": "3 c m : ( $ 1 5 0 0 0 0 $ ) k m = 1 : 5 0 0 0 0", "predi": "3 c m : ( $ 1 5 0 0 $ ) k m = 1 : 5 0 0 0 0"}, "mask_1a1757f1-e0c5-11ed-8d72-afb3f1fce4a2_1.jpg": {"label": "4 . . 8 5 + F { 3 } { 8 } + F { 5 } { 8 } = 4 . 8 5 + ( $ F { 3 } { 8 } $ + $ F { 5 } { 8 } $ )", "predi": "4 . 8 5 + F { 3 } { 8 } + F { 5 } { 8 } = 4 . 8 5 + ( $ F { 3 } { 8 } + F { 5 } { 8 } $ )"}, "mask_d0474a51-d9e3-11ed-84f9-453cfc188b9d_33.jpg": {"label": "4 . 2 + 1 9 * 4 . 2 = $ 8 . $", "predi": "4 . 2 + 1 9 * 4 . 2 = $ 8 . 4 $"}, "mask_dc5fe520-cfd1-11ed-8036-3719fb7a0815_14.jpg": {"label": "4 5 0 / F { 1 } { 1 0 0 0 0 } = $ 4 5 0 0 0 0 0 $", "predi": "4 5 0 / F { 1 } { 1 0 0 0 0 } = $ 4 5 0 0 0 0 0 0 $"}, "mask_e42ea331-cdc0-11ed-b38d-61d6cdafd92f_2.jpg": {"label": "0 5 * F { 1 } { 4 } = $ F { 1 } { 8 } $", "predi": "0 . 5 * F { 1 } { 4 } = $ F { 1 } { 8 } $"}, "mask_ff274691-c089-11ed-ae50-43c86fc84dfc_39.jpg": {"label": "8 0 0 / [ ( 4 0 * ( 5 5 - 3 5 ) ] = $ 1 $", "predi": "8 0 0 / [ [ 0 * ( 5 5 - 3 5 ) ] = $ 1 $"}, "mask_f519b381-cc47-11ed-a7ad-ff031fc9473a_31.jpg": {"label": "五 $ $ * $ $ = $ $", "predi": "成 $ $ * $ $ = $ $"}, "mask_f12fd680-ceb5-11ed-b656-59d635ff4dae_40.jpg": {"label": "1 5 8 - 5 4 = $ 1 $", "predi": "1 5 8 - 5 4 ="}, "mask_ec22af01-d851-11ed-b335-2d5f49ab9aa4_24.jpg": {"label": "1 . 5 6 m ^ 2 $ > $ 1 5 6 分 d m ^ 2", "predi": "1 . 5 6 m ^ 2 $ > $ 1 5 6 d m ^ 2"}, "mask_e7dcf541-c659-11ed-a5f4-69b0e68cef5f_26.jpg": {"label": "F { 1 3 } { 1 8 } - F { 5 } { 2 4 } = $ F { 3 4 } { 5 } $", "predi": "F { 1 3 } { 1 8 } - F { 5 } { 5 4 } = $ F { 3 4 } { 5 } $"}, "mask_5b9a4f51-d91a-11ed-b621-b5a720b0269b_27.jpg": {"label": "0 0 1 4 1 8 1", "predi": "9 0 / 4 = $ 3 4 $"}, "mask_1fce8271-e35b-11ed-a0b0-17b8cfd28887_4.jpg": {"label": "F { 2 } { 9 } * F { 3 } { 8 } = $ F { 2 * 3 } { 4 } = F { 1 } { 1 2 } $", "predi": "F { 2 } { 9 } * F { 3 } { 8 } = $ F { 2 * 3 } { 9 * 8 4 } = F { 1 } { 1 2 } $"}, "mask_fa0a2b60-cae7-11ed-8e9c-c71f313ad890_33.jpg": {"label": "4 * ( $ 6 $ ) < 2 5", "predi": "4 * ( $ 6 P $ ) < 2 5"}, "mask_3fcf51c1-d20e-11ed-9621-fd090491243c_22.jpg": {"label": "1 0 1 m 八 八 4 八", "predi": "0 . 6 4 7 t = $ 6 4 0 $ ) k"}, "mask_4bde2e70-e41d-11ed-b190-a701d8346fd0_25.jpg": {"label": "6 . 4 亿 = ( $ 1 6 4 0 0 0 0 0 0 $ )", "predi": "6 . 4 亿 = ( $ 1 6 4 0 0 0 0 0 0 $"}, "mask_e781d991-c8a1-11ed-9d0a-95ec6217db79_39.jpg": {"label": "1 6 : F { 8 } { 2 1 } = $ 2 * 2 1 = 4 2 $", "predi": "1 6 : F { 8 } { 2 1 } = $ 2 2 1 = 4 2 $"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_8.jpg": {"label": "六 成 = ( $ 6 0 $ ) %", "predi": "公 成 = ( $ 6 0 $ ) %"}, "mask_fe660dd1-c8aa-11ed-81c2-ad6785b79d8f_49.jpg": {"label": "1 . 1 1 1 1", "predi": "1 1 1 1 1 1 1"}, "mask_ee4f0771-d157-11ed-901f-0596f1039dbf_19.jpg": {"label": "9 0 - 1 4 = $ 7 0 $", "predi": "9 0 - 1 4 = $ 7 6 $"}, "mask_faa3ef91-eb42-11ed-ae0b-05fb2f89647a_15.jpg": {"label": "1 3 - 7 = $ 6 $", "predi": "1 3 - 7 = $ 6 1 $"}, "mask_04d36130-e0f3-11ed-b680-ddaa959e4fe1_20.jpg": {"label": "1 0 * 0 . 4 8 = $ 4 8 $", "predi": "1 0 0 * 0 . 4 8 = $ 4 8 $"}, "mask_e086df60-bfb3-11ed-a635-0fe7b680b134_0.jpg": {"label": "8 + 2 0 = $ 3 8 $", "predi": "8 + 3 0 = $ 3 8 $"}, "mask_dc5fe520-cfd1-11ed-8036-3719fb7a0815_0.jpg": {"label": ": 8 1 0 0 k m = 1 : ( $ 9 0 0 0 0 0 $", "predi": ": 8 1 0 0 k m = 1 : ( $ 9 0 0 0 0 0 0 $"}, "mask_4bc86941-dcda-11ed-8d6f-5165cfe46b50_23.jpg": {"label": "$ ( $ 五 3", "predi": "$ > $ 张 ="}, "mask_db5c6951-c1a3-11ed-9f04-6510225d74fa_38.jpg": {"label": "1 . 成 - 0", "predi": "1 . 5 - 5 0"}, "mask_3ff7a871-e2b0-11ed-8772-5705ff086af7_29.jpg": {"label": "0 . 6 5 + F { 1 } { 2 0 } = $ F { 6 5 } { 1 0 0 } + F { 1 } { 2 0 } = F { 6 5 } { 1 0 0 } + F { 5 } { 1 0 0 } = F { 7 0 } { 1 0 0 } $", "predi": "0 . 6 5 + F { 1 } { 2 0 } = $ F { 6 5 } { 1 0 0 } + F { 1 } { 2 0 } = F { 6 5 } { 1 0 0 } + F { 5 } { 1 0 0 } = F { 7 0 } 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0"}, "mask_f3b006d1-d399-11ed-8a75-796b68d0d900_18.jpg": {"label": "2 . 3 1 * 1 0 = $ 2 3 . 1 $", "predi": "2 . 3 1 * 1 0 = $ 2 3 . $"}, "mask_fc781e71-bfc0-11ed-a147-f3cbab19d534_7.jpg": {"label": "1 . 5 / 6 . 3 = $ 7 . 8 $", "predi": "1 . 5 + 6 . 3 = $ 7 . 8 $"}, "mask_e1c07520-c7e8-11ed-b033-a593c6dc4e7b_2.jpg": {"label": "F { 4 } { 5 } : F { 3 } { 1 0 } = 8 : 3", "predi": "F { 4 } { 5 } : F { 3 } { 1 0 } = $ 8 : 3 $"}, "mask_dec85980-e4e1-11ed-89ef-d97474040c54_2.jpg": {"label": "F { 5 { } 2", "predi": "F { 9 . 2 1"}, "mask_3fa101b1-d201-11ed-84a1-456fdeaec262_39.jpg": {"label": "F { 4 } { 5 } = F { 1 6 } ( $ 2 0 $ ) }", "predi": "F { 4 } { 5 } = F { 1 6 } { ( $ 2 0 $ ) }"}, "mask_efb0fa51-d379-11ed-9280-c3b565066808_9.jpg": {"label": "F { 3 7 5 } { 1 0 0 0 } = F { } { 8 }", "predi": "F { 3 7 5 } { 1 0 0 0 } = F { ( ) } { 8 }"}, "mask_5af43a51-d9d5-11ed-9064-19b9b8c76ddb_2.jpg": {"label": "3 4 5 千 克 = ( $ 0 . 3 4 5 $ } 吨", "predi": "3 4 5 千 克 = ( $ 0 . 3 4 5 $ ) 吨"}, "mask_5b9a4f51-d91a-11ed-b621-b5a720b0269b_16.jpg": {"label": "0 1 4 9 3", "predi": "9 0 4 2 = 9 2"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_14.jpg": {"label": "F { 1 0 } { 3 } - F { 7 } { 9 } = $ F { 3 0 } { 9 } - F { 7 } { 9 } = F { 3 0 - 7 } { 9 } = F { 2 3 } { 9 } $", "predi": "F { 1 0 } { 3 } - F { 7 } { 9 } = $ F { 3 0 } { 9 } - F { 7 } { 9 } = F { 3 0 - 7 } { 9 } = F { 2 3 3 } { 9 } $ 3 3 3 3 3 3 } { 9 } { 9 3 3 } { 9 } { 9 3 3 } {"}, "mask_dfc40281-db68-11ed-8612-758c44062f71_28.jpg": {"label": "5 2 = ( $ 5 $ ) 角 ( $ 2 $ ) 分", "predi": "5 2 分 = ( $ 5 $ ) 角 ( $ 2 $ ) 分"}, "mask_f5a09d01-d79b-11ed-b1f9-d59d104042e2_12.jpg": {"label": "七 分 0 米", "predi": "5 分 0 米"}, "mask_f6527ce1-c96e-11ed-947b-57b7182967c2_26.jpg": {"label": "1 0 0 元 = ( $ 1 $ 张 1 0 元 + 4 张 2 0 元", "predi": "1 0 0 元 = ( $ 1 $ ) 张 1 0 元 + 4 张 2 0 元"}, "mask_3fe0d961-d43c-11ed-8cf1-9b5e5e75ffc7_3.jpg": {"label": "F { 1 5 } { 2 0 } = $ F { 3 } { 4 } $", "predi": "F { 1 5 } { 2 0 } = $ F { 5 } { 4 } $"}, "mask_1f64cfc1-cc94-11ed-bb44-3b11b70e4561_13.jpg": {"label": "5 = $ $", "predi": "5 6 / 8 = $ 7 $"}, "mask_fcd02331-d75e-11ed-a616-eb43d73a4732_45.jpg": {"label": "- 3 + 4 0 = $ 1 $", "predi": "3 + 1 0 = $ 1 $"}, "mask_5a116f41-dca5-11ed-912e-f724c0c2a1a0_25.jpg": {"label": "1 0 0 * 0 . 0 5 6 + 1 0 = $ 0 . 5 6 $", "predi": "1 0 0 * 0 . 0 5 6 / 1 0 = $ 0 . 5 6 $"}, "mask_fb9e56a1-ccad-11ed-95f3-41f0b9787350_2.jpg": {"label": "9 0 / ( 4 * 2 ) = $ 1 2 $", "predi": "9 6 / ( 4 * 2 ) = $ 1 2 $"}, "mask_3d04f691-dd24-11ed-b269-bf8a73a71052_5.jpg": {"label": "F { 2 } { 2 5 } = F { 4 } { ( $ 5 0 0 $ ) }", "predi": "F { 2 } { 2 5 } = F { 4 } { ( $ 5 0 $ ) }"}, "mask_efb0fa51-d379-11ed-9280-c3b565066808_35.jpg": {"label": "F { 3 5 } { 5 0 } = F { 7 } { }", "predi": "F { 3 5 } { 5 0 } = F { 7 } { ( ) }"}, "mask_6dd2f001-dd0c-11ed-9d84-d32c0689dfde_7.jpg": {"label": "0 . 0 5 亿 = ( $ 5 0 0 0 0 0 0 $ )", "predi": "0 . 0 5 亿 = ( $ 5 0 0 0 0 0 0 $"}, "mask_d8972341-c1ad-11ed-b768-613d9569d463_31.jpg": {"label": "F { 1 3 } { } / 2 6 =", "predi": "F { 1 3 } { 1 3 } / 2 6 ="}, "mask_4f15a891-df84-11ed-b25b-556838d0d558_30.jpg": {"label": "F { 1 } { 9 } + F { 3 } { 8 } = $ F { 3 5 } { 1 2 } $", "predi": "F { 1 } { 9 } + F { 3 } { 8 } = $ F { 3 5 } { 7 2 } $"}, "mask_1eac3931-d9fd-11ed-aae3-47e61ed0b4f9_19.jpg": {"label": "9 F { 1 } { 2 } $ = $ F { 1 9 } { 2 }", "predi": "9 F { 1 } { 2 } $ - $ F { 1 9 } { 2 }"}, "mask_5deecaa1-ddd0-11ed-a1ba-cfbbd3beaed0_3.jpg": {"label": "1 角 3 分 $ < $ 2 角", "predi": "1 角 3 分 $ < $ 2 角 1"}, "mask_2a1f0201-eff3-11ed-aa3f-57649601d057_3.jpg": {"label": "F { 5 } { 3 } + F { 1 5 { 1 1 } = $ F { 5 5 } { 3 3 } + F { 4 5 } { 3 3 } = F { 1 0 0 } { 3 3 } $", "predi": "F { 5 } { 3 } + F { 1 5 } { 1 1 } = $ F { 5 5 } { 3 3 } + F { 4 5 } { 3 3 } = F { 1 0 0 } { 3 3 } $"}, "mask_d8659441-e973-11ed-8d17-f3444709f1b5_10.jpg": {"label": "三 2 $ ) = $ 4 2", "predi": ") = $ 米 4 $"}, "mask_f23f1f61-e7ea-11ed-b8bf-3f3ddf6e0703_23.jpg": {"label": "3 6 * F { 5 } { 1 2 } = $ 1 3 $", "predi": "3 6 * F { 5 } { 1 2 } = $ 1 5 $"}, "mask_dba9f521-d680-11ed-a434-2134a46105ac_29.jpg": {"label": "8 8 + 3 9 = $ 6 . 7 8 $", "predi": "8 8 + 3 . 9 = $ 6 . 7 8 $"}, "mask_f21ec7c1-c07a-11ed-8b24-9f7d39551bb9_38.jpg": {"label": "1 0 * F { 1 } { 2 } = = $ 5 $", "predi": "1 0 * F { 1 } { 2 } = $ 5 $"}, "mask_2b4d2d20-dead-11ed-92ce-8f9d95bbca57_30.jpg": {"label": "5 6 = 5", "predi": "8 / 1 . 6 = $ 5 $"}, "mask_fb22cd31-c269-11ed-94cc-c1e563a13ae8_33.jpg": {"label": "2 8 0 / 5 = 5 6", "predi": "2 8 0 / 5 = $ 5 6 $"}, "mask_fac6cd71-d1b2-11ed-a7e7-a16bb42ce68e_42.jpg": {"label": "( $ 1 F { 7 } { 4 } $ ) * + 1 F { 4 } { 7 } = 1", "predi": "( $ 1 F { 7 } { 4 } $ ) * 1 F { 4 } { 7 } = 1"}, "mask_d2059ba0-e003-11ed-a8a9-216faef1c6fa_29.jpg": {"label": "( $ 3 八 七 $ ) 四 m", "predi": "( $ 3 米 $ ) m"}, "mask_0d63cf91-e59b-11ed-afb0-1b7f0c684913_3.jpg": {"label": "3 0 0 / F { 1 } { 1 0 0 0 0 } = $ 3 0 0 0 0 0 0 $", "predi": "3 0 0 / F { 1 } { 1 0 0 0 0 } = $ 3 0 0 0 0 0 0 0 $"}, "mask_eaadd9b1-ecd3-11ed-858c-4b17e7bb57e4_39.jpg": {"label": "7 - 8 $ 6 7 $", "predi": "7 5 - 8 = $ 6 7 $"}, "mask_d052eed0-e633-11ed-83fb-19e9e0104357_23.jpg": {"label": ". 0 0 9 = $ 5 $", "predi": ". 0 0 9 = $ 9 $"}, "mask_f02ae1a1-ca2b-11ed-8d7b-4b03830502be_11.jpg": {"label": "6 0 = F { 1 } { 5 0 }", "predi": "6 0 = $ F { 1 } { 5 0 } $"}, "mask_e3020711-d534-11ed-a881-dd60a0497853_26.jpg": {"label": "3 t 1 8 0 k g = ( $ 3 . 1 8 $ ) t", "predi": "3 t 1 8 0 k g = ( $ 3 . 1 8 $ ) t t"}, "mask_0bedc2f1-e1cd-11ed-b72c-89f65f93903e_14.jpg": {"label": "F { 3 } { 1 0 } = F { 3 * ( $ 4 $ ) } { 1 0 * ( $ 4 $ ) } = F { ( $ 1 2 $ ) } { ( $ 4 0 $ }", "predi": "F { 3 } { 1 0 } = F { 3 * ( $ 4 $ ) } { 1 0 * ( $ 4 $ ) } = F { ( $ 1 2 $ ) } { ( $ 4 0 $ ) }"}, "mask_edb967b1-ee62-11ed-9772-a546d28586eb_20.jpg": {"label": "F { 1 0 } { 3 } + F { 7 } { 1 0 } = $ F { 1 0 0 } { 3 0 } + F { 2 1 } { 3 0 } + F { 1 2 7 } { 3 0 } $", "predi": "F { 1 0 } { 3 } + F { 7 } { 1 0 } = $ F { 1 0 0 } { 3 0 } + F { 2 1 } { 3 0 } = F { 1 2 7 } { 3 0 } $"}, "mask_facb43e1-ce89-11ed-8861-4104c479a3ee_36.jpg": {"label": "七 5 = ( $ 1 5 5 $", "predi": "5 = ( $ 1 8 4 $"}, "mask_fe4d9e41-e34d-11ed-895e-ef180248dc40_29.jpg": {"label": "F [ 1 } { 2 } + F { 1 } { 8 } = $ F { 5 } { 8 } $", "predi": "F { 1 } { 2 } + F { 1 } { 8 } = $ F { 5 } { 8 } $"}, "mask_edae2411-bfb2-11ed-a3e0-c90cdb682c63_7.jpg": {"label": "9 2 - 6 9 = $ 2 3 $", "predi": "9 2 - 6 9 = $ 2 3 3 $"}, "mask_4d1fc7b1-c8a9-11ed-9c74-9f765749440d_4.jpg": {"label": "6 2 / 4 = $ 2 3 $", "predi": "9 2 / 4 = $ 2 3 $"}, "mask_1d2d90e1-c332-11ed-ab36-6f0e80a19d59_0.jpg": {"label": "F [ 1 7 } { 2 2 } - F { 5 } { 2 2 } - F { 7 } { 2 2 } =", "predi": "F { 1 7 } { 2 2 } - F { 5 } { 2 2 } - F { 7 } { 2 2 } ="}, "mask_1aa60951-cdbf-11ed-b874-7943e1116fb9_42.jpg": {"label": "F { 4 } { 2 3 } * 6 = $ F { 2 4 } { 2 3 } $", "predi": "F { 4 } { 2 3 } * 6 = $ F { 2 4 3 } { 2 3 } $"}, "mask_f47c09b1-de6b-11ed-8659-df206a520e06_39.jpg": {"label": "6 0 = $ 0 1 3 0 $", "predi": "6 0 = $ 0 3 0 $"}, "mask_4e562911-ec7d-11ed-a017-a16f8243b981_3.jpg": {"label": "3 3 3 3 * 3 3 3 3 = $ 1 1 1 0 8 8 8 9 $", "predi": "3 3 3 3 * 3 3 3 3 = 1 1 1 0 8 8 8 9"}, "mask_dc30f921-dd07-11ed-8d37-831e7084deb4_46.jpg": {"label": "6 元 - 8 元 = ( $ 8 $ ) 元", "predi": "1 6 元 - 8 元 = ( $ 8 $ ) 元"}, "mask_fe048aa1-e43a-11ed-bd43-dfc59631d029_3.jpg": {"label": "6 3 + 3 6 =", "predi": "6 3 + 3 6 = $ 0 $"}, "mask_5f435420-e320-11ed-99f6-81f8f4ba29d8_4.jpg": {"label": "1 = ( $ 0 . 2 $ ) m 3", "predi": "1 = ( $ 0 . 2 $ ) m ^ 3"}, "mask_e13c5281-cb81-11ed-8723-fd369f9d61a5_15.jpg": {"label": "F { 3 } { 8 } * 5 = $ F { 4 0 } { 3 } $", "predi": "F { 8 } { 3 } * 5 = $ F { 4 0 } { 3 } $"}, "mask_ddaad721-ea69-11ed-a991-e734eed98763_45.jpg": {"label": "3 . $ $ > 张 元 4 %", "predi": "3 . $ $ ) 米 米 米"}, "mask_1fb27f31-e6ee-11ed-9901-954cfd85eb53_10.jpg": {"label": "F { 2 } { 1 4 } = $ { 1 } { 7 } $", "predi": "F { 2 } { 1 4 } = $ F { 1 } { 7 } $"}, "mask_e54ab4e1-d921-11ed-b912-2334d9f34950_35.jpg": {"label": "F { 1 1 } { 1 5 } - F { 2 } { 3 } = $ F { 1 } { 1 5 } $", "predi": "F { 1 1 1 } { 1 5 } - F { 2 } { 3 } = $ F { 1 } { 1 5 } $"}, "mask_e4dd60f1-deaf-11ed-8fdd-a93b1b770418_7.jpg": {"label": "6 / 8 = F { ( $ 6 $ } { ( $ 8 $ ) }", "predi": "6 / 8 = F { ( $ 6 $ } { ( $ 8 $ }"}, "mask_5e3ec161-c550-11ed-865c-a750d6dbbacb_28.jpg": {"label": "3 7 / 8 = $ 4 P $", "predi": "3 7 / 8 = $ 4 P 1 $"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_23.jpg": {"label": "$ 八 八 八 成 1 五 ( $ ) 1 1", "predi": "2 5 6 2 8 5 4 ≈ ( ) 1 1"}, "mask_1e192391-e356-11ed-977f-63f155ca3a6c_22.jpg": {"label": "8 0 - ( 8 + 2 ) = $ 7 . 2 $", "predi": "8 0 - ( 8 + 2 ) = $ 7 2 $"}, "mask_fc844b51-d799-11ed-a603-638cf77b5798_18.jpg": {"label": "F { 2 } { 3 } = F { 2 * ( $ 1 1 $ ) } { 3 * ( $ 1 1 $ ) } = F { ( $ 2 2 $ ) } { ( $ 3 3 $ ) }", "predi": "F { 2 } { 3 } = F { 2 * ( $ 1 1 $ ) } { 3 * ( $ 1 1 $ ) } = F { ( $ 2 2 $ ) } { ( $ 3 3 $ ) } $ ) $ )"}, "mask_df6fdf91-d387-11ed-8768-51795f6dee33_16.jpg": {"label": "5 5 6 4 0 0 7 $ < $ 1 . 0 1", "predi": "5 5 6 4 0 0 7 $ < $"}, "mask_e723c511-dd1b-11ed-8f7c-1bb2a902275d_31.jpg": {"label": "5 4 / 7 = $ 7 $", "predi": "5 4 / 7 = 7"}, "mask_f6a22261-d736-11ed-89a9-5355b80fbe3a_35.jpg": {"label": "1 8 5 + 1 2 6 + 1 1 5 = $ 3 2 6 $", "predi": "8 5 + 1 2 6 + 1 1 5 = $ 3 2 6 $"}, "mask_3e546b11-ee69-11ed-b31c-9d611ad46d79_24.jpg": {"label": "F { 3 } { 2 0 0 } ≈ ( $ 0 . 0 2 $", "predi": "F { 3 } { 2 0 0 } ≈ ( $ 0 . 0 2 $ )"}, "mask_1b6ecc31-d541-11ed-8f3b-c1e5fb0428f2_5.jpg": {"label": "F { 3 } { 1 0 } * F { 5 } { 2 }", "predi": "F { 3 } { 1 0 } * F { 3 } { 1 0 } = $ F { 9 } { 1 0 0 } $"}, "mask_1e994951-ecc8-11ed-83a4-f954b980b063_37.jpg": {"label": "1 4 0 / 7 =", "predi": "1 4 0 / 7 = $ 0 $"}, "mask_1efde671-d20c-11ed-bdd7-abe835d02339_11.jpg": {"label": "1 5 8 + 5 = $ 1 6 $", "predi": "1 5 8 + 5 = $ 1 6 2 $"}, "mask_6a2bdc21-cf86-11ed-b16e-716b3d88ca57_3.jpg": {"label": "3 * m * m = 3 m ^ 2", "predi": "3 * m * m = $ 3 m ^ 2 $"}, "mask_6d1f8c31-eeec-11ed-a728-c508ad7b873a_14.jpg": {"label": "4 : 0 . 4 = $ 0 4 0 2 . $", "predi": "1 : 0 . 4 = $ 0 . 0 2 . $"}, "mask_fc35cb81-d21a-11ed-a137-e75cd9a33ba9_8.jpg": {"label": "9 0 d m 4 c m = ( $ 9 . 0 4 $ ) ) m", "predi": "9 0 d m 4 c m = ( $ 9 . 0 4 $ ) m"}, "mask_dfa175d1-ecc0-11ed-b16f-dfe147916af4_0.jpg": {"label": "2 $ { 元 ( $", "predi": "$ ) $ ( ("}, "mask_dfb08741-e7ad-11ed-acec-a30475fb8bda_16.jpg": {"label": "3 7 + 7 = $ 4 0 $", "predi": "3 7 + 7 = $ 4 4 $"}, "mask_e9a8b181-c25c-11ed-a7a6-29351b22d0bb_4.jpg": {"label": "6 ≈ / 7 ≈ 9 0", "predi": "6 % / 7 ≈ $ 9 0 $"}, "mask_e865a0c1-d932-11ed-96c1-73d76ce7c43a_15.jpg": {"label": "F { 4 } { 6 } * 6 = $ F { 2 4 } { 5 } $", "predi": "F { 4 } { 5 } * 6 = $ F { 2 4 } { 5 } $"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_22.jpg": {"label": "4 0 0 1 1 角 米 ( ) 1 4", "predi": "4 1 1 1 1 1 1 1 1 1"}, "mask_5d1a4ae1-d20f-11ed-a61e-2b470e54a03e_24.jpg": {"label": "0 0 / 1 0 0 * 1 0 = $ 4 0 $", "predi": "4 0 0 / 1 0 0 * 1 0 = $ 4 0 $"}, "mask_5cacf9f1-efe6-11ed-baaf-3f66f3380149_25.jpg": {"label": "9 8 0 m ^ 2 = ( $ 0 . 0 0 0 9 8 $ ) k m ^ 2", "predi": "9 8 0 m ^ 2 = ( ) k m ^ 2 0 . 0 0 0 9 8"}, "mask_f5cb8b21-c3f3-11ed-b617-33380b063175_24.jpg": {"label": "1 c m * 1 m m =", "predi": "1 c m : 1 m m ="}, "mask_da65e7d1-e433-11ed-a4ec-d78f5557c6a6_0.jpg": {"label": "0 . 8 7 5 = F { ( $ 8 7 5 $ ) } { $ 1 0 0 0 $ ) }", "predi": "0 . 8 7 5 = F { ( $ 8 7 5 $ ) } { $ $ 0 0 0 }"}, "mask_2d8e2a81-d5fc-11ed-85d6-13127a39c17e_38.jpg": {"label": "1 1 1 .", "predi": "1 2 5 0 $ < $ 1 2 6 5"}, "mask_dfcb2461-eb40-11ed-bf18-fbcb8b623bb2_14.jpg": {"label": "F { 1 7 } { 2 0 } + F { 1 } { 4 } - F { 7 } { 2 0 } = $ F { 3 6 } { 8 0 } = F { 9 } { 2 0 } $", "predi": "F { 1 7 } { 2 0 } + F { 1 } { 4 } + F { 7 } { 2 0 } = $ F { 3 6 } { 8 0 } = F { 9 } { 2 0 } $"}, "mask_d120ddd1-d095-11ed-a34e-810c1e9c0e59_29.jpg": {"label": "四 成 = ( $ 4 0 $ ) %", "predi": "成 成 = ( $ 4 0 $ ) %"}, "mask_5b4c45b1-ebfa-11ed-9a2c-1fe9fa50ec60_6.jpg": {"label": "5 年 = ( $ 6 0 $ ) 月", "predi": "5 秒 = ( $ 6 0 $ ) 万"}, "mask_fe660dd1-c8aa-11ed-81c2-ad6785b79d8f_56.jpg": {"label": "0 1 1 0 1 1", "predi": "0 1 1 1 1 1"}, "mask_05fe6481-d53e-11ed-92e9-a7d3915530df_42.jpg": {"label": "F { 4 0 6 } { 7 8 } = $ F { 1 6 } { 2 9 } $", "predi": "F { 4 8 } { 7 8 } = $ F { 1 6 } { 2 9 } $"}, "mask_ebd00ca1-cc94-11ed-9629-47431946c27d_28.jpg": {"label": "8 8 - 8 - 8 = $ 8 8 $", "predi": "8 8 - 8 + 8 = $ 8 8 $"}, "mask_0d4df151-e2a0-11ed-b324-61eb9c78acea_15.jpg": {"label": "3 5 9 0 ≈ ( 0 . 3 5 9 ) 万", "predi": "3 5 9 0 ≈ ( $ 0 . 3 5 9 $ ) 万"}, "mask_e907be41-d47e-11ed-920b-3ba87a6c5884_20.jpg": {"label": "F { 1 4 } * { 5 } * F { 1 5 } { 2 8 } = $ F { 2 0 0 } { 1 4 0 } $", "predi": "F { 1 4 } { 5 } * F { 1 5 } { 2 8 } = $ F { 2 0 0 } { 1 4 0 } $"}, "mask_db5c6951-c1a3-11ed-9f04-6510225d74fa_19.jpg": {"label": "七 五 : F 2 } ( $ 五 $ ) =", "predi": "米 5 : ( $ 2 $ ) ="}, "mask_f0c0fa61-db32-11ed-a57f-c74a4717fa46_1.jpg": {"label": "3 0 + 4", "predi": "3 0 + 4 -"}, "mask_e54ab4e1-d921-11ed-b912-2334d9f34950_3.jpg": {"label": "F { 1 } { 3 } - F { 5 } { 1 2 } = $ F { 9 } { 1 2 } = F { 3 } { 4 } $", "predi": "F { 1 } { 3 } + F { 5 } { 1 2 } = $ F { 9 } { 1 2 } = F { 3 } { 4 } $"}, "mask_de2bed31-cfbe-11ed-9d32-71be645b584a_44.jpg": {"label": "4 1 1 1 1", "predi": "1 1 1 1 1"}, "mask_4e1b5001-ed9b-11ed-aeeb-1747dd124717_40.jpg": {"label": "6 . 9 5 6 7 ≈ ( $ 6 . 9 6 $ )", "predi": "6 . 9 5 6 7 ≈ ( $ 6 . 9 6 $"}, "mask_d36e8a20-d210-11ed-b0a7-755723efd073_1.jpg": {"label": "6 角 > $ $ ) 米 ( 4 4", "predi": "4 6 角 = ( ) 元 ( $ 9 $ ) 角"}, "mask_e6a39e61-c6bb-11ed-b9a6-d9977d9471dd_14.jpg": {"label": "5 7 8 - ( $ 2 2 4 $ ) = 3 5 4", "predi": "5 7 8 - ( $ 2 4 $ ) = 3 5 4"}, "mask_1f520ae1-cee0-11ed-9835-2789202e11c4_35.jpg": {"label": "1 米 - 8 3 厘 米 = $ 1 7 厘 米 $", "predi": "1 米 - 8 3 厘 米 = $ 1 7 厘 米"}, "mask_4b86f2d1-c254-11ed-9288-b98c2ee2a8ce_44.jpg": {"label": "F { 2 0 0 7 } { 2 0 0 8 } $ < $ F { 2 0 0 8 } { 2 0 0 9 }", "predi": "F { 2 0 0 7 } { 2 0 0 8 } $ < $ F { 2 0 0 9 } { 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0"}, "mask_5a008ce1-eb2b-11ed-8aee-130657d3104a_8.jpg": {"label": "4 0 + 1 0 + 4 0 = $ 9 0 $", "predi": "4 0 + 1 0 + 4 0 = $ 9 6 $"}, "mask_db5c6951-c1a3-11ed-9f04-6510225d74fa_16.jpg": {"label": "七 { . : ( ( $ 7 $ ) 六 5", "predi": "米 顷 : ( $ 7 . $ ) 元"}, "mask_db1f55e1-e248-11ed-998a-6f252a5f8486_37.jpg": {"label": "0 . 0 5 * 1 . 3 = $ 0 $", "predi": "0 . 0 5 * 1 . 3 ="}, "mask_facb43e1-ce89-11ed-8861-4104c479a3ee_28.jpg": {"label": "2 . 6 分 6 9 - 0 0", "predi": "2 . 6 + 4 0 = $ 0 0 6 $"}, "mask_2f0ac751-e9a3-11ed-808d-8da7c920af79_29.jpg": {"label": "F { 1 } { 1 8 0 5", "predi": "F { 1 } { 3 0 4 }"}, "mask_00ab4401-dd11-11ed-b69a-d76908d4f989_25.jpg": {"label": "8 5 4 2 2 2 0 0 = ( $ 8 5 4 2 . 2 2 $ ) 万", "predi": "8 5 4 2 2 2 0 0 = ( $ 8 5 4 . 2 2 $ ) 万"}, "mask_d721f011-e004-11ed-97b4-25072c4ce7ba_38.jpg": {"label": "7 6 c m ^ 3 = ( $ 7 6 $ ) m L", "predi": "7 6 c m ^ 3 = ( $ 7 6 $ ) m L L"}, "mask_f5937791-cedd-11ed-a036-751de6548355_0.jpg": {"label": "9 0 角 9 分 1 元 2 角", "predi": "9 角 9 分 1 元 2 角"}, "mask_fe220661-dde7-11ed-b98d-b121d22f8930_36.jpg": {"label": "1 0 0 9 8 5 3 2 5 8 7 ≈ ( $ 1 0 1 . 0 $ ) 亿", "predi": "1 0 0 9 8 5 3 2 2 8 7 ≈ ( $ 1 0 1 . 0 $ ) 亿"}, "mask_4cc0a160-cbc2-11ed-b0e2-6332b9c024a5_24.jpg": {"label": "F { 1 { 1 } { 1 { 1 8 }", "predi": "F { 3 } { 1 } * F { 1 } { 1 2 } = $ F { 1 } { 8 } $"}, "mask_f8c20e61-e42a-11ed-ba36-751da7486feb_32.jpg": {"label": "1 4 * 1 1 8 6 0", "predi": "1 4 * 1 0 = $ 1 2 8 6 0 $"}, "mask_e42e2d81-e286-11ed-a775-e9793a0ff6fd_35.jpg": {"label": "6 5 4 / 6 = 1 0 9", "predi": "6 5 4 / 6 = $ 1 0 9 $"}, "mask_1a458a41-eb41-11ed-ac4f-5deaca229540_11.jpg": {"label": "8 . 5 b - 1 . 3 b = $ 7 . 2 b $", "predi": "8 . 5 b - 1 . 3 b = $ 7 . 2 6 $"}, "mask_06b9e290-efe8-11ed-8246-5f78d0e8dbed_6.jpg": {"label": "1 2 5 6 k m ^ 2 = $ 1 2 5 6 0 0 $ 顷", "predi": "1 2 5 6 k m ^ 2 = $ 1 2 5 6 0 0 顷 顷"}, "mask_4e562911-ec7d-11ed-a017-a16f8243b981_34.jpg": {"label": "1 0 1 0 1 * 1 1 = $ 1 1 1 1 1 1 $", "predi": "1 0 1 0 1 * 1 1 = 1 1 1 1 1 1"}, "mask_1c732121-d3b2-11ed-a0e0-c74d15aada3e_4.jpg": {"label": "F { 3 } { 4 } * x = F { 5 } { 1 2 } * 2 4", "predi": "F { 3 } { 4 } x x = F { 5 } { 1 2 } * 2 4"}, "mask_d98def11-efff-11ed-9f42-75065af1c30f_23.jpg": {"label": "F { 2 0 0 7 } { 2 0 0 8 } $ = $ F { 2 0 0 8 } { 2 0 0 9 }", "predi": "F { 2 0 0 7 } { 2 0 0 8 } $ = $ F { 2 0 0 8 } { 2 0 0 9 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8"}, "mask_5cca42f1-e643-11ed-830d-cbe284fabe89_12.jpg": {"label": "a * 5 * b = $ 5 a b $", "predi": "a * 5 * b = $ 5 a b b $"}, "mask_ea0f6361-df78-11ed-9638-63bc230c3369_43.jpg": {"label": "F { 1 } { 5 } * F { 1", "predi": "F { 1 } { 1 } { 1 = $ F { 2 0 } { 6 0 }"}, "mask_5a85e8a1-e25b-11ed-8952-2b8b26b1d9ef_48.jpg": {"label": "5 4 / 7 = $ 7 $", "predi": "5 4 / 7 = 7"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_36.jpg": {"label": "4 0 1 1 1 9 1 1", "predi": "4 9 4 1 1 1 1 1 1 1"}, "mask_e1f36c21-c17e-11ed-9679-d1df5fedaa3c_46.jpg": {"label": "5 - 5 3", "predi": "5 - 5 . 5"}, "mask_2ecb1421-ee62-11ed-93cc-db66e90ada16_3.jpg": {"label": "F { 5 } { 9 } + F { 1 } { 1 8 } = $ F { 1 0 } { 1 8 } = F { 1 1 } { 1 8 } $", "predi": "F { 5 } { 9 } + F { 1 } { 1 8 } = $ F { 1 0 } { 1 8 } + F { 1 1 } { 1 8 } $"}, "mask_e1c2cb71-cf01-11ed-90ca-73f9290a83b4_6.jpg": {"label": "5 = ( $ 6 0 0 1 ) 0 $ )", "predi": "5 = ( $ 6 0 0 m $ )"}, "mask_5f851271-e88e-11ed-89ee-eda97d825ab7_69.jpg": {"label": "4 0 + 2 8 = $ 7 0 $", "predi": "4 2 + 2 8 = $ 7 0 $"}, "mask_1ab1d2f1-cf9d-11ed-b0e2-15cbdd81d5fa_35.jpg": {"label": "8 5 4 2 2 2 0 0 = ( $ 8 5 4 2 . 2 2 $ ) 万", "predi": "8 5 4 2 2 2 0 0 = ( $ 8 5 . 2 2 2 2 $ ) 万"}, "mask_f7fe2b21-d792-11ed-b143-61d69488bac9_2.jpg": {"label": "$ 7 0 $ ) 分 = 7 角", "predi": "( $ 7 0 $ ) 分 = 7 角"}, "mask_d466ceb1-d864-11ed-8a76-eb6e23274aaf_45.jpg": {"label": "$ 4 - 4 $", "predi": "4 - 4 $ 4 $"}, "mask_2c089050-d93b-11ed-a735-257ab80c06bd_23.jpg": {"label": "1 2 - 5 + 6 =", "predi": "1 2 - 5 + 6 = $ 1 $"}, "mask_f3d6b551-c57a-11ed-8ee3-edfea97889d2_14.jpg": {"label": "5 * 7 = $ 3 $", "predi": "5 * 7 = $ 3 5 $"}, "mask_fe80c750-eb10-11ed-afef-850259460a0f_0.jpg": {"label": "F { 1 9 } { 5 0 } - F { 8 } { 5 0 } = $ F { 1 1 } 5 0 } $", "predi": "F { 1 9 } { 5 0 } - F { 8 } { 5 0 } = $ F { 1 1 } { 5 0 0 } $"}, "mask_eca0bf21-d6d1-11ed-a57c-5ba9d58fc7c9_8.jpg": {"label": "3 1 4 c m ^ 3 = ( $ 0 . 3 1 4 $ ) d m ^ 3", "predi": "3 1 4 c m ^ 3 = ( $ 0 . 3 1 4 $ ) d m ^ 3 d 3"}, "mask_6cce8d41-e1cb-11ed-a777-1d170e0ce03a_1.jpg": {"label": "F { 2 5 } { 5 } = $ 5 . 2 $", "predi": "F { 2 6 } { 5 } = $ 5 . 2 $"}, "mask_fc8e0ca1-e4fd-11ed-aa8c-395f46862edf_16.jpg": {"label": "平 年 一 年 = ( $ 3 6 5 $ ) 天", "predi": "4 角 角 = ( $ 3 6 5 $ ) 万"}, "mask_db5c6951-c1a3-11ed-9f04-6510225d74fa_25.jpg": {"label": "七 八 : ( $ $ ) 五 一", "predi": "米 顷 : ( $ = $ ) 万 -"}, "mask_d6221981-efed-11ed-a351-b3d5861e6764_19.jpg": {"label": "a ) * 1 6", "predi": "( ) * 1 6"}, "mask_feb61041-df6c-11ed-94e8-f3caf705334f_10.jpg": {"label": "$ = 1 0 $", "predi": "= $ 1 0 $"}, "mask_e9a8b181-c25c-11ed-a7a6-29351b22d0bb_9.jpg": {"label": "5 6 5 / 7 ≈ 8 0", "predi": "5 6 5 / 7 ≈ $ 8 0 $"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_37.jpg": {"label": "0 . 9 d m ^ 2 = ( 0 . 0 0 9 ) m ^ 2", "predi": "0 . 9 d m ^ 2 = ( $ 0 . 0 0 9 $ ) m ^ 2"}, "mask_6a21c811-d3ba-11ed-8ec0-adc48813d352_25.jpg": {"label": "1 1 1 0 5 角 4 3 4", "predi": "1 1 0 * 6 5 * 4 ) = 7"}, "mask_fdd803c1-e6a8-11ed-8d6e-1578250edc0a_22.jpg": {"label": "F { 1 } { 5 } + F { 1 } { 6 } = $ F { } { 2 0 } $", "predi": "F { 1 } { 5 } + F { 1 } { 6 } = $ F { 6 1 } { 3 0 } $"}, "mask_3a56f290-cbd6-11ed-b2cd-0d6545e75019_8.jpg": {"label": "8 1 - 0 . 0 6 = $ 8 . 0 4 $", "predi": "8 . 1 - 0 . 0 6 = $ 8 . 0 4 $"}, "mask_d20e9471-c3f8-11ed-8d96-657a92ba30fb_5.jpg": {"label": "5 0 0 c m ^ 3 = ( $ 5 0 $ ) m L", "predi": "5 0 c m ^ 3 = ( $ 5 0 $ ) m L"}, "mask_fbca5771-e1dc-11ed-948e-4be3080be794_15.jpg": {"label": "$ 8 $ ) * 7 < 6 0", "predi": "( $ 8 $ ) * 7 < 6 0"}, "mask_4e3c8f11-deb2-11ed-ab22-37d936f4e395_18.jpg": {"label": "$ 7 $ ) * 3 < 2 3", "predi": "( $ 7 $ ) * 3 < 2 3"}, "mask_1c7324d1-ed53-11ed-918b-398e7122eb1f_26.jpg": {"label": "1 % = ( $ F { 1 } { 1 0 0 $ } )", "predi": "1 % = ( $ F { 1 } { 1 0 0 } $ )"}, "mask_e0560aa1-d789-11ed-bbdd-915603bb572f_4.jpg": {"label": "3 . 6 0 c m ^ 2 = ( $ 0 . 0 3 6 $ ) d m ^ 2", "predi": "3 . 6 c m ^ 2 = ( $ 0 . 0 3 6 $ ) d m ^ 2"}, "mask_1d900771-d813-11ed-aa63-6f481826652e_42.jpg": {"label": "2 7 0 * 2 = $ 5 4 0 0 $", "predi": "2 7 0 * 2 0 = $ 5 4 0 0 $"}, "mask_4cc0a160-cbc2-11ed-b0e2-6332b9c024a5_40.jpg": {"label": "F { 1 1 } { 1 5 } * F { 1 3 } { 1 4 } * F { 1 5 } { 2 6 } = $ F { 1 } { 2 } $", "predi": "F { 1 1 } { 1 5 } * F { 1 3 } { 1 4 } * F { 1 5 } { 2 6 } = $ F { 1 } { 2 } { 2 1 } { 2 1 } { 2 1 } { 2 1 } { F { 1 } { } { } { 1 1 } { 1 1 } {"}, "mask_3cb30871-eb3a-11ed-a4a0-610838f4f7b2_2.jpg": {"label": "1 6 + 3 9 $ > $ 6 3 - 2", "predi": "1 6 + 3 9 $ > $ 6 3 - 3"}, "mask_fc18d331-ed2d-11ed-b8ce-ff2a340f5f5a_41.jpg": {"label": "八 折 = ( $ 8 0 $ ) %", "predi": "公 成 = ( $ 8 0 $ ) %"}, "mask_fa50ad01-ed93-11ed-8914-d90ff35af95b_17.jpg": {"label": ". 2 8 * 1 0 0 = $ 6 2 8 $", "predi": "0 . 2 8 * 1 0 0 = $ 6 2 8 $"}, "mask_faeec271-ddd4-11ed-ac93-7f3d98aaa450_6.jpg": {"label": "F { 6 } { 7 } - F { 1 } { 3 } = $ F { 6 * 2 } { 7 * 3 } - F { 1 * 7 } { 3 * 7 } = F { 1 1 } { 2 1 } $", "predi": "F { 6 } { 7 } - F { 1 } { 3 } = $ F { 6 * 3 } { 7 * 3 } - F { 1 * 7 } { 3 * 7 } = F { 1 1 } { 2 1 } $"}, "mask_fee3b991-ed5a-11ed-aaf6-89338b64c756_12.jpg": {"label": "F { 4 } { 9 } = F { 4 * ( ) } { 9 * ( ) } = F { ( } { ( }", "predi": "F { 4 } { 9 } = F { 4 * ( ) } { 9 * ( ) } = F { ( } { ( ) }"}, "mask_d25d2de1-d926-11ed-a78a-7d0fadc38d20_23.jpg": {"label": "{ 2 5 } ( { 1 0", "predi": "F { 2 } { 4 } = $ 1 0 $"}, "mask_5c738711-e438-11ed-8a55-7b0e95c04ea5_20.jpg": {"label": "3 m l = ( $ 0 . 0 0 3 $ ) L", "predi": "3 m L = ( $ 0 . 0 0 3 $ ) L"}, "mask_ec4ee6c1-ee58-11ed-965e-3f1524b0ca84_21.jpg": {"label": "$ 2 $ ) + 5 = 1 7", "predi": "( $ 2 $ ) + 5 = 1 7"}, "mask_ed61e2d1-bf44-11ed-a345-ed3f6b44010c_7.jpg": {"label": "3 5 + 1 4 / 7 = $ 7 $", "predi": "$ 3 5 + 1 4 / 7 = 7 $"}, "mask_dba87f51-d811-11ed-8d5f-a117d81ad554_46.jpg": {"label": "2 1 4 - 1", "predi": ". 1 4 - 1"}, "mask_f9c15a01-e0c9-11ed-b5a8-fd85e9fce9a0_44.jpg": {"label": "0 . 8 7 5 = F { ( $ 1 7 5 $ ) } { ( $ 2 4 0 $ ) }", "predi": "0 . 8 7 5 = F { ( $ 1 7 5 5 $ ) } { ( $ 2 4 0 $ ) }"}, "mask_e0268f11-d47c-11ed-8d4e-63ad0fd8d277_15.jpg": {"label": "F { 3 2 } 9 = $ 8 $", "predi": "F { 1 7 - 9 = $ 8 $"}, "mask_0ce2b3e1-dc33-11ed-91ec-3b6e94231feb_14.jpg": {"label": "3 8 + 7 分 = ( $ 1 $ ) 角", "predi": "3 分 + 7 分 = ( $ 1 $ ) 角"}, "mask_f7109ac1-dd0c-11ed-b704-e77afdf7f94f_43.jpg": {"label": "1 1 / 3 = $ 5 P $", "predi": "1 1 / 3 = $ 5 P 1 $"}, "mask_e6a39e61-c6bb-11ed-b9a6-d9977d9471dd_19.jpg": {"label": "( $ 7 5 2 $ ) + 1 2 3 = 8 7 5", "predi": "$ 7 5 2 $ ) + 1 2 3 = 8 7 5"}, "mask_f12f40c0-e0bd-11ed-acd8-c135e0cd48a5_29.jpg": {"label": "1 角 3 分 $ < $ 2 角 1", "predi": "1 角 3 分 $ < $ 2 角 1 分"}, "mask_5a86a341-da08-11ed-a715-9b26f126788e_0.jpg": {"label": "5 个 月 = ( $ F { 5 } { 1 2 } $ ) 年", "predi": "5 公 顷 = ( $ F { 5 } { 1 2 } $ ) 千"}, "mask_4e1b5001-ed9b-11ed-aeeb-1747dd124717_28.jpg": {"label": "F { 1 } { 8 } ≈ ( $ 0 . 1 $ )", "predi": "F { 1 } { 8 } ≈ ( $ 0 . 1 $"}, "mask_4f6d35b1-e69b-11ed-a49f-e5fd73654b31_17.jpg": {"label": "3 . 2 : 0 . 8 = $ 4 . 1 $", "predi": "3 . 2 : 0 . 8 = $ 4 : 1 $"}, "mask_6a9f9da1-ef24-11ed-a9a8-a5c5140488dd_78.jpg": {"label": "F { 4 } { 4 5 0 = $ 6 $", "predi": "F { 4 6 + 5 0 = $ 9 1 7 0 $"}, "mask_d01662a1-d47a-11ed-b8e7-7d40b1959cd5_6.jpg": {"label": "$ 3 5 $", "predi": "$ 3 9 $"}, "mask_d24cc831-d9ec-11ed-a484-1b963266065c_1.jpg": {"label": "1 = ( $ 1 0 5 $ ) d m", "predi": "1 = ( $ 1 0 5 $ ) d m ^ 3"}, "mask_fd3f9d21-c181-11ed-ac0c-bb89e4e0f5a3_0.jpg": {"label": "三 4 0 = $ 6 $", "predi": "5 4 0 = $ 6 $"}, "mask_dd4e6461-effd-11ed-9591-89cf6883e6d6_35.jpg": {"label": "/ 3 = $ 2 P 2 0 $ ) + 2 8 0 = 1 0", "predi": "1 6 / 3 = $ 5 P 7 2 0 $ ) + 2 8 0 = 1 0 0 0"}, "mask_2ac03b11-c722-11ed-8156-f3e9f4fd72cc_32.jpg": {"label": "$ 2 6 - 2 0 $ = $ 6 $ ( $ 只 $ )", "predi": "$ 2 6 - 2 0 $ = $ 6 $ ( $ 0 $ )"}, "mask_4d3d6441-c337-11ed-ad12-879f3e6234a2_39.jpg": {"label": "1 5 - ( $ 9 $ )", "predi": "1 5 - ( $ 9 $ ) :"}, "mask_90f3ddd1-c329-11ed-a6c3-5781877ada00_27.jpg": {"label": "1 2 + 6 $ > $ 1 2 - 6", "predi": "1 2 + 6 $ > $ 1 2 -"}, "mask_417ae041-e846-11ed-9602-9963544701c9_29.jpg": {"label": "5 7 + 9 - 3 = $ 4 5 $", "predi": "5 7 - 9 - 3 = $ 4 5 $"}, "mask_2c8ad3b1-c8a8-11ed-bf9c-9bac90c71228_5.jpg": {"label": "3 1 4 * 0 . 2 ^ 2 * 5 0 = $ 6 . 2 8 $", "predi": "3 . 1 4 * 0 . 2 ^ 2 * 5 0 = $ 6 . 2 8 $"}, "mask_a257c8d1-d2e1-11ed-b526-2fae7ac04954_3.jpg": {"label": "+ 6 9 0 =", "predi": "4 0 + 9 0 ="}, "mask_a8661011-dea3-11ed-b409-2ff7d2e88091_25.jpg": {"label": "7 . 5 3 ≈ 8", "predi": "7 . 5 3 ≈ $ 8 $"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_46.jpg": {"label": "9 4 $ > $ > + 6", "predi": "9 4 $ > $ + + 6"}, "mask_2e921f31-e7d2-11ed-92b3-c99ebdbf361f_27.jpg": {"label": "F { 2 } { 5 } / F { 5 }", "predi": "F { 2 } { 5 } / F { 8 } { 1 5 } ="}, "mask_2a1f0201-eff3-11ed-aa3f-57649601d057_4.jpg": {"label": "F { 4 } { 3 } - F { 5 } { 6 } = $ F { 8 } { 6 } - F { 5 } { 6 } = F { 1 } { 2 } $", "predi": "F { 4 } { 3 } - F { 5 } { 6 } = $ F { 8 } { 6 } - F { 5 } { 6 } = F { 1 } { 2 } { 2 } { 2 1 } { 2 1"}, "mask_eaf22021-c8ac-11ed-8af2-779d63ef86eb_12.jpg": {"label": "1 4 8 6 $ > $ 1 5 8 7", "predi": "1 4 8 6 $ = $ 1 5 8 7"}, "mask_3da010b1-dc09-11ed-9217-9fd681f6bdc1_2.jpg": {"label": "( F { 7 } { 8 } - F { 1 } { 4 } ) * ( F { 5 } { 1 2 } - F { 1 } { 4 }", "predi": "( F { 7 } { 8 } - F { 1 } { 4 } ) * ( F { 5 } { 1 2 } - F { 1 } { 4 } )"}, "mask_d7578300-d3a3-11ed-ae91-eb6c9f34fa4b_4.jpg": {"label": "x = $ 3 0 0 0 0 $", "predi": "x = $ 3 0 0 0 0 0 $"}, "mask_0f7400c1-d78b-11ed-b45d-f7c534bbf4df_41.jpg": {"label": "1 4 - 7 $ 9 $ 1 2 2 6", "predi": "1 4 - 7 $ > $ 1 2 - 6"}, "mask_1f22abb1-cd5a-11ed-a23f-7d30b30b103a_33.jpg": {"label": "2 m : 5 m = ( ) : 1", "predi": "2 m : 5 m m = ( ) : 1"}, "mask_dfc40281-db68-11ed-8612-758c44062f71_37.jpg": {"label": "2 $ 厘 = 6 $ ) $ $ 角", "predi": "2 角 = 6 $ 6 $ ) 元 ( $ 2 $"}, "mask_fac6cd71-d1b2-11ed-a7e7-a16bb42ce68e_24.jpg": {"label": "F { 7 } { 3 } * F { 7 } { 3 ) =", "predi": "F { 7 } { 3 } * F { 7 } { 3 } ="}, "mask_eca0bf21-d6d1-11ed-a57c-5ba9d58fc7c9_28.jpg": {"label": "1 2 d m ^ 3 = ( $ 0 . 0 1 2 $ ) m ^ 3", "predi": "1 2 d d ^ 3 = ( $ 0 . 0 1 2 $ ) m ^ 3"}, "mask_1bd993e1-db3f-11ed-b550-8346d1d164b0_24.jpg": {"label": "1 2 0 ° + 1 6 0 ° =", "predi": "2 0 0 ° + 1 6 0 ° ="}, "mask_e7dcf541-c659-11ed-a5f4-69b0e68cef5f_12.jpg": {"label": "F { 1 1 } { 1 0 } - F { 1 7 } { 3 0 } = $ F { 1 } { 2 } $", "predi": "F { 1 1 } { 1 0 } - F { 1 7 } { 3 0 } = $ F { 1 } { } $"}, "mask_fe665720-eb34-11ed-876c-956317a0e3d6_25.jpg": {"label": "5 m ^ 6 d m ^ 2 = ( $ 5 . 6 $ ) m ^ 2", "predi": "5 m ^ 2 6 d m ^ 2 = ( $ 5 . 6 $ ) m ^ 2"}, "mask_0f869931-d04a-11ed-ac00-29cfd11ee7e5_6.jpg": {"label": "7 / 1 8 = F { ( $ 7 $ ) } { $ 1 8 $ ) }", "predi": "7 / 1 8 = F { ( $ 7 $ ) } { ( $ 1 8 $ ) }"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_21.jpg": {"label": "F { 7 } { 8 } - F { 3 } { 4 } = $ F { 1 4 } { 1 6 } - F { 1 2 } { 1 6 } = F { 1 4 - 1 2 } { 1 6 } = F { 1 } { 8 } $", "predi": "F { 7 } { 8 } - F { 3 } { 4 } = $ F { 1 4 } { 1 6 } - F { 1 2 } { 1 6 } = F { 1 4 + 1 2 } { 1 6 } = F { 1 } { 8 } $"}, "mask_4d9e94b1-ddde-11ed-a8ae-f7ba1c3f5d5f_13.jpg": {"label": "3 0 - 5 $ = $ 5 0 - 2 0", "predi": "3 5 - 5 $ = $ 5 0 - 2 0"}, "mask_0fc252d1-c38a-11ed-ae73-8d7e12799d4b_34.jpg": {"label": "6 0 0 - 2 0 0 ) * 2 . 5 % = $ 1 0 $", "predi": "( 6 0 0 - 2 0 0 ) * 2 . 5 % = $ 1 0 $"}, "mask_1c0d6441-cbcc-11ed-821d-a9dd0e75b623_2.jpg": {"label": "5 - 0 . 9 3 - 0 . 0 7 = 5 - ( $ 0 . 9 3 $ + $ - 0 . 0 7 $ ) = $ 4 . 9 9 $", "predi": "5 - 0 . 9 3 - 0 . 0 7 = 5 - ( $ 0 . 9 9 $ + $ - 0 . 0 7 $ ) = $ 4 . 9 9 $"}, "mask_fd4f1eb0-dd26-11ed-86e7-e34991cc4897_2.jpg": {"label": "8 0 . 4 = $ 0 . 3 2 $", "predi": "* 0 . 4 = $ 0 . 3 2 $"}, "mask_d2059ba0-e003-11ed-a8a9-216faef1c6fa_14.jpg": {"label": "( $ 七 1 ) $ ) 五 十 六", "predi": "( $ 米 1 1 $ ) 千 + 米"}, "mask_ed61e2d1-bf44-11ed-a345-ed3f6b44010c_10.jpg": {"label": "6 5 - 3 5 / 5 = $ 6 $", "predi": "6 5 - 3 5 / 5 = 6"}, "mask_1bf50501-df7a-11ed-bac7-fb2f45b6f854_35.jpg": {"label": "F { 8 } { 1 8 } $ = $ F { 8 } { 1 6 }", "predi": "F { 8 } { 1 5 } $ = $ F { 8 } { 1 6 }"}, "mask_ea10d491-e0d9-11ed-aef6-7d98183c5e83_28.jpg": {"label": "六 成 五 = ( $ 6 5 $ %", "predi": "公 成 五 = ( $ 6 5 . % $"}, "mask_d20e9471-c3f8-11ed-8d96-657a92ba30fb_0.jpg": {"label": "$ 2 5 - 1 0 = 1 5 ( c m ) $", "predi": "$ 2 5 - 1 0 = 1 5 ( m m $"}, "mask_da65e7d1-e433-11ed-a4ec-d78f5557c6a6_4.jpg": {"label": "F { 5 5 } { 2 5 } = $ { 5 5 } { 2 5 } = F { } { 5 } $", "predi": "F { 5 5 } { 2 5 } = $ F { 5 5 } { 2 5 } = F { } { 5 } $"}, "mask_1d781620-d925-11ed-b182-dda4e9f1aaa3_15.jpg": {"label": "7 . 1 5 / 1 0 = $ 0 . 7 5 $", "predi": "7 . 1 5 / 1 0 = $ 0 . 7 1 5 $"}, "mask_d69e4070-deac-11ed-af3b-d19d1131ae48_5.jpg": {"label": "2 . 5 : 6 = ( $ F { 2 } { 1 } $ ) .", "predi": "2 . 5 : 6 = ( $ F { } { } $ ) ."}, "mask_1e994951-ecc8-11ed-83a4-f954b980b063_40.jpg": {"label": "4 8 * 2 0 = $ 9 6 0 $", "predi": "4 8 * 2 0 = $ 9 6 $"}, "mask_f5039341-dd39-11ed-b5b6-91df6f4f3ca1_18.jpg": {"label": "2 2 0 ° - 2 0 ° = $ 2 ° $", "predi": "2 2 ° - 2 0 ° = $ 2 ° $"}, "mask_f67793d1-d3b0-11ed-902a-fb4c93e5ba0a_43.jpg": {"label": "F { 4 } { 3 } - F { 1 2 } { 1 1 } + F { 5 } { 4 } = $ F { 1 7 } { 3 } $", "predi": "F { 4 } { 3 } - F { 1 0 } { 1 1 } + F { 1 } { 1 1 } = $ F { 1 7 } { 3 3 } $"}, "mask_9e321fa1-cede-11ed-85a4-cf1cd11915cb_34.jpg": {"label": "1 * x * y = $ x y $", "predi": "1 * x * a = $ 3 9 $"}, "mask_4baed921-d9ed-11ed-b770-f1a26b9fd758_33.jpg": {"label": "6 2 / 7 = $ 9 P 1 ( $ *", "predi": "6 2 / 7 = 9 P 1 ( $ * $ )"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_6.jpg": {"label": "1 1 4 $ < $ 6 + 9", "predi": "1 4 $ < $ 6 + 9"}, "mask_9e321fa1-cede-11ed-85a4-cf1cd11915cb_1.jpg": {"label": "a . a - b . b = $ a ^ 2 - b ^ 2 $", "predi": "a . a - b . b = a ^ 2 - 6 ^ 2"}, "mask_c7183621-c8b2-11ed-8c68-d1b4066848bf_29.jpg": {"label": "1 0 0 / F { 3 } { 4 } = $ F { 4 0 0 } { 3 } $", "predi": "1 0 0 / F { 3 } { 4 } = $ F { 4 0 0 0 } { 3 } $"}, "mask_9e321fa1-cede-11ed-85a4-cf1cd11915cb_2.jpg": {"label": "4 . 7 * y = $ 4 . 7 y $", "predi": "4 . 7 * a = $ 4 . 7 9 $"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_25.jpg": {"label": "5 1 = 4 + 8 9", "predi": "5 1 = 4 + 8 角"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_22.jpg": {"label": "7 - 6 $ - $ 1 1", "predi": "7 = 6 $ - $ 1 1"}, "mask_2c8ad3b1-c8a8-11ed-bf9c-9bac90c71228_34.jpg": {"label": "7 2 * 5 = $ 2 4 5 $", "predi": "7 ^ 2 * 5 = $ 2 4 5 $"}, "mask_3f336dd0-cfc7-11ed-a41a-15ee767741cd_24.jpg": {"label": "4 1 6 - 9 + 6 - 6 °", "predi": "4 1 5 - 9 + 6 - 6 °"}, "mask_c31b7081-df70-11ed-94dc-0b2b1560dcb5_19.jpg": {"label": "$ F 7 } $", "predi": "$ F { 7 } { $"}, "mask_da1ed751-c895-11ed-b3d9-7bc3a2f7277e_5.jpg": {"label": "F { 7 } { 1 2 } $ < $ { 5 } { 7 }", "predi": "F { 7 } { 1 2 } $ < $ F { 5 } { 7 }"}, "mask_d11b7f41-ce27-11ed-a454-97f6756d8d1e_9.jpg": {"label": "3 5 - 2 3 + 1 8 = $ 3 0 $", "predi": "- 2 3 + 1 8 = $ 3 0 $"}, "mask_209a1e11-d85e-11ed-adbd-6d6d42a66bf5_29.jpg": {"label": "3 8 + 4 - 3 0 = $ 4 $", "predi": "3 0 + 4 - 3 0 = $ 4 $"}, "mask_da1ed751-c895-11ed-b3d9-7bc3a2f7277e_16.jpg": {"label": "F { 5 } { 7 } $ < $ F { 3 } { 4 }", "predi": "F { 5 } { 1 2 } $ < $ F { 3 } { 4 }"}, "mask_efb6d9c1-ef30-11ed-b984-f34b7720ac33_3.jpg": {"label": "6 5 * { 6 } { 5 } = $ 7 8 $", "predi": "6 5 * F { 6 } { 5 } = $ 7 8 $"}, "mask_bf758eb1-e89f-11ed-b47a-177cc8d1e3e2_23.jpg": {"label": "F { 1 } { 3 } / F { 1 } { 8 } = $ { 8 } { 3 } $", "predi": "F { 1 } { 3 } / F { 1 } { 8 } = $ F { 8 } { 3 } $"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_30.jpg": {"label": "1 1 - 2 + 6 角", "predi": "1 1 = 2 + 6 角"}, "mask_dac76e31-d9be-11ed-b0e0-459043f77d4a_3.jpg": {"label": "1 - F { 7 } { 1 0 } = $ F { 3 } { 1 0 } $", "predi": "1 - F { 7 } { 1 0 } = $ F { 3 } { 1 0 0 } $"}, "mask_e4f569e1-e880-11ed-8240-c907d23284e5_11.jpg": {"label": "6 0 0 万 - 4 1 0 万 = $ 1 9 0 万 $", "predi": "6 0 0 万 - 4 1 0 万 = $ 1 9 0 万"}, "mask_8b4c1480-ee65-11ed-a0ac-d5695dcde8d4_41.jpg": {"label": "5 - ( 9 - ) = $ 3 7 $", "predi": "5 - ( 9 - 3 ) = $ 5 7 $"}, "mask_67bac381-d5c5-11ed-a657-99f4e9086bc8_42.jpg": {"label": "8 6 - ( 3 3 + 3 3 ) = $ 2 0 $", "predi": "8 6 - ( 3 3 + 3 3 ) = $ 2 $"}, "mask_de1f9331-cc73-11ed-935e-b95740ebfb6e_0.jpg": {"label": "$ 4 + 8 $ = $ 1 2 $ ( 个 )", "predi": "$ 4 $ + $ 8 $ = $ 1 2 $ ( 分 )"}, "mask_ecf7db11-ca34-11ed-843c-01c4cd6400ad_11.jpg": {"label": "* 5 = 5", "predi": "9 = 2 2 $ > $"}, "mask_310e4711-eb25-11ed-8ba5-1f9ed43d52ac_26.jpg": {"label": "1 = 6 $ < $ 6", "predi": "1 = 6 $ - $ 6"}, "mask_f9f15e11-e8f5-11ed-8a72-336513e714d8_15.jpg": {"label": "6 = - 2 - 4 4", "predi": "6 = 2 - 4 4"}, "mask_436df7c0-ea35-11ed-b39d-7d59e631ca90_13.jpg": {"label": "6 5 = $ 2 3 $", "predi": "5 5 = $ 2 3 $"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_29.jpg": {"label": "1 1 - 6 + 8 °", "predi": "1 1 = 6 + 8"}, "mask_a2d9c191-ea70-11ed-9013-370bd1dfabab_37.jpg": {"label": "4 7 + 2 8 = $ 7 5 $", "predi": "4 7 + 2 8 = $ 7 . 5 $"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_10.jpg": {"label": "6 $ $ 4 + 9", "predi": "8 4 $ > $ 4 + 9"}, "mask_8311d291-d9fd-11ed-9507-f55f4b659375_28.jpg": {"label": "2 4 8 + 4 8 9 - 4 3 1 = $ 3 0 $", "predi": "2 4 9 + 4 8 9 - 4 3 1 = $ 3 0 7 $"}, "mask_f1b84f01-eff4-11ed-ae39-e11c2f0005b3_38.jpg": {"label": "3 0 + 6 - 7 = $ 2 0 $", "predi": "3 0 + 6 - 7 = $ 2 9 $"}, "mask_87092361-c262-11ed-80bd-fde4dded76b5_24.jpg": {"label": "F { 6 } {", "predi": "F { 3 } {"}, "mask_436df7c0-ea35-11ed-b39d-7d59e631ca90_12.jpg": {"label": "$ F 7 $", "predi": "$ 4 7 $"}, "mask_88f7bd81-bfb0-11ed-9c69-6730a93f2b9d_34.jpg": {"label": "8 4 ( $ $ ) 9 1", "predi": "8 + ( $ 8 $ ) = 1"}, "mask_88f7bd81-bfb0-11ed-9c69-6730a93f2b9d_33.jpg": {"label": "9 1 ( $ 7 $ ) 1", "predi": "7 + ( $ 7 $ ) 1"}, "mask_eed8c811-ef2b-11ed-8912-5bbadea3f215_5.jpg": {"label": "F { 3 } { 5 } - F { 1 } { 2 0 } = $ F { 1 1 } { 2 2 } $", "predi": "F { 3 } { 5 } - F { 1 } { 2 0 } = $ F { 1 1 1 } { 2 2 } $"}, "mask_7ef42c60-bfc4-11ed-8d6a-1304f93d1dd9_22.jpg": {"label": "9 0 - 1 5 = $ 7 4 $", "predi": "9 0 - 1 6 = $ 7 4 $"}, "mask_f8d484a0-cab2-11ed-8d8a-4dd3c8bd8206_17.jpg": {"label": "F { 9 } { 1 0 } - F { 2 } { 5 } = $ f { 1 } { 2 } $", "predi": "F { 9 } { 1 0 } - F { 2 } { 5 } = $ F { 1 } { 2 } $"}, "mask_a6752a61-ce2d-11ed-b168-d97fbc0b6edf_42.jpg": {"label": "0 . 3 2 1 = $ 0 . 3 $ + $ 0 . 0 2 $ + $ 0 . 0 0 1 $", "predi": "0 . 3 2 2 = $ 0 . 3 $ + $ 0 . 0 2 $ + $ 0 . 0 0 1 $"}, "mask_3cac4601-d462-11ed-8487-390e95443872_20.jpg": {"label": "1 0 . 0 9 9", "predi": "1 0 . 0 9 9 ≈"}, "mask_4beefb40-e997-11ed-8fa6-eb952426256e_12.jpg": {"label": "8 0 + 3 6 0 = $ 8 4 0 $", "predi": "0 + 3 6 0 = $ 8 4 0 $"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_7.jpg": {"label": "2 = 8 + 6 9", "predi": "2 = 8 + 6 9 0"}, "mask_90f3ddd1-c329-11ed-a6c3-5781877ada00_36.jpg": {"label": "1 : 2 6 1", "predi": "1 = $ < $ 6 1"}, "mask_b63b7281-c32c-11ed-8d70-b9addc095558_18.jpg": {"label": "2 1 - 8 + 1 8", "predi": "2 1 = 8 + 4 8"}, "mask_ecf7db11-ca34-11ed-843c-01c4cd6400ad_37.jpg": {"label": "8 5 * 5 = $ 1 1 7 $", "predi": "5 8 5 / 5 = $ 1 1 7 $"}, "mask_8feeeb21-de6b-11ed-8646-33765f8ba913_45.jpg": {"label": "0 . 3 5 = $ { 3 5 } { 1 0 0 } = F { 7 } { 2 0 } $", "predi": "0 . 3 5 = $ F { 3 5 } { 1 0 0 } = F { 7 } { 2 0 } $"}, "mask_36d2c680-d85b-11ed-b61e-ff9d3ca67088_11.jpg": {"label": "1 0 0 0 + 8 =", "predi": "1 0 0 0 / 8 ="}, "mask_dab20130-e9b3-11ed-a5e2-db36ea36819f_0.jpg": {"label": "1 . 6 * 5 0 = $ 0 . 8 * ( $ )", "predi": "1 . 6 * 5 0 = 0 . 8 * ( )"}, "mask_85161d41-e712-11ed-a1f9-97d8589ef508_43.jpg": {"label": "6 1 0 = ( $ 6 0 0 $ ) + ( $ 4 0 $ )", "predi": "6 4 0 = ( $ 6 0 0 $ ) + ( $ 4 0 $ )"}, "mask_1567eb11-dd1f-11ed-a743-c188beb2e55e_28.jpg": {"label": "2 5 * 4 = $ 9 2 $", "predi": "2 3 * 4 = $ 9 2 $"}, "mask_d7220081-d787-11ed-9f08-b1d4a418aed0_6.jpg": {"label": "3 7 + 4 = $ 4 1 $", "predi": "7 7 + 4 = $ 4 1 $"}, "mask_76f97c21-eda2-11ed-b374-970372aa7a9a_9.jpg": {"label": "9 8 - $ 7 0 $ - 1 0 = $ 1 8 $", "predi": "9 8 - 7 0 - 1 0 = $ 1 8 $"}, "mask_aefa8c91-d222-11ed-b631-754de6a212c2_14.jpg": {"label": "8 元 = ( ) 0 ^ 2", "predi": "8 元 = ( ) c m ^ 2"}, "mask_db6b1f01-e6f5-11ed-b073-ebfdf9150c37_45.jpg": {"label": "6 - 6 = $ 2 $", "predi": "- 6 - 6 = $ 2 $"}, "mask_efb0fa51-d379-11ed-9280-c3b565066808_32.jpg": {"label": "{ F } { 5 } 4 }", "predi": "F { 4 } { 8 } = F { } { ( ) }"}, "mask_f2886071-e705-11ed-924f-656b34a8ea53_9.jpg": {"label": "6 6 0 / 6 0 = $ 1 7 $", "predi": "6 6 0 / 6 0 = $ 1 1 $"}, "mask_f6f91f41-ca41-11ed-8d06-e556687a7972_40.jpg": {"label": "0 . . 9 2 * 0 . 4 = $ 0 . 3 6 6 $", "predi": "0 . 9 2 * 0 . 4 = $ 0 . 3 6 6 $"}, "mask_6c142741-d530-11ed-8503-d5d7f0fee5d1_3.jpg": {"label": "1 0 0 * 0 . 8 9 9 / 1 0 = $ 8 . 9 0 $", "predi": "1 0 0 * 0 . 8 9 9 / 1 0 = $ 8 . 9 9 $"}, "mask_4bde2e70-e41d-11ed-b190-a701d8346fd0_11.jpg": {"label": "F { 3 } { 4 } * ( $ F { 4 } { } $ ) = 1", "predi": "F { 3 } { 4 } * ( $ F { 4 } { 2 } $ ) = 1"}, "mask_ec52fba1-c7db-11ed-ad1d-f38130ebe3ac_35.jpg": {"label": "3 . 1 4 * 1 3 + 3 . 1 4 * 1 7 = $ 9 4 . 2 $", "predi": "3 . 1 4 * 1 3 + 3 . 1 4 * 1 7 = $ 4 4 . 2 $"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_32.jpg": {"label": "F { 2 } { 7 } + F { 5 } { 1 1 } + F { 5 } { 7 } = $ F { 2 } { 7 } + F { 5 } { 7 } + F { 5 } { 1 1 } = 1 + F { 5 } { 1 1 } = 1 F { 5 } { 1 1 } $", "predi": "F { 2 } { 7 } + F { 5 } { 1 1 } + F { 5 } { 7 } = $ F { 2 } { 7 } + F { 5 } { 7 } + F { 5 } { 1 1 } = 1 + F { 5 } { 1 1 1 } { 1 F { 5 } { 1 1 } { 5 } { 1 1 } { 1 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { 1 } { } { 1 } { } { 1 } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { } { 5 } { 5 } { 5 {"}, "mask_1b6ecc31-d541-11ed-8f3b-c1e5fb0428f2_7.jpg": {"label": "1 2 * F { 3 } { 5 } =", "predi": "1 2 / F { 3 } { 5 } ="}, "mask_fc781e71-bfc0-11ed-a147-f3cbab19d534_25.jpg": {"label": "7 m L = ( $ 0 . 0 0 7 $ ) L", "predi": "7 m L = ( $ 0 . 0 0 7 $ ) L L"}, "mask_f34311a0-c70b-11ed-b67a-d11098fe96c5_23.jpg": {"label": "5 千 米 = ( $ 5 0 0 0 $ ) 米", "predi": "5 千 米 = ( $ 5 0 1 0 $ ) 米"}, "mask_ec1ae0e1-c6b8-11ed-beae-7326e6878b8a_2.jpg": {"label": "( $ 3 5 5 $ ) + 1 3 4 = 4 8 9", "predi": "( $ 3 5 5 $ ) + 1 3 4 = 4 8 9 9"}, "mask_ded5c0a1-e74a-11ed-9839-f9718d4223a9_12.jpg": {"label": "6 : 1 2 = $ 0 . . 3 $", "predi": "6 : 1 2 = $ 0 . 3 $"}, "mask_e1ac27a0-d8f0-11ed-bfe2-1b1542ddad09_11.jpg": {"label": "F { 3 6 } { 8 1 } = $ F { 5 } { 9 ] $", "predi": "F { 3 6 } { 8 1 } = $ F { 5 } { 9 } $"}, "mask_dd413da1-d043-11ed-af06-b70f51ba59d9_9.jpg": {"label": "F { 5 } 7 成 = $ 1 7 6 7 ( 4 七 $", "predi": "F { 5 角 五 = ( $ 7 6 7 ( 4 $ )"}, "mask_0b35ba30-d08d-11ed-9a87-810184c1a412_5.jpg": {"label": "F { 2 } { 9 } : F { 3 } { 4 } = ( $ F { 1 0 } { 8 1 } $ ) . F { 5 } { 1 2 }", "predi": "F { 2 } { 9 } : F { 3 } { 4 } = ( $ F { 1 0 } { 8 1 } $ ) : F { 5 } { 1 2 }"}, "mask_e907be41-d47e-11ed-920b-3ba87a6c5884_7.jpg": {"label": "0 . 7 5 * F { 1 } { 5 } = $ F { 1 5 0 } { 1 0 0 } $", "predi": "0 . 7 5 * F { 1 } { 5 } = $ F { 1 5 0 0 0 0 } $ 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0"}, "mask_5f435420-e320-11ed-99f6-81f8f4ba29d8_10.jpg": {"label": "F { 3 } { 5 } - F { 1 } { 2 0 } = $ F { 1 1 } [ 2 0 } $", "predi": "F { 3 } { 5 } - F { 1 } { 2 0 } = $ F { 1 1 } { 2 0 } $"}, "mask_f7109ac1-dd0c-11ed-b704-e77afdf7f94f_50.jpg": {"label": "4 0 : 1 1", "predi": "1 0 1 1"}, "mask_ed452870-c278-11ed-acbf-b3579151b686_16.jpg": {"label": "2 * 2 三 = $ 5 3 $", "predi": "2 * 2 5 = $ 8 3 $"}, "mask_d66c80a0-d08b-11ed-b88a-f35c3433537e_11.jpg": {"label": "4 - 8 1", "predi": "4 = $ 8 . 6 $"}, "mask_f132e2b1-d466-11ed-ab33-edbcf7bbc6bf_15.jpg": {"label": "1 1 1 1 1 1 1 1 1 1 0 1 1", "predi": "1 1 1 1 1 1 1 1 1 1 1 1 1 1"}, "mask_f437de91-c7df-11ed-89db-09b66e187465_22.jpg": {"label": "( $ 5 $ ) * 9 > 5 0", "predi": "( $ 5 $ ) * 9 < 5 0"}, "mask_00ab4401-dd11-11ed-b69a-d76908d4f989_18.jpg": {"label": "5 亿 = ( $ 5 0 0 0 0 0 0 0 0 $ )", "predi": "5 亿 = ( $ 5 0 0 0 0 0 0 0 0 $"}, "mask_ec76e6d1-e5b5-11ed-b2da-7710060f0357_89.jpg": {"label": "4 1 / 5 = ( $ 8 $ ) P ( $ 1 $ )", "predi": "4 1 / 5 = ( $ 8 $ ) P ( $ 1 $"}, "mask_dfa175d1-ecc0-11ed-b16f-dfe147916af4_1.jpg": {"label": "4 五 ( $ 1 } { 1 } $ ) } : F 五 { ( }", "predi": "4 [ ( $ 1 1 $ ) } { F { ( ) } { ( ) }"}, "mask_2b4d2d20-dead-11ed-92ce-8f9d95bbca57_32.jpg": {"label": "1 2 m L + ( $ 0 . 0 1 2 $ )", "predi": "1 2 m L = ( $ 0 . 0 1 2 $ )"}, "mask_3a8ea9c1-f088-11ed-afd7-0d03d3b771e3_7.jpg": {"label": "F { 5 } { 9 } - F { 5 } { 1 2 } + F { 4 } { 9 } = $ F { 5 } { 9 } + F { 4 } { 9 } - F { 5 } { 1 2 } = 1 - F { 5 } { 1 2 } = F { 1 2 } { 1 2 } - F { 5 } { 1 2 } = { 1 2 - 5 } { 1 2 } = F { 7 } { 1 2 } $", "predi": "F { 5 } { 9 9 } - F { 5 } { 1 2 } + F { 4 } { 9 } = $ F { 5 } { 9 } + F { 4 } { 9 } - F { 5 } { 1 2 } = 1 - F { 5 } { 1 2 } = F { 1 2 } { 1 2 } - F { 5 } { 1 2 } = F { 1 2 - 5 } = F { 7 } { 1 2 } $ F { 1 } { 1 2 } $"}, "mask_e6df4ac1-d604-11ed-85b3-f9c0b595672b_17.jpg": {"label": "1 0 0 * 0 . 0 1 5 * 1 0 = $ 7 . 5 $", "predi": "1 0 0 * 0 . 0 7 5 * 1 0 = $ 7 . 5 $"}, "mask_e8144521-e988-11ed-bee9-a3529571f462_42.jpg": {"label": "0 . 5 : 3 . 6 = ( $ F { 1 2 } { 2 5 } $ ) : 1 5", "predi": "0 . 5 : 3 . 6 = ( $ F { 1 : 1 5 } { 2 5 } $"}, "mask_5b9a4f51-d91a-11ed-b621-b5a720b0269b_18.jpg": {"label": "1 0 1 1", "predi": "1 0 1 1 1"}, "mask_fb197ba1-df37-11ed-a794-0bd03935def7_36.jpg": {"label": "6 0 / 1 2 0 0 0 = $ F { 1 } { 2 0 0 } $", "predi": "6 0 / 1 2 0 0 0 = $ F { 1 } { 2 0 0 0 } $"}, "mask_5aad13d1-c19a-11ed-a4b4-ed0a2df21331_39.jpg": {"label": "9 9 * 3 - 1 8 = $ 1 5 $", "predi": "9 9 / 3 - 1 8 = $ 1 5 $"}, "mask_ec106de1-e1c5-11ed-bdc9-a3caf80f747e_38.jpg": {"label": "1 天 = ( $ 2 4 $ ) 时", "predi": "1 五 = ( $ 2 4 $ ) 时"}, "mask_ef9c3f91-cef4-11ed-a171-8da2f75df540_5.jpg": {"label": "F { 3 } { x } = $ F { 1 } { 1 2 0 0 0 } $", "predi": "F { 3 } { x } = F { 1 } { 1 2 0 0 0 }"}, "mask_3d04f691-dd24-11ed-b269-bf8a73a71052_22.jpg": {"label": "F { 1 8 } { 1 1 } = F { ( $ 3 6 $ ) } { 7 2 }", "predi": "F { 1 8 } { 1 1 } = F { ( $ 3 6 $ ) } { 2 2 }"}, "mask_5b650e31-c484-11ed-a2dd-4f8a69cf87a7_7.jpg": {"label": "0 . 1 5 * 1 0 0 0 = $ 1 5 0 0 $", "predi": "0 . 1 5 * 1 0 0 0 = $ 1 5 0 0 5 0 0 $"}, "mask_e6e2a681-e4f4-11ed-b274-7d49d3347fd3_13.jpg": {"label": "1 8 元 十 P", "predi": "1 8 / 4 = $ 4 $ P $"}, "mask_1e8ee371-ed54-11ed-af4f-1b6df7ae09e2_35.jpg": {"label": "6 . 9 5 6 7 ≈ ( $ 6 . 9 6 $ )", "predi": "6 . 9 5 6 7 ≈ ( $ 6 . 9 6 $"}, "mask_1f520ae1-cee0-11ed-9835-2789202e11c4_31.jpg": {"label": "4 8 厘 米 - 2 5 厘 米 = $ 2 3 厘 米 $", "predi": "4 8 厘 米 - 2 5 厘 米 = $ 2 3 厘 米"}, "mask_6e9a0670-eda8-11ed-a20f-cf61e17ee98d_0.jpg": {"label": "F { 4 0 } { 1 6 } = F { } { 4 8 }", "predi": "F { 4 0 } { 1 6 } = F { ( ) } { 4 8 }"}, "mask_dab3eee1-dd2f-11ed-b8d3-2df5089bf94d_9.jpg": {"label": "( $ 1 2 $ ) c 5 五 1 : 2 0 0 0 0 0", "predi": "( $ 1 2 $ ) c 1 : 2 0 0 0 0 0"}, "mask_2bb10ec1-d21a-11ed-83a5-d96c6bae08aa_15.jpg": {"label": "1 k g 8 9 g = ( $ 1 . 0 8 9 $ ) k g", "predi": "1 k g 8 9 g = ( $ 1 . 0 8 9 $ ) k g g"}, "mask_1fb6e271-d5cb-11ed-9b97-2f6a5ca95ee6_26.jpg": {"label": "1 0 1 1 1 3 1 ( ) 1 1", "predi": "1 1 1 1 1 1 1 1 1 1"}, "mask_e781d991-c8a1-11ed-9d0a-95ec6217db79_15.jpg": {"label": "0 . 5 : 0 . 2 5 = $ 2 . 1 $", "predi": "0 . 5 : 0 . 2 5 = $ 2 : 1 $"}, "mask_1e8ee371-ed54-11ed-af4f-1b6df7ae09e2_16.jpg": {"label": "4 . 7 5 % ≈ ( $ 0 . 4 8 $ )", "predi": "4 . 7 5 % ≈ ( $ 0 . 4 8 $"}, "mask_2a1f0201-eff3-11ed-aa3f-57649601d057_39.jpg": {"label": "F { 8 } { 2 1 } / F { 2 } { 7 } = $ F { 8 } { 2 1 } + F { 6 } { 2 1 } = F { 2 } { 3 } $", "predi": "F { 8 } { 2 1 } + F { 2 } { 7 } = $ F { 8 } { 2 1 } + F { 6 } { 2 1 } = F { 2 } { 3 } $"}, "mask_f5b434f1-d53e-11ed-a640-fb9b4f351d70_40.jpg": {"label": "F { 8 } { 0 } = $ F { 6 } { 1 0 } $", "predi": "F { 8 } { 0 0 } = $ F { 6 { 6 0 { 1 0 { 1 0 $ F { 6 0 0 { 6 6 6 6 6 6 6 6 { $ F { 6 { 6 { 6 { 6 { 6 { 6 { 6 { 6 { 6 { 6 { 6 0 { 6 0 { 6 0 { 6 0 { 6 0 { 6 0 { 6 0 { 6 0 { 6 0 { 6 0 { 6 0 { 6 0 6 $ 6 6 6 $ F { 6 0 { 6 0 6 6 6 6 { 6 6 { 6 6 6 6 6 { 6 6 { 6 6 6 6 6 6 { 6 6 { 6 6 6 6 6 6 { 6 6 { 6 6 6 6 6 6 { 6 6 { 6 6 6 6 6 6 { 6 6 { 6 6 6 6 6 6 { 6 6 { 6 6 6 6 6 6 { 6 6 { 6 6 6 6 6 6 { 6 6 { 6 6 6 6"}, "mask_6a21c811-d3ba-11ed-8ec0-adc48813d352_22.jpg": {"label": "8 0 0 / 1 6 ≈", "predi": "8 0 0 / 1 6 ="}, "mask_dae2ce81-ed7f-11ed-8251-ffc616ddd88e_9.jpg": {"label": "1 8 0 ° * 6 = $ 1 0 8 ° $", "predi": "1 8 0 ° * 6 = $ 1 0 8 0 $"}, "mask_f1cd0601-ef24-11ed-b4cb-679d693c5b5d_0.jpg": {"label": "6 0 - 5 = $ 5 4 $", "predi": "6 0 - 6 = $ 5 4 $"}, "mask_f7d35df0-e5b1-11ed-9cdd-f97b2444d423_0.jpg": {"label": "角 7 $ < $ 7 角 1 分", "predi": "7 角 $ < $ 7 角 1 分"}, "mask_f2661331-e688-11ed-9199-a9eebc4480a8_41.jpg": {"label": "( $ 4 8 0 $ ) - 1 2 0 = 3 6 0", "predi": "$ 4 8 0 $ ) - 1 2 0 = 3 6 0"}, "mask_dd25db51-efdd-11ed-8eba-e37619319a37_2.jpg": {"label": "5 0 9 8 ≈ $ 5 0 0 0 $", "predi": "5 0 9 8 ≈ $ 5 0 0 6 $"}, "mask_dd413da1-d043-11ed-af06-b70f51ba59d9_5.jpg": {"label": "八 = $ 1 2 5 $", "predi": "F { 5 = $ 1 2 5 $"}, "mask_4efc0101-ef25-11ed-b03d-b94246ddbbe9_23.jpg": {"label": "F { 1 } { 1 0 } + F { 1 } { 1 1 } = $ F { 1 1 } { 1 1 0 } + F { 1 0 } { 1 1 0 } = F { 2 1 } { 1 1 0 } $", "predi": "F { 1 } { 1 0 } + F { 1 } { 1 1 } = $ F { 1 1 } { 1 1 } + F { 1 0 } { 1 1 0 } = F { 2 1 } { 1 1 0 } $"}, "mask_5f98a151-ceee-11ed-9d4e-69f950ec23e1_18.jpg": {"label": "3 4 * 5 = $ 1 7 $", "predi": "3 . 4 * 5 = $ 1 7 $"}, "mask_fb168d31-cca6-11ed-90f9-c543d32302dd_3.jpg": {"label": "6 0 * 3 0 0 = $ 1 8 0 0 0 $", "predi": "6 0 * 3 0 0 = $ 1 8 0 0 0 1 $"}, "mask_f089b161-c302-11ed-8680-910ff3f968bb_1.jpg": {"label": "2 5 * 4 0 = $ 1 8 0 0 $", "predi": "4 5 * 4 0 = $ 1 8 0 0 $"}, "mask_1a492c91-e8f8-11ed-8849-9bd50053a95e_41.jpg": {"label": "F { 3 0 } { 4 5 } F { 1 6 } { 8 4 }", "predi": "F { 3 0 } { 4 5 } F { 1 6 } { 2 4 }"}}