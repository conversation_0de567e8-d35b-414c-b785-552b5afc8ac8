# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_vertical_calculation.py
import traceback
import json
from base_common import LoggerFactory, ImageUtil, Constants, MissionMode
from base_common.service.service_of_base import BaseModelService
from vertical_calc_rec_service.model.shushi_interface import Shushi_Auto

log = LoggerFactory.get_logger('VerticalCalculationService')
class VerticalCalculationService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.VERTICAL_CALC_REC_MISSION
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/vertical_calc_rec_service'
        self.shushier = Shushi_Auto(model_path)

    def do_post(self, data_json):
        try:
            new_boxes_list = data_json['new_boxes_list']
            img_photo_cv2 = self.get_image(data_json)
            shushi_boxs, shushi_regs = self.shushier.run(img_photo_cv2, (0, 0), new_boxes_list)
            log.info(f'{json.dumps(shushi_regs)} {json.dumps(data_json)}')
            return {'shushi_boxs': shushi_boxs, 'shushi_regs': shushi_regs}
        except:
            log.error(f'{traceback.format_exc()}')
            return {'shushi_boxs': [], 'shushi_regs': []}