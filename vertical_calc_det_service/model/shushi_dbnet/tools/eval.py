# -*- coding: utf-8 -*-
# @Time    : 2018/6/11 15:54
# <AUTHOR> zhoujun
import os
import cv2
import sys
import pathlib
__dir__ = pathlib.Path(os.path.abspath(__file__))
sys.path.append(str(__dir__))
sys.path.append(str(__dir__.parent.parent))
# project = 'DBNet.pytorch'  # 工作项目根目录
# sys.path.append(os.getcwd().split(project)[0] + project)

import argparse
import time
import shutil
import torch
from tqdm.auto import tqdm
import numpy as np
from models import build_model
from data_loader import get_dataloader
from post_processing import get_post_processing
from utils import get_metric,Denormalize,draw_result,write_txt_map

class EVAL():
    def __init__(self, model_path, gpu_id=0):

        self.vis_dir = './vis_eval/'
        if os.path.exists(self.vis_dir):
            shutil.rmtree(self.vis_dir)
        os.mkdir(self.vis_dir)
        
        self.denorm = Denormalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        
        self.gpu_id = gpu_id
        if self.gpu_id is not None and isinstance(self.gpu_id, int) and torch.cuda.is_available():
            self.device = torch.device("cuda:%s" % self.gpu_id)
            torch.backends.cudnn.benchmark = True
        else:
            self.device = torch.device("cpu")
        checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
        config = checkpoint['config']
        config['arch']['backbone']['pretrained'] = False
        self.cls_list = config['cls_list']
        config['arch']['cls_list'] = self.cls_list
        config['dataset']['train']['dataset']['args']['cls_list'] = self.cls_list
        config['dataset']['validate']['dataset']['args']['cls_list'] = self.cls_list
        print(config['dataset']['validate']['dataset']['args']['data_path'])
        config['dataset']['validate']['dataset']['args']['data_path'] = [
#               '/home/<USER>/data/pdf_photo_detect/photo/tab/test_tab.txt',
#               '/home/<USER>/data/pdf_photo_detect/pdf/tab/test_tab.txt',
              '/home/<USER>/data/pdf_photo_detect/pdf/tab/test_tab_0323.txt',
              '/home/<USER>/data/pdf_photo_detect/pdf/tab/test_tab_0322.txt',
            
# #               '/home/<USER>/data/pdf_photo_detect/pdf/tab/test_tab.txt'
        ]

        self.validate_loader = get_dataloader(config['dataset']['validate'], config['distributed'])
        self.model = build_model(config['arch'])
        self.model.load_state_dict(checkpoint['state_dict'])
        self.model.to(self.device)

        self.box_thresh = 0.7
        config['post_processing']['args']['thresh'] = 0.5
        
        config['post_processing']['args']['box_thresh'] = self.box_thresh
        config['post_processing']['args']['unclip_ratio'] = 0
        
        
        self.post_process = get_post_processing(config['post_processing'])
        
        config['metric']['args']['is_output_polygon'] = False
        
        metric_cls1 = get_metric(config['metric'])
        metric_cls2 = get_metric(config['metric'])
        metric_cls3= get_metric(config['metric'])
        metric_cls4 = get_metric(config['metric'])
        self.metric_clses = [metric_cls1,metric_cls2,metric_cls3,metric_cls4]
            
        self.metric_cls_all = get_metric(config['metric'])

    def eval(self):
        self.model.eval()
        # torch.cuda.empty_cache()  # speed up evaluating after training finished
        raw_metrics = [[],[],[],[]]
        raw_metrics_all = []
        total_frame = 0.0
        total_time = 0.0
        for i, batch in tqdm(enumerate(self.validate_loader), total=len(self.validate_loader), desc='test model'):
            with torch.no_grad():
                # 数据进行转换和丢到gpu
                for key, value in batch.items():
                    if value is not None:
                        if isinstance(value, torch.Tensor):
                            batch[key] = value.to(self.device)
                start = time.time()
                preds = self.model(batch['img'])
#                 image_input = (self.denorm(batch['img'][0]) * 255).transpose(1, 2, 0).astype(np.uint8).copy()
                image_input = cv2.imread(batch['img_path'][0])
                boxes_all = []
                scores_all = []
                clses_all = []
                for kk,cls_ in enumerate(self.cls_list):
#                     ['img','for','tab','ver']
#                     if kk != 1:
#                         continue
                    boxes, scores = self.post_process(batch, preds[kk],is_output_polygon=self.metric_cls_all.is_output_polygon)
#                     print(boxes)
                    boxes_all.extend(boxes[0])
                    scores_all.extend(scores[0])
                    clses_all.extend([cls_]*len(boxes[0]))
                    
                    
                    raw_metric_kk = self.metric_clses[kk].validate_measure(batch, (boxes, scores),cls_,self.box_thresh)
                    raw_metric_all = self.metric_cls_all.validate_measure(batch, (boxes, scores),cls_,self.box_thresh)
                    
                    raw_metrics[kk].append(raw_metric_kk)
                    raw_metrics_all.append(raw_metric_all)
#                     break

                draw_result(self.vis_dir,batch['img_name'][0],image_input,boxes_all,scores_all,clses_all)#可视化结果

#                 write_txt_map(save_map_dir,batch['img_path'][0],boxes_all,scores_all,clses_all,self.box_thresh)

                total_frame += batch['img'].size()[0]
                total_time += time.time() - start
                
        for kk in range(len(self.cls_list)):
            metrics = self.metric_clses[kk].gather_measure(raw_metrics[kk])
            print(self.cls_list[kk],metrics['recall'].avg,metrics['precision'].avg,metrics['fmeasure'].avg)
            
        metrics = self.metric_cls_all.gather_measure(raw_metrics_all)
        recall = metrics['recall'].avg
        precision = metrics['precision'].avg
        fmeasure = metrics['fmeasure'].avg
        print('all cls together: ',recall,precision,fmeasure)
        
        print('FPS:{}'.format(total_frame / total_time))
        return recall,precision,fmeasure


def init_args():
    parser = argparse.ArgumentParser(description='DBNet.pytorch')
    parser.add_argument('--model_path', required=False,default='./weights/output_tab_res18_clean_v8/DBNet_resnet18_FPN_SEGHead/checkpoint/model_best.pth', type=str)
    args = parser.parse_args()
    return args


if __name__ == '__main__':

    args = init_args()
    detector = EVAL(args.model_path)
#     save_map_dir = '/home/<USER>/code/answer_det/short_yolo4/test_map/detection-results/'
#     if os.path.exists(save_map_dir):
#         shutil.rmtree(save_map_dir)
#     os.mkdir(save_map_dir)
    
    result = detector.eval()
    print(result)
