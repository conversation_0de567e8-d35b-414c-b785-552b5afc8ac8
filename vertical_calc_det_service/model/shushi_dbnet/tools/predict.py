# -*- coding: utf-8 -*-
# @Time    : 2019/8/24 12:06
# <AUTHOR> zhoujun

import os
import numpy as np
import sys
import pathlib
import shutil
__dir__ = pathlib.Path(os.path.abspath(__file__))
sys.path.append(str(__dir__))
sys.path.append(str(__dir__.parent.parent))

# project = 'DBNet.pytorch'  # 工作项目根目录
# sys.path.append(os.getcwd().split(project)[0] + project)
import time
import cv2
import torch
import random
from tqdm import tqdm

from data_loader import get_transforms
from models import build_model
from post_processing import get_post_processing
from utils import draw_result,write_jingling_json 


import pathlib
from utils.util import draw_bbox, save_result, get_file_list

def resize_image_long(img):
    height, width, _ = img.shape
    if height > width:
        long_size = (896,1280)#(896,1280)
    else:
        long_size = (1280,896)
    scale = min(long_size[1]/height,long_size[0]/width)
    new_height = int(round(round(height*scale) / 32) * 32)
    new_width = int(round(round(width*scale) / 32) * 32)
    resized_img = cv2.resize(img, (new_width, new_height))
    return resized_img


def resize_image_short(img, short_size):
    # 最短边至少resize到short_size，超过则不处理
    height, width, _ = img.shape
    if height < width:
        new_height = short_size
        new_width = new_height / height * width
    else:
        new_width = short_size
        new_height = new_width / width * height
    new_height = int(round(new_height / 32) * 32)
    new_width = int(round(new_width / 32) * 32)
    resized_img = cv2.resize(img, (new_width, new_height))
    return resized_img


class Pytorch_model:
    def __init__(self, model_path, post_p_thre=0.7, gpu_id=None):
        '''
        初始化pytorch模型
        :param model_path: 模型地址(可以是模型的参数或者参数和计算图一起保存的文件)
        :param gpu_id: 在哪一块gpu上运行
        '''
        self.gpu_id = gpu_id

        if self.gpu_id is not None and isinstance(self.gpu_id, int) and torch.cuda.is_available():
            self.device = torch.device("cuda:%s" % self.gpu_id)
        else:
            self.device = torch.device("cpu")
        print('device:', self.device)
        checkpoint = torch.load(model_path, map_location=self.device)

        config = checkpoint['config']
        config['arch']['backbone']['pretrained'] = False
        
        self.cls_list = config['cls_list']
        config['arch']['cls_list'] = self.cls_list
        config['dataset']['train']['dataset']['args']['cls_list'] = self.cls_list
        config['dataset']['validate']['dataset']['args']['cls_list'] = self.cls_list
        
        config['post_processing']['args']['thresh'] = 0.005
        config['post_processing']['args']['box_thresh'] = 0.01
        config['post_processing']['args']['unclip_ratio'] = 0.
        self.post_process = get_post_processing(config['post_processing'])
        self.img_mode = config['dataset']['train']['dataset']['args']['img_mode']
        
        self.model = build_model(config['arch'])
        self.model.load_state_dict(checkpoint['state_dict'])
        self.model.to(self.device)
        self.model.eval()

        self.transform = []
        for t in config['dataset']['train']['dataset']['args']['transforms']:
            if t['type'] in ['ToTensor', 'Normalize']:
                self.transform.append(t)
        self.transform = get_transforms(self.transform)
#         self.jingling_dir = ''
        self.jingling_dir = '/home/<USER>/data/quickcal/auto_data/detect_full/choose_1000_0/outputs_0704'
        if os.path.exists(self.jingling_dir):
            shutil.rmtree(self.jingling_dir)
        os.mkdir(self.jingling_dir)
        
        self.vis_dir = './vis_predict/'
        if os.path.exists(self.vis_dir):
            shutil.rmtree(self.vis_dir)
        os.mkdir(self.vis_dir)
        
        self.crop_dir = ''
#         self.crop_dir = './crop_maths_part1/'
#         if os.path.exists(self.crop_dir):
#             shutil.rmtree(self.crop_dir)
#         os.mkdir(self.crop_dir)
        
    def predict(self, img_path: str,image_name:str, is_output_polygon=False):
        '''
        对传入的图像进行预测，支持图像地址,opecv 读取图片，偏慢
        :param img_path: 图像地址
        :param is_numpy:
        :return:
        '''
        assert os.path.exists(img_path), 'file is not exists'
            
        img = cv2.imread(img_path, 1 if self.img_mode != 'GRAY' else 0)
        image_input = img.copy()
        if self.img_mode == 'RGB':
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        h, w = img.shape[:2]
        img = resize_image_long(img)
        # 将图片由(w,h)变为(1,img_channel,h,w)
        tensor = self.transform(img)
        tensor = tensor.unsqueeze_(0)

        tensor = tensor.to(self.device)
        batch = {'shape': [(h, w)]}
        
        with torch.no_grad():
            if str(self.device).__contains__('cuda'):
                torch.cuda.synchronize(self.device)
            start = time.time()
            preds = self.model(tensor)
        
            boxes_all = []
            scores_all = []
            clses_all = []
            for kk,cls_ in enumerate(self.cls_list):
#                 ['img','for','tab','ver']
#                 if kk != 1:
#                     continue
                boxes, scores = self.post_process(batch, preds[kk],is_output_polygon=is_output_polygon)
    
                cv2.imwrite('./mask_{}.jpg'.format(kk),np.array(preds[kk][0][0].cpu()*255,dtype=np.uint8))
#                 cv2.imwrite('./mask_{}.jpg'.format(kk),image_input)
                boxes_all.extend(boxes[0])
                scores_all.extend(scores[0])
                clses_all.extend([cls_]*len(boxes[0]))
                
#                 for ii_,b in enumerate(boxes[0]):
#                     if b[1][0] - b[0][0] > 0.6 * w:
#                         boxes_all.append(boxes[0][ii_])
#                         scores_all.append(scores[0][ii_])
#                         clses_all.append(cls_)

#             if len(boxes_all) != 0:
#                 if random.random() < 0.01:
                draw_result(self.vis_dir,image_name[:image_name.rfind('.')],image_input,boxes_all,scores_all,clses_all,crop_dir=self.crop_dir)#可视化结果
                
                if len(self.jingling_dir) != 0:
                    if len(boxes_all) == 1:
                            if boxes_all[0][2][1] - boxes_all[0][0][1] < 0.5 * h:
#                         if random.random() < 0.1:
                                write_jingling_json(self.jingling_dir,image_input,img_path,boxes_all,clses_all,is_output_polygon)
                    else:
                        write_jingling_json(self.jingling_dir,image_input,img_path,boxes_all,clses_all,is_output_polygon)
            else:
#                 print(img_path)
                pass
            t = time.time() - start
            
        return boxes_all, scores_all, clses_all, t


def save_depoly(model, input, save_path):
    traced_script_model = torch.jit.trace(model, input)
    traced_script_model.save(save_path)


def init_args():
    import argparse
    parser = argparse.ArgumentParser(description='DBNet.pytorch')
    parser.add_argument('--model_path', default='./weights/output_print_2/DBNet_resnet18_FPN_SEGHead/checkpoint/model_latest.pth', type=str)
    parser.add_argument('--input_folder', default='./test/', type=str, help='img path for predict')
    parser.add_argument('--thre', default=0.7,type=float, help='the thresh of post_processing')
    parser.add_argument('--polygon', action='store_true', help='output polygon or box')
    parser.add_argument('--show', action='store_true', help='show result')
    parser.add_argument('--save_resut', action='store_true', help='save box and score to txt file')
    args = parser.parse_args()
    return args


if __name__ == '__main__':
    args = init_args()
    os.environ['CUDA_VISIBLE_DEVICES'] = str('0')
    # 初始化网络
    model = Pytorch_model(args.model_path, post_p_thre=args.thre, gpu_id=0)
#     img_folder = pathlib.Path(args.input_folder)
#     for img_path in tqdm(get_file_list(args.input_folder, p_postfix=['.jpg'])):

#     predict_txt = '/home/<USER>/data/pdf_photo_detect/pdf/tianzi_wuxian_zuowen/test.txt'
#     with open(predict_txt,'r') as f:
#         lines = f.readlines()
#         image_paths = [line.split('\t')[0] for line in lines]
        
        
#     img_dir = '/home/<USER>/data/split_page/to_label_sup_1/'
    img_dir = '/home/<USER>/data/quickcal/auto_data/detect_full/choose_1000_0'
    image_paths = [os.path.join(img_dir,i) for i in os.listdir(img_dir)]
    
#     img_dir_0 = '/home/<USER>/data/pdf_photo_detect/pdf/chinese_imgs/'
#     img_dir_1 = '/home/<USER>/data/pdf_photo_detect/pdf/english_imgs/'
#     image_paths = [os.path.join(img_dir_0,i) for i in os.listdir(img_dir_0)]+[os.path.join(img_dir_1,i) for i in os.listdir(img_dir_1)]
    for image_path in tqdm(image_paths):
#         image_path = '/home/<USER>/code/answer_det/tianzi_wuxian/b.jpg'
#         image_path'./test_imgs/1.jpg'
        image_name = image_path.split('/')[-1]
        if image_name.find('ipynb_checkpoints') != -1:
            continue
#         result = model.predict(image_path,image_name, is_output_polygon=False)
        try:
            result = model.predict(image_path,image_name, is_output_polygon=True)
#             print(result)
        except:
            print(image_name)
            pass
        
#         break
        
#     img_paths = ['./test_images/t2.png','./test_images/t1.png','./test_images/iShot_2022-10-10_14.png','./test_images/aa.png','./test_images/a1.png','./test_images/a2.png','./test_images/a3.png','./test_images/a4.png','./test_images/a5.png','./test_images/a7.png','./test_images/a6.png']
#     for image_path in img_paths:
#         image_name = image_path.split('/')[-1]
#         result = model.predict(image_path,image_name, is_output_polygon=False)
    
'''
    img_dir = '/home/<USER>/data/pdf_photo_detect/pdf/par_line_imgs/' #'/home/<USER>/data/table/tal_ocr_table/train_data/train_img/'
    start = time.time()
    for image_name in tqdm(os.listdir(img_dir)):
        if image_name.find('ipynb_checkpoints') != -1:
            continue
        img_path = img_dir+image_name

        try:
            result = model.predict(img_path,image_name, is_output_polygon=False)
        except:
            print(img_path)
#         print(result)
    end = time.time()
    
    print('1 image cost: {}, fps is {}.'.format((end-start)/len(os.listdir(img_dir)),len(os.listdir(img_dir))/(end-start)))
'''