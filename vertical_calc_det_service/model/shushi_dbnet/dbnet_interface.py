# -*- coding: utf-8 -*-
# @Time    : 2019/8/24 12:06
# <AUTHOR> zhoujun

import time
import cv2
import torch

from base_common import Constants
from vertical_calc_det_service.model.shushi_dbnet.data_loader import get_transforms
from vertical_calc_det_service.model.shushi_dbnet.models import build_model
from vertical_calc_det_service.model.shushi_dbnet.post_processing import get_post_processing

'''
def resize_image_long(img, long_size):
    try:
        height, width, _ = img.shape
        long_size = (1280,896)
        
        if height < long_size[0] and width < long_size[1]:
            scale = 1
        else:
            scale = min(long_size[0]/height,long_size[1]/width)
        new_height = int(round(round(height*scale) / 32) * 32)
        new_width = int(round(round(width*scale) / 32) * 32)
        resized_img = cv2.resize(img, (new_width, new_height))
        return resized_img
    except:
        return None
'''
def resize_image_long(img, long_size):
    try:
        height, width, _ = img.shape
        if height > width:
            long_size = (1560,1280) #(1280,896)
        else:
            long_size = (1560,2048) #(1280,896)
        
        #if height < long_size[0] and width < long_size[1]:
        #    scale = 1
        #else:
        scale = min(long_size[0]/height,long_size[1]/width)
        new_height = int(round(round(height*scale) / 32) * 32)
        new_width = int(round(round(width*scale) / 32) * 32)
        resized_img = cv2.resize(img, (new_width, new_height))
        return resized_img
    except:
        return None


def resize_image_short(img, short_size):
    # 最短边至少resize到short_size，超过则不处理
    height, width, _ = img.shape
    if height < width:
        new_height = short_size
        new_width = new_height / height * width
    else:
        new_width = short_size
        new_height = new_width / width * height
    new_height = int(round(new_height / 32) * 32)
    new_width = int(round(new_width / 32) * 32)
    resized_img = cv2.resize(img, (new_width, new_height))
    return resized_img


class Pytorch_model:
    def __init__(self, model_path):
        '''
        初始化pytorch模型
        :param model_path: 模型地址(可以是模型的参数或者参数和计算图一起保存的文件)
        :param gpu_id: 在哪一块gpu上运行
        '''
        torch.cuda.set_per_process_memory_fraction(0.1875, device=0)
        checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
        
        config = checkpoint['config']
        config['arch']['backbone']['pretrained'] = False
        self.model = build_model(config['arch'])
        
        config['post_processing']['args']['thresh'] = 0.1 #0.5
        config['post_processing']['args']['box_thresh'] = 0.1 #0.5
        config['post_processing']['args']['unclip_ratio'] = 1.1 #0.8
        
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
#         self.device = torch.device('cpu')
        
        self.post_process = get_post_processing(config['post_processing'])
        self.img_mode = config['dataset']['train']['dataset']['args']['img_mode']
        self.model.load_state_dict(checkpoint['state_dict'])
#         self.model.cuda()
        self.model = self.model.to(self.device)
        self.model.eval()

        self.transform = []
        for t in config['dataset']['train']['dataset']['args']['transforms']:
            if t['type'] in ['ToTensor', 'Normalize']:
                self.transform.append(t)
        self.transform = get_transforms(self.transform)

    def predict(self, image: str, is_output_polygon=False, short_size: int = 1024):
        """
        对传入的图像进行预测，支持图像地址,opecv 读取图片，偏慢
        :param image: 图像cv
        :param is_numpy:
        :return:
        """
        
        if self.img_mode == 'RGB':
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        h, w = image.shape[:2]
        img = resize_image_long(image, short_size)
        if img is None:
            return [],[],0
        # 将图片由(w,h)变为(1,img_channel,h,w)
        tensor = self.transform(img)
        tensor = tensor.unsqueeze_(0)

#         tensor = tensor.cuda()
        tensor = tensor.to(self.device)
        batch = {'shape': [(h, w)]}
        with torch.no_grad():
            start = time.time()
            preds = self.model(tensor)
            box_list, score_list = self.post_process(batch, preds[0], is_output_polygon=is_output_polygon)
            box_list, score_list = box_list[0], score_list[0]
            if len(box_list) > 0:
                if is_output_polygon:
                    idx = [x.sum() > 0 for x in box_list]
                    box_list = [box_list[i] for i, v in enumerate(idx) if v]
                    score_list = [score_list[i] for i, v in enumerate(idx) if v]
                else:
                    idx = box_list.reshape(box_list.shape[0], -1).sum(axis=1) > 0  # 去掉全为0的框
                    box_list, score_list = box_list[idx], score_list[idx]
            else:
                box_list, score_list = [], []
            t = time.time() - start
        return box_list, score_list, t

class Dbnet_Interface:
    def __init__(self, model_path):
        self.polygon = False
        self.model = Pytorch_model(model_path)
        
    def detect_img(self,image):
        box_list, score_list, t = self.model.predict(image, is_output_polygon=self.polygon)
        
        return box_list
