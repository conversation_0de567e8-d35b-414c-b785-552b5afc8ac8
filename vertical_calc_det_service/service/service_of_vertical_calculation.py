# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_vertical_calculation.py
import traceback
from base_common import LoggerFactory, ImageUtil, Constants, MissionMode
from base_common.service.service_of_base import BaseModelService
from vertical_calc_det_service.model.shushi_interface import Shushi_Auto

log = LoggerFactory.get_logger('VerticalCalculationService')
class VerticalCalculationService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.VERTICAL_CALC_DET_MISSION
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/vertical_calc_det_service/model_best.pth'
        self.shushier = Shushi_Auto(model_path)

    def do_post(self, data_json):
        try:
            img_photo_cv2 = self.get_image(data_json)
            boxs = self.shushier.run(img_photo_cv2)
            return boxs
        except:
            log.error(f'{traceback.format_exc()}')
            return []