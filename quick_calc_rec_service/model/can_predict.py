import os
import shutil
import cv2
from PIL import Image
import torch
from transformers import TrOCRProcessor, VisionEncoderDecoderModel

import numpy as np
from glob import glob
from tqdm import tqdm
os.environ["CUDA_VISIBLE_DEVICES"] = '0'
from quick_calc_rec_service.model import util
from quick_calc_rec_service.model import handle_question_new
from base_common import Constants,LoggerFactory
log = LoggerFactory.get_logger('QuickcalReg')

def decode_text(tokens, vocab, vocab_inp):
    ##decode trocr
    s_start = vocab.get('<s>')
    s_end = vocab.get('</s>')
    unk = vocab.get('<unk>')
    pad = vocab.get('<pad>')
    text = []
    for tk in tokens:
        if tk not in [s_end, s_start , pad, unk]:
            text.append(vocab_inp[tk])

    return text

def judge_item(pred_str):
    '''
    final = {
    'flag':True/False,
    'reg_answers':['',''],
    'std_answers':['',''],
    }

    0:True 对
    1:False 错
    2:None 问号
    3:Printing 印刷体
    4:Error 报错
    '''
    final = {'flag': '2', 'reg_answers': [], 'std_answers': []}

    pred_str = util.fraud_frac_replace(pred_str)  # 一又二分之一改成“（1+1/2）”
    pred_str = util.add_plus_between_units(pred_str)  # 2dm^25cm^2改成2dm^2+5cm^2
    ori_line = str(pred_str)

    # 快速计算,判断式子是否成立，如果成立直接返回True
    quick_pred = handle_question_new.quick_correcting(pred_str)
    if quick_pred:
        final['flag'] = '0'
        return final

    # 替换其中的美元符号 $
    stem, answer_list = util.replace_dollar(pred_str)
    # 特殊题型 只判错并且不推荐答案的题型
    result = handle_question_new.wrong_and_no_answer_question(final, pred_str, stem, answer_list)
    if result is not None:
        return result

    # 特殊题型
    result = handle_question_new.SpecialQuestionHandler().handle_special_question(ori_line, final, pred_str, stem,
                                                                                  answer_list)
    return result
     
def judge_pred(label_loss_mean,pred_loss_mean,lbl,pred_text=''):
    pred_loss_mean = 1e-6
    if abs(len(lbl) - len(pred_text)) > 2:
        return False
    if label_loss_mean > 2:
        return False

    if label_loss_mean < 1.:
        return True
    return label_loss_mean/pred_loss_mean < 100.
    
class CanPredictor:
    def __init__(self,model_path):
        self.processor = TrOCRProcessor.from_pretrained(f"{model_path}")
        self.vocab = self.processor.tokenizer.get_vocab()
        self.vocab_inp = {self.vocab[key]: key for key in self.vocab}
        self.model = VisionEncoderDecoderModel.from_pretrained(f"{model_path}",early_stopping=True,length_penalty=2.0,num_beams=2)
        self.model.config.max_length = 128
        self.model.config.no_repeat_ngram_size = 0 #3 取消预测词组重复的限制
        #self.model.config.length_penalty = 2.0 #2.0 不对预测长度进行惩罚
    
        self.model.eval()
        self.model.cuda()
        self.crossentropyloss = torch.nn.CrossEntropyLoss(reduction='none')
        self.max_label_length = 128
        
    def predict(self,imgs,labels=None):
        imgs = [Image.fromarray(cv2.cvtColor(cv2_image, cv2.COLOR_BGR2RGB).astype(np.uint8)) for cv2_image in imgs]
        if labels is None:
            return self.predict_no_label(imgs)
        else:
            return self.predict_with_label(imgs,labels)

    def predict_no_label_multi(self,img):
        pixel_values = self.processor([img], return_tensors="pt").pixel_values
        with torch.no_grad():
            generated_ids = self.model.generate(pixel_values[:, :, :].cuda(),num_return_sequences=3,num_beams=3,return_dict_in_generate=True,output_scores=True)
            #num_return_sequences输出几个预测结果，num_beams表示有几个备选预测，影响速度
        generated_texts_i = []
        scores_i = []        
        for j in range(len(generated_ids.sequences)):
            generated_text = decode_text(generated_ids.sequences[j].cpu().numpy(), self.vocab, self.vocab_inp)
            final_str = ''.join(generated_text)
            score = generated_ids.sequences_scores[j].cpu().numpy()
            
            #loss = self.get_label_loss(imgs[i],final_str)
            
            #log.info(f'generated_text: {final_str},score:{score},loss:{0}')
            
            if j > 0 and score < -0.05:
                break
            generated_texts_i.append(final_str)
            scores_i.append(generated_ids.sequences_scores[j].cpu().numpy())

        #log.info(f'generated_texts_i: {generated_texts_i},scores_i:{scores_i}')
        return generated_texts_i

    def predict_no_label(self,imgs):
        

        pixel_values = self.processor(imgs, return_tensors="pt").pixel_values
        with torch.no_grad():
            generated_ids = self.model.generate(pixel_values[:, :, :].cuda(),num_return_sequences=1,num_beams=1,return_dict_in_generate=True,output_scores=True,early_stopping=True,length_penalty=2.)
            #num_return_sequences输出几个预测结果，num_beams表示有几个备选预测，影响速度
        
        generated_texts = []
        for i in range(len(imgs)):
            generated_text = decode_text(generated_ids.sequences[i].cpu().numpy(), self.vocab, self.vocab_inp)
            generated_text = ''.join(generated_text)
            
            final = util.check_pred_str(generated_text, judge_item)
            if final is None:
                final = judge_item(generated_text)
            if not final is None and final['flag'] == '0':
                generated_texts.append([generated_text])
            else:
                generated_texts_multi = self.predict_no_label_multi(imgs[i])
                generated_texts_tmp = [generated_text]
                generated_texts_tmp.extend(generated_texts_multi)
                generated_texts.append(generated_texts_tmp)

            
        log.info(f'predict_no_label: {generated_texts}')
        return generated_texts
        

    def get_label_loss(self,img,label_str):
        pixel_values = self.processor([img], return_tensors="pt").pixel_values
        pixel_values = pixel_values.to('cuda:0')

        n = 0
        label = [l for l in label_str]
        
        ##### model methode

        lbl = [-100]*self.max_label_length
        lbl[0] = 0
        for i,l in enumerate(label[:self.max_label_length-3]):
            try:
                lbl[i+1] = self.vocab[l]
            except:
                continue
        lbl[i+2] = 2
        tensor_labels = torch.tensor(lbl).unsqueeze(0).to('cuda:0')
        
        with torch.no_grad():
            outputs = self.model(pixel_values=pixel_values,labels=tensor_labels)
        return outputs.loss.cpu().item()

    def predict_loss(self,imgs):
    
        pixel_values = self.processor(imgs, return_tensors="pt").pixel_values
        with torch.no_grad():
            generated_ids = self.model.generate(pixel_values[:, :, :].cuda(),num_return_sequences=1,num_beams=1,return_dict_in_generate=True,output_scores=True,early_stopping=False,length_penalty=1.0)
            #num_return_sequences输出几个预测结果，num_beams表示有几个备选预测，影响速度
        
        generated_texts = []
        losses_mean = []
        for i in range(len(imgs)):
            generated_text = decode_text(generated_ids.sequences[i].cpu().numpy(), self.vocab, self.vocab_inp)
            generated_texts.append(generated_text)
        
            logits = []
            for ii in range(len(generated_text)+2):
                logits.append(generated_ids.scores[ii][i])
            logits = torch.stack(logits,0).to('cuda:0')
            
            labels = [0]*(len(generated_text)+2)
            for it,tt in enumerate(generated_text):
                labels[it+1] = self.vocab[tt]
            labels[-1] = 2
            tensor_labels = torch.tensor(labels).to('cuda:0')
            
            loss = self.crossentropyloss(logits,tensor_labels)
            losses_mean.append(loss.mean().cpu().item())
        
        return generated_texts,losses_mean
      
    def predict_with_label(self,imgs,labels):
        pred_labels = []
        generated_texts,losses_mean = self.predict_loss(imgs)
        for i in range(len(imgs)):
            pred_text_list = generated_texts[i] #['1','2']
            labels_i = labels[i].split('\t')
            
            get_right_answer = False
            for lbl in labels_i:
                pred_text = ''
                for gt in pred_text_list:
                    pred_text += gt
                        
                if pred_text== lbl:
                    #pred_labels.append([lbl])
                    pred_labels.append(True)
                    get_right_answer = True
                    break

                pred_loss_mean = losses_mean[i]#第一预测答案的loss        
                label_loss_mean = self.get_label_loss(imgs[i],lbl)#期望答案的loss,lbl为str
                # judge=True认为可以模糊替换,else...
                
                judge = judge_pred(label_loss_mean,pred_loss_mean,lbl,pred_text)
                log.info(f'{label_loss_mean} {pred_loss_mean} {lbl} {pred_text} {judge}')
                
                if judge :
                    #pred_labels.append([lbl])
                    pred_labels.append(True)
                    get_right_answer = True
                    break
                else:
                    pass
                    
            if not get_right_answer:
                #pred_labels.append([pred_text])
                pred_labels.append(False)
        log.info(f'predict_with_label: {pred_labels}')
        return pred_labels