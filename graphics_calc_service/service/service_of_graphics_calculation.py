# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_vertical_calculation.py
import time
import cv2
from PIL import Image

from base_common import LoggerFactory, Constants, ImageUtil, MissionMode
from base_common.service.service_of_base import BaseModelService
from graphics_calc_service.model.paint_interface import Yolo_Det_Paint

log = LoggerFactory.get_logger('GraphicsCalculationService')

class GraphicsCalculationService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.GRAPHICS_CALC_MISSION
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/graphics_calc_service/best.pt'
        self.painter = Yolo_Det_Paint(model_path, vis_paint=Constants.SAVE_VIS)

    def do_post(self, data_json):
        resp_datas = [None] * len(data_json)
        log.info(f'作图题请求 :{len(data_json)}')
        for i in range(len(data_json)):
            req_data = data_json[i]
            item_img_photo = ImageUtil.base64_2_numpy(req_data['img_base64'])
            item_img_photo = Image.fromarray(cv2.cvtColor(item_img_photo, cv2.COLOR_BGR2RGB))

            b0, b1 = req_data['bb']
            answer = req_data['answer']
            try:
                paint_results = self.painter.run(item_img_photo, answer, (b0, b1), f'graphics_{str(time.time())}', i)
                resp_datas[i] = {'boxs': {}, 'types': {}} if paint_results is None else paint_results
            except:
                resp_datas[i] = {'boxs': {}, 'types': {}}
                log.error("unsupport type")
        return resp_datas