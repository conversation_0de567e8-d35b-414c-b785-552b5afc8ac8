'''
predict.py有几个注意点
1、无法进行批量预测，如果想要批量预测，可以利用os.listdir()遍历文件夹，利用Image.open打开图片文件进行预测。
2、如果想要保存，利用r_image.save("img.jpg")即可保存。
3、如果想要获得框的坐标，可以进入detect_image函数，读取top,left,bottom,right这四个值。
4、如果想要截取下目标，可以利用获取到的top,left,bottom,right这四个值在原图上利用矩阵的方式进行截取。
'''
import os
import shutil
from PIL import Image
import config
import time
from tqdm import tqdm

from base_common import Constants

cfg = config.config_swin_640

from yolo import YOLO
save_dir = f'{Constants.VIS_PATH}/'
if os.path.exists(save_dir):
    shutil.rmtree(save_dir)
os.mkdir(save_dir)
save_dir_ = './test_map/detection-results/'
if os.path.exists(save_dir_):
    shutil.rmtree(save_dir_)
os.mkdir(save_dir_)


yolo = YOLO()

test_txt = cfg['test_txt']
with open(test_txt) as ff:
    lines = ff.readlines()
imgs_path = [line.split(' ')[0] for line in lines]

n = 0
start = time.time()
for img_path in tqdm(imgs_path):
#     try:
        n+=1
        if n%1000 == 0:
            print(n)
        if not len(img_path.split('.')[-1]) == 3:
            continue
        image = Image.open(img_path)

        r_image = yolo.detect_image(image,img_path)
        r_image.convert('RGB').save(os.path.join('./vis',img_path.split('/')[-1]))

end = time.time()
print('speed is {} s'.format((end-start)/n))
print('fps is {} s'.format(n/(end-start)))