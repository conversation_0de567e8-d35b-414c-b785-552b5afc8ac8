from random import shuffle
import numpy as np
import torch
import os
import torch.nn as nn
import math
import torch.nn.functional as F
from PIL import Image
from torch.autograd import Variable
from torch.utils.data import DataLoader
from torch.utils.data.dataset import Dataset
from utils.utils import bbox_iou, merge_bboxes
from matplotlib.colors import rgb_to_hsv, hsv_to_rgb
from nets.yolo_training import Generator
import cv2
import random

class YoloDataset(Dataset):
    def __init__(self, train_lines, image_size, mosaic=True, is_train=True):
        super(YoloDataset, self).__init__()

        self.train_lines = train_lines
        self.train_batches = len(train_lines)
        self.image_size = image_size
        self.mosaic = mosaic
        self.flag = True
        self.is_train = is_train

    def __len__(self):
        return self.train_batches

    def rand(self, a=0, b=1):
        return np.random.random()*(b-a)+a

    def get_random_data(self, annotation_line, input_shape, jitter=.1, hue=.005, sat=1.005, val=1.005, random_=True):
        """实时数据增强的随机预处理"""
        line = annotation_line.strip().split(' ')
        image = Image.open(line[0])
#         ori = Image.open(line[0].replace('adjust','A4_printed'))
        ori = Image.open(line[0].replace('croped_answered_images','croped_empty_images'))
        iw, ih = image.size
        h, w = input_shape
        box = np.array([np.array(list(map(int, box.split(',')))) for box in line[1:]])
        if not random_:
            scale = min(w/iw, h/ih)
            nw = int(iw*scale)
            nh = int(ih*scale)
            dx = (w-nw)//2
            dy = (h-nh)//2
#             image = image.resize((nw,nh), Image.BICUBIC)
#             new_image = Image.new('RGB', (w,h), (128,128,128))
#             new_image.paste(image, (dx, dy))
            
            
            new_ar = nw / nh * self.rand(1 - jitter, 1 + jitter) / self.rand(1 - jitter, 1 + jitter)
            scale = self.rand(.95, 1.05)
            #print(scale)
            if new_ar < 1:
                nh = int(scale * nh)
                nw = int(nh * new_ar)
            else:
                nw = int(scale * nw)
                nh = int(nw / new_ar)
            image = image.resize((nw, nh), Image.BICUBIC)
            gap_x = 0
            gap_y = 0
            gap_x_image = 0
            gap_y_image = 0
            
            ###模板与照片偏差增强
#             gap_x = random.randint(0,min(10,nw//10))
#             gap_y = random.randint(0,min(10,nh//10))
#             gap_x_image = random.randint(0,gap_x)
#             gap_y_image = random.randint(0,gap_y)
#             image = image.crop([gap_x_image,gap_y_image,nw-gap_x,nh-gap_y])
#             image = image.resize((nw, nh), Image.BICUBIC)
            
            # 放置图片
            dx = int(self.rand(0, w - nw))
            dy = int(self.rand(0, h - nh))
            new_image = Image.new('RGB', (w, h),
                                  (np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255)))
            new_image.paste(image, (dx, dy))
            
            
#             image_data = np.array(new_image, np.float32)
            
            ori = ori.resize((nw,nh), Image.BICUBIC)
        
            ###模板与照片偏差增强
#             gap_x_ = random.randint(0,min(10,nw//10))
#             gap_y_ = random.randint(0,min(10,nh//10))
#             gap_x_ori = random.randint(0,gap_x_)
#             gap_y_ori = random.randint(0,gap_y_)
#             ori = ori.crop([gap_x_ori,gap_y_ori,nw-gap_x_,nh-gap_y_])
#             ori = ori.resize((nw,nh), Image.BICUBIC)
            
            new_ori = Image.new('RGB', (w,h), (128,128,128))
            new_ori.paste(ori, (dx, dy))
            
            flip = self.rand() < 0.
            if flip:
                new_image = new_image.transpose(Image.FLIP_LEFT_RIGHT)
                new_ori = new_ori.transpose(Image.FLIP_LEFT_RIGHT)
                
            hue = self.rand(-hue, hue)
            sat = self.rand(2-sat, sat) 
            val = self.rand(2-val, val)
            x = cv2.cvtColor(np.array(new_image,np.float32)/255, cv2.COLOR_RGB2HSV)
            x[..., 0] += hue*360
            x[..., 0][x[..., 0]>1] -= 1
            x[..., 0][x[..., 0]<0] += 1
            x[..., 1] *= sat
            x[..., 2] *= val
            x[x[:,:, 0]>360, 0] = 360
            x[:, :, 1:][x[:, :, 1:]>1] = 1
            x[x<0] = 0
            image_data = cv2.cvtColor(x, cv2.COLOR_HSV2RGB)*255
            
#             image_data = cv2.cvtColor(image_data,cv2.COLOR_RGB2GRAY)
#             new_ori = cv2.cvtColor(np.array(new_ori),cv2.COLOR_RGB2GRAY)
            
            # 调整目标框坐标
            box_data = np.zeros((len(box), 5))
            if len(box) > 0:
                np.random.shuffle(box)
                box[:, [0, 2]] = box[:, [0, 2]] * nw / iw
                box[:, [1, 3]] = box[:, [1, 3]] * nh / ih
                
                box[:, [0, 2]] = (box[:, [0, 2]]-gap_x_image)*(nw/(nw-gap_x))
                box[:, [1, 3]] = (box[:, [1, 3]]-gap_y_image)*(nh/(nh-gap_y))
                
                box[:, [0, 2]] = box[:, [0, 2]] + dx
                box[:, [1, 3]] = box[:, [1, 3]] + dy
                if flip:
                    box[:, [0, 2]] = w - box[:, [2, 0]]
                box[:, 0:2][box[:, 0:2] < 0] = 0
                box[:, 2][box[:, 2] > w] = w
                box[:, 3][box[:, 3] > h] = h
                box_w = box[:, 2] - box[:, 0]
                box_h = box[:, 3] - box[:, 1]
                box = box[np.logical_and(box_w > 1, box_h > 1)]  # 保留有效框
                box_data = np.zeros((len(box), 5))
                box_data[:len(box)] = box

            return image_data,np.array(new_ori), box_data

        # 调整图片大小
#         print('bug....')
        new_ar = iw / ih * self.rand(1 - jitter, 1 + jitter) / self.rand(1 - jitter, 1 + jitter)
        scale = self.rand(.75, 1.)
        #print(scale)
        if new_ar < 1:
            nh = int(scale * ih)
            nw = int(nh * new_ar)
        else:
            nw = int(scale * iw)
            nh = int(nw / new_ar)
        image = image.resize((nw, nh), Image.BICUBIC)

        # 放置图片
        dx = int(self.rand(0, w - nw))
        dy = int(self.rand(0, h - nh))
        new_image = Image.new('RGB', (w, h),
                              (np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255)))
        new_image.paste(image, (dx, dy))
        image = new_image

        # 是否翻转图片
        flip = self.rand() < .3
        if flip:
            image = image.transpose(Image.FLIP_LEFT_RIGHT)

        # 色域变换
        hue = self.rand(-hue, hue)
        sat = self.rand(2-sat, sat)
        val = self.rand(2-val, val)
        x = cv2.cvtColor(np.array(image,np.float32)/255, cv2.COLOR_RGB2HSV)
        x[..., 0] += hue*360
        x[..., 0][x[..., 0]>1] -= 1
        x[..., 0][x[..., 0]<0] += 1
        x[..., 1] *= sat
        x[..., 2] *= val
        x[x[:,:, 0]>360, 0] = 360
        x[:, :, 1:][x[:, :, 1:]>1] = 1
        x[x<0] = 0
        image_data = cv2.cvtColor(x, cv2.COLOR_HSV2RGB)*255

        # 调整目标框坐标
        box_data = np.zeros((len(box), 5))
        if len(box) > 0:
            np.random.shuffle(box)
            box[:, [0, 2]] = box[:, [0, 2]] * nw / iw + dx
            box[:, [1, 3]] = box[:, [1, 3]] * nh / ih + dy
            if flip:
                box[:, [0, 2]] = w - box[:, [2, 0]]
            box[:, 0:2][box[:, 0:2] < 0] = 0
            box[:, 2][box[:, 2] > w] = w
            box[:, 3][box[:, 3] > h] = h
            box_w = box[:, 2] - box[:, 0]
            box_h = box[:, 3] - box[:, 1]
            box = box[np.logical_and(box_w > 1, box_h > 1)]  # 保留有效框
            box_data = np.zeros((len(box), 5))
            box_data[:len(box)] = box

        return image_data, box_data

    def get_random_data_with_Mosaic(self, annotation_line, input_shape, hue=.1, sat=1.5, val=1.5):
        h, w = input_shape
        min_offset_x = 0.3
        min_offset_y = 0.3
        scale_low = 1 - min(min_offset_x, min_offset_y)
        scale_high = scale_low + 0.2

        image_datas = []
        box_datas = []
        index = 0

        place_x = [0, 0, int(w * min_offset_x), int(w * min_offset_x)]
        place_y = [0, int(h * min_offset_y), int(h * min_offset_y), 0]
        for line in annotation_line:
            # 每一行进行分割
            line_content = line.split()
            # 打开图片
            image = Image.open(line_content[0])
            image = image.convert("RGB")
            # 图片的大小
            iw, ih = image.size
            # 保存框的位置
            box = np.array([np.array(list(map(int, box.split(',')))) for box in line_content[1:]])

            # 是否翻转图片
            flip = self.rand() < .5
            if flip and len(box) > 0:
                image = image.transpose(Image.FLIP_LEFT_RIGHT)
                box[:, [0, 2]] = iw - box[:, [2, 0]]

            # 对输入进来的图片进行缩放
            new_ar = w / h
            scale = self.rand(scale_low, scale_high)
            if new_ar < 1:
                nh = int(scale * h)
                nw = int(nh * new_ar)
            else:
                nw = int(scale * w)
                nh = int(nw / new_ar)
            image = image.resize((nw, nh), Image.BICUBIC)

            # 进行色域变换
            hue = self.rand(-hue, hue)
            sat = self.rand(1, sat) if self.rand() < .5 else 1 / self.rand(1, sat)
            val = self.rand(1, val) if self.rand() < .5 else 1 / self.rand(1, val)
            x = cv2.cvtColor(np.array(image,np.float32)/255, cv2.COLOR_RGB2HSV)
            x[..., 0] += hue*360
            x[..., 0][x[..., 0]>1] -= 1
            x[..., 0][x[..., 0]<0] += 1
            x[..., 1] *= sat
            x[..., 2] *= val
            x[x[:,:, 0]>360, 0] = 360
            x[:, :, 1:][x[:, :, 1:]>1] = 1
            x[x<0] = 0
            image = cv2.cvtColor(x, cv2.COLOR_HSV2RGB) # numpy array, 0 to 1

            image = Image.fromarray((image * 255).astype(np.uint8))
            # 将图片进行放置，分别对应四张分割图片的位置
            dx = place_x[index]
            dy = place_y[index]
            new_image = Image.new('RGB', (w, h),
                                  (np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255)))
            new_image.paste(image, (dx, dy))
            image_data = np.array(new_image)

            index = index + 1
            box_data = []
            # 对box进行重新处理
            if len(box) > 0:
                np.random.shuffle(box)
                box[:, [0, 2]] = box[:, [0, 2]] * nw / iw + dx
                box[:, [1, 3]] = box[:, [1, 3]] * nh / ih + dy
                box[:, 0:2][box[:, 0:2] < 0] = 0
                box[:, 2][box[:, 2] > w] = w
                box[:, 3][box[:, 3] > h] = h
                box_w = box[:, 2] - box[:, 0]
                box_h = box[:, 3] - box[:, 1]
                box = box[np.logical_and(box_w > 1, box_h > 1)]
                box_data = np.zeros((len(box), 5))
                box_data[:len(box)] = box

            image_datas.append(image_data)
            box_datas.append(box_data)

        # 将图片分割，放在一起
        cutx = np.random.randint(int(w * min_offset_x), int(w * (1 - min_offset_x)))
        cuty = np.random.randint(int(h * min_offset_y), int(h * (1 - min_offset_y)))

        new_image = np.zeros([h, w, 3])
        new_image[:cuty, :cutx, :] = image_datas[0][:cuty, :cutx, :]
        new_image[cuty:, :cutx, :] = image_datas[1][cuty:, :cutx, :]
        new_image[cuty:, cutx:, :] = image_datas[2][cuty:, cutx:, :]
        new_image[:cuty, cutx:, :] = image_datas[3][:cuty, cutx:, :]

        # 对框进行进一步的处理
        new_boxes = np.array(merge_bboxes(box_datas, cutx, cuty))

        return new_image, new_boxes

    def __getitem__(self, index):
        lines = self.train_lines
        n = self.train_batches
        index = index % n
        if self.mosaic:
            if self.flag and (index + 4) < n:
                img, y = self.get_random_data_with_Mosaic(lines[index:index + 4], self.image_size[0:2])
            else:
                img,ori, y = self.get_random_data(lines[index], self.image_size[0:2], random_=self.is_train)
            self.flag = bool(1-self.flag)
        else:
            img,ori, y = self.get_random_data(lines[index], self.image_size[0:2], random_=self.is_train)
        
        # 使用 jpg 和 png 两种方式模拟 拍照图片质量压缩增强
#         if random.random() < 0.5:
#             flag = random.randint(0,100000000)
#             cv2.imwrite("./train_vis/{}.jpg".format(flag),img,[cv2.IMWRITE_JPEG_QUALITY,random.randint(20,100)])
#             img = cv2.imread("./train_vis/{}.jpg".format(flag))
#             os.remove("./train_vis/{}.jpg".format(flag))
#         else:
#             flag = random.randint(0,100000000)
# #             cv2.imwrite("./train_vis/{}.jpg".format(flag),img,[cv2.IMWRITE_JPEG_QUALITY,random.randint(15,100)])
#             cv2.imwrite("./train_vis/{}.png".format(flag), img, [int(cv2.IMWRITE_PNG_COMPRESSION), random.randint(0,9)])
#             img = cv2.imread("./train_vis/{}.png".format(flag))
#             os.remove("./train_vis/{}.png".format(flag))
            
        img_vis = img.copy()
        if len(y) != 0:
            # 从坐标转换成0~1的百分比
            
            boxes_vis = np.array(y[:, :4], dtype=np.int32).copy()
            boxes = np.array(y[:, :4], dtype=np.float32)
            boxes[:, 0] = boxes[:, 0] / self.image_size[1]
            boxes[:, 1] = boxes[:, 1] / self.image_size[0]
            boxes[:, 2] = boxes[:, 2] / self.image_size[1]
            boxes[:, 3] = boxes[:, 3] / self.image_size[0]

            boxes = np.maximum(np.minimum(boxes, 1), 0)
            boxes[:, 2] = boxes[:, 2] - boxes[:, 0]
            boxes[:, 3] = boxes[:, 3] - boxes[:, 1]

            boxes[:, 0] = boxes[:, 0] + boxes[:, 2] / 2
            boxes[:, 1] = boxes[:, 1] + boxes[:, 3] / 2
            y = np.concatenate([boxes, y[:, -1:]], axis=-1)
            for bb in boxes_vis:
                img_vis = cv2.rectangle(img_vis,(bb[0],bb[1]),(bb[2],bb[3]),(255,0,0),2)
        label = random.randint(0,100)
        cv2.imwrite('./train_vis/vis_{}.jpg'.format(label),img_vis)
        cv2.imwrite('./train_vis/vis_{}_ori.jpg'.format(label),ori)
        
#         img = img[:,:,np.newaxis]
#         ori = ori[:,:,np.newaxis]

        img = np.array(img, dtype=np.float32)
        ori = np.array(ori, dtype=np.float32)

        tmp_inp = np.transpose(img / 255.0, (2, 0, 1))
        tmp_ori = np.transpose(ori / 255.0, (2, 0, 1))
        tmp_targets = np.array(y, dtype=np.float32)
        return tmp_inp,tmp_ori, tmp_targets


# DataLoader中collate_fn使用
def yolo_dataset_collate(batch):
    images = []
    oris = []
    bboxes = []
    for img,ori, box in batch:
        images.append(img)
        oris.append(ori)
        bboxes.append(box)
    images = np.array(images)
    oris = np.array(oris)
    return images,oris, bboxes

