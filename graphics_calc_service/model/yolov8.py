import torch
import torch.cuda
from ultralytics import YOLO
import numpy as np

class v8_paint:
    def __init__(self, model_path):
        # Load a model

        self.model = YOLO(model_path)  # pretrained YOLOv8n model
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
    def run(self, image):
        # Run batched inference on a list of images
        # /home/<USER>/code/answer_det/paint_item_yolo8/test/455211-618441-62692599.jpg
        results = self.model(image,imgsz=640,conf=0.25, device=self.device)  # return a list of Results objects

        # Process results list
        list_cls = []
        for result in results:
            boxes = result.boxes  # Boxes object for bounding box outputs
            xyxy = (boxes.xyxy).cpu().numpy()
            cls = (boxes.cls).cpu().numpy()

            result_arr = np.concatenate((xyxy, cls[:, None]), axis=1).astype('int32')
            list_cls.append(result_arr)

        return np.concatenate(list_cls)