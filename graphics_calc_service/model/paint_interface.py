import os
import shutil
import numpy as np
from PIL import Image

from base_common import Constants
import cv2
from graphics_calc_service.model.yolov8 import v8_paint

'''
/** 圆 */
Circle = 'circle',
/** 勾 */
Tick = 'tick',
/** 叉 */
Cross = 'cross',
/** 斜线 */
Backslash = 'backslash',
/** 下滑线 */
Underline = 'underline',
/** 空 */
Empty = 'empty',
/** 三角形 */
Triangle = 'triangle',
/** 正方形 */
Square = 'square',
/** 五角星 */
FivePointedStar = 'fivePointedStar',
/** 菱形 */
Rhombus = 'rhombus',
}
'''
'''
模型训练标签
names:
  0: quan
  1: hengxian
  2: xiexian
  3: gou
  4: cha
  5: sanjiao
  6: fangxing
  7: wujiaoxing
  8: aixin
  9: lingxing
  10: qita
'''

labels_word = ['circle', 'line', 'slash', 'right', 'wrong', 'sanjiao', 'fangxing', 'wujiaoxing', 'lingxing',
               'other']
users_word = ['circle', 'underline', 'backslash', 'tick', 'cross', 'triangle', 'square', 'fivePointedStar', 'rhombus',
              'other']

from base_common import LoggerFactory

log = LoggerFactory.get_logger('Paint log:')


def NMS(dets, confs, thresh=0.5):
    #     print(confs)
    # 首先数据赋值和计算对应矩形框的面积
    # dets的数据格式是dets[[xmin,ymin,xmax,ymax,scores]....]
    scores = confs
    x1 = dets[:, 0]
    y1 = dets[:, 1]
    x2 = dets[:, 2]
    y2 = dets[:, 3]
    areas = (y2 - y1 + 1) * (x2 - x1 + 1)

    # 这边的keep用于存放，NMS后剩余的方框
    keep = []

    # 取出分数从大到小排列的索引。.argsort()是从小到大排列，[::-1]是列表头和尾颠倒一下。
    index = scores.argsort()[::-1]
    # 上面这两句比如分数[0.72 0.8  0.92 0.72 0.81 0.9 ]
    #  对应的索引index[  2   5    4     1    3   0  ]记住是取出索引，scores列表没变。

    # index会剔除遍历过的方框，和合并过的方框。
    while index.size > 0:
        # 取出第一个方框进行和其他方框比对，看有没有可以合并的
        i = index[0]  # every time the first is the biggst, and add it directly

        # 因为我们这边分数已经按从大到小排列了。
        # 所以如果有合并存在，也是保留分数最高的这个，也就是我们现在那个这个
        # keep保留的是索引值，不是具体的分数。
        keep.append(i)
        # 计算交集的左上角和右下角
        # 这里要注意，比如x1[i]这个方框的左上角x和所有其他的方框的左上角x的
        x11 = np.maximum(x1[i], x1[index[1:]])  # calculate the points of overlap
        y11 = np.maximum(y1[i], y1[index[1:]])
        x22 = np.minimum(x2[i], x2[index[1:]])
        y22 = np.minimum(y2[i], y2[index[1:]])

        # 这边要注意，如果两个方框相交，X22-X11和Y22-Y11是正的。
        # 如果两个方框不相交，X22-X11和Y22-Y11是负的，我们把不相交的W和H设为0.
        w = np.maximum(0, x22 - x11 + 1)
        h = np.maximum(0, y22 - y11 + 1)

        # 计算重叠面积就是上面说的交集面积。不相交因为W和H都是0，所以不相交面积为0
        overlaps = w * h

        # 这个就是IOU公式（交并比）。
        # 得出来的ious是一个列表，里面拥有当前方框和其他所有方框的IOU结果。
        #         ious = overlaps / (areas[i]+areas[index[1:]] - overlaps)
        #         print('nms......: ' , areas[i],areas[index[1:]] , overlaps)
        ious = overlaps / np.minimum(areas[i], areas[index[1:]])

        # 接下来是合并重叠度最大的方框，也就是合并ious中值大于thresh的方框
        # 我们合并的操作就是把他们剔除，因为我们合并这些方框只保留下分数最高的。
        # 我们经过排序当前我们操作的方框就是分数最高的，所以我们剔除其他和当前重叠度最高的方框
        # 这里np.where(ious<=thresh)[0]是一个固定写法。
        idx = np.where(ious <= thresh)[0]

        # 把留下来框在进行NMS操作
        # 这边留下的框是去除当前操作的框，和当前操作的框重叠度大于thresh的框
        # 每一次都会先去除当前操作框，所以索引的列表就会向前移动移位，要还原就+1，向后移动一位
        index = index[idx + 1]  # because index start from 1
    return dets[keep]


def calc_iou(bbox1, bbox2):
    if not isinstance(bbox1, np.ndarray):
        bbox1 = np.array(bbox1)
    if not isinstance(bbox2, np.ndarray):
        bbox2 = np.array(bbox2)
    xmin1, ymin1, xmax1, ymax1, = np.split(bbox1, 4, axis=-1)
    xmin2, ymin2, xmax2, ymax2, = np.split(bbox2, 4, axis=-1)

    area1 = (xmax1 - xmin1) * (ymax1 - ymin1)
    area2 = (xmax2 - xmin2) * (ymax2 - ymin2)

    ymin = np.maximum(ymin1, np.squeeze(ymin2, axis=-1))
    xmin = np.maximum(xmin1, np.squeeze(xmin2, axis=-1))
    ymax = np.minimum(ymax1, np.squeeze(ymax2, axis=-1))
    xmax = np.minimum(xmax1, np.squeeze(xmax2, axis=-1))

    h = np.maximum(ymax - ymin, 0)
    w = np.maximum(xmax - xmin, 0)
    intersect = h * w

    iou_1 = intersect / area1
    iou_2 = intersect / area2.reshape(1, -1)
    ious_ = np.stack((iou_1, iou_2), 0)
    ious = ious_.max(0)
    return ious
        
'''
def calc_iou(bbox1, bbox2):
    if not isinstance(bbox1, np.ndarray):
        bbox1 = np.array(bbox1)
    if not isinstance(bbox2, np.ndarray):
        bbox2 = np.array(bbox2)
    xmin1, ymin1, xmax1, ymax1, = np.split(bbox1, 4, axis=-1)
    xmin2, ymin2, xmax2, ymax2, = np.split(bbox2, 4, axis=-1)

    area1 = (xmax1 - xmin1) * (ymax1 - ymin1)
    area2 = (xmax2 - xmin2) * (ymax2 - ymin2)

    ymin = np.maximum(ymin1, np.squeeze(ymin2, axis=-1))
    xmin = np.maximum(xmin1, np.squeeze(xmin2, axis=-1))
    ymax = np.minimum(ymax1, np.squeeze(ymax2, axis=-1))
    xmax = np.minimum(xmax1, np.squeeze(xmax2, axis=-1))

    h = np.maximum(ymax - ymin, 0)
    w = np.maximum(xmax - xmin, 0)
    intersect = h * w

    union = area1 + np.squeeze(area2, axis=-1) - intersect
    return intersect / union
'''


def filter_overlapping_results_with_iou(result_f, iou_threshold=0.4):
    """
        过滤同一位置的多个预测结果。
        如果同一位置存在多个结果，一对一错，则只保留对的结果。
    """
    box_results = result_f['types']
    box_coords = result_f['boxs']
    processed = set()

    box_ids = list(box_coords.keys())
    boxes = np.array([box_coords[box_id] for box_id in box_ids])

    # 遍历所有框，计算 IoU
    for i, box_id1 in enumerate(box_ids):
        if box_id1 in processed:
            continue

        overlapping_ids = []
        for j, box_id2 in enumerate(box_ids):
            if box_id1 != box_id2 and box_id2 not in processed:
                # 计算 IoU
                iou = calc_iou([boxes[i]], [boxes[j]])  # 直接传递每个框
                if iou[0][0] > iou_threshold:
                    overlapping_ids.append(box_id1)
                    overlapping_ids.append(box_id2)

        if len(overlapping_ids) != 0:
            id_1 = overlapping_ids[0]
            id_2 = overlapping_ids[1]
            result_1 = box_results[id_1]
            result_2 = box_results[id_2]
            # 如果一个是10另一个是11，且 box_results 的值为10和11，修改11的值为-1
            if (result_1 == 10 and result_2 == 11) or (result_1 == 11 and result_2 == 10):
                # 如果是 11 的框
                if result_1 == 11:
                    result_f['types'][id_1] = -1
                elif result_2 == 11:
                    result_f['types'][id_2] = -1
                return result_f
            elif result_1 == 11 and result_2 == 11:
                result_f['types'][id_1] = -1
                return result_f

    return result_f


# 结果说明： 0：正确且打勾 1：正确但是忽略 2：错误打叉 -1：有答案但是未检测到作答
def judge_answer(answers, preds, item_start=(0, 0)):
    b0, b1 = item_start
    result_f = {'boxs': {}, 'types': {}}  # 记录最后答案.格式为{box_id:ans_type,...}

    boxs = []  # 统计标准答案位置；同时记录返回检测框。如果检测到答案，则替换为答案位置，如果没有匹配到答案，则返回其本身的位置
    results = [''] * len(answers)  # 每个作答位置对应的预测答案
    gt_types = []  # 每个作答位置对应的标准答案
    for ans in answers:
        boxs.append(ans['boxs'])
        gt_types.append(ans['ans_type'])

    pred_boxs = []  # 预测结果boxs
    pred_labels = []  # 预测labels
    for pred in preds:
        # 爱心 和 其他暂时不批改
        if pred[4] in [8, 10]:
            continue
        pred_labels.append(labels_word[pred[4]])
        if labels_word[pred[4]] == 'line':  # 针对下划线的预测，进行向上扩充补偿
            h = pred[3] - pred[1]
            pred_boxs.append([pred[0], pred[1] - h, pred[2], pred[3]])
        elif labels_word[pred[4]] == 'right':  # 针对对勾的预测，进行向下压缩补偿
            h = pred[3] - pred[1]
            pred_boxs.append([pred[0], pred[1] + int(h / 4), pred[2], pred[3]])
        else:
            pred_boxs.append(pred[:4])
    #     print(pred_boxs,boxs)
    if len(boxs) == 0:
        return result_f
    if len(pred_boxs) != 0:
        ious = calc_iou(pred_boxs, boxs)  # 计算预测结果和gtbox之间的ious
        #log.info(f"IoU between {pred_boxs} and {boxs}: {ious}")
        
        len_pred, len_gt = ious.shape

        while True:  # 按照iou从大到小排序
            ind_max = np.argmax(ious)  # 找到最大的iou值及其坐标
            iou_max = np.max(ious)
            if iou_max < 0.1:  # 当iou小于0.5时，不进行匹配
                break

            pred_ind_max = ind_max // len_gt
            gt_ind_max = ind_max % len_gt
            ious[pred_ind_max, :] = 0.  # 对处理过的iou位置赋值0，循环进行第二大的iou处理，直到小于0.5时打断
            ious[:, gt_ind_max] = 0.
            if results[gt_ind_max] == '':  # 如果为‘’，说明没有该gt没有匹配，则进行匹配。如果不为''，说明已经和更大的iou匹配，则进行跳过
                if pred_labels[pred_ind_max] == 'right':
                    p_box = pred_boxs[pred_ind_max]
                    g_box = boxs[gt_ind_max]
                    #if p_box[3] < (g_box[1] + g_box[3]) / 2.:
                    #    continue
                results[gt_ind_max] = pred_labels[pred_ind_max]
                boxs[gt_ind_max] = pred_boxs[pred_ind_max]
    #     print(results,gt_types)
    #     ['', 'right', 'right', '', '', 'right', '', 'right', 'right', '', 'right', '', '', '', 'right', '', '', 'right']
    #     ['', 'right', 'circle', '', '', 'right', '', 'right', 'right', '', 'right', '', '', '', 'right', '', '', 'right']

    for i in range(len(answers)):
        bb = boxs[i]
        pad_b = [b0, b1, b0, b1]
        b_f = [int(bb[k] + pad_b[k]) for k in range(4)]
        result_f['boxs'][answers[i]['box_id']] = b_f
        result_f['types'][answers[i]['box_id']] = -1

        if gt_types[i] == 'empty':
            if results[i] == '':
                result_f['types'][answers[i]['box_id']] = -1  # 标准答案为空，且没有匹配到检测答案时，正确，但是不会打勾
            else:
                result_f['types'][answers[i]['box_id']] = 11  # 标准答案为空，且匹配到检测答案时，错误，打叉
        else:
            if results[i] == '':
                result_f['types'][answers[i]['box_id']] = -2  # 标准答案不为空，且没有匹配到检测答案时，错误，未检测到答案处理

            # else:
            # result_f['types'][answers[i]['box_id']] = 10

            elif users_word.index(gt_types[i]) == labels_word.index(results[i]):
                result_f['types'][answers[i]['box_id']] = 10  # 标准答案不为空，且匹配到检测答案与标准答案一致，正确
            else:
                if gt_types[i] == 'tick' and results[i] == 'slash':
                    result_f['types'][answers[i]['box_id']] = 10  # 答案为勾，但是识别为斜线，也算对

                if gt_types[i] == 'underline' and results[i] == 'slash':
                    result_f['types'][answers[i]['box_id']] = 10  # 答案为下划线，但是识别为钩，也算对

                else:
                    result_f['types'][answers[i]['box_id']] = 11  # 标准答案不为空，且匹配到检测答案与标准答案不一致，错误

    log.info(f'result_f:{result_f}')
    return filter_overlapping_results_with_iou(result_f)


class Yolo_Det_Paint():
    def __init__(self, model_path, vis_paint):
        self.vis_paint = vis_paint
        if self.vis_paint:
            self.vis_dir = f'{Constants.VIS_PATH}/vis_paint/'
            if os.path.exists(self.vis_dir):
                shutil.rmtree(self.vis_dir)
            os.mkdir(self.vis_dir)
        self.yolo = v8_paint(model_path)

    def run(self, image, answers, item_start=(0, 0), img_name='', index=0):
        # answers:[{'box_id':str,'boxs':list,'ans_type':'str'},...]
        results = self.yolo.run(image)
        #log.info(f'results_: {results}')
        #if len(results) == 0:
        #    return judge_answer(answers, results, item_start)

        # results_ = NMS(results, top_conf)  # 同一个位置保留一个类型的box

        if self.vis_paint:
            # 储存作图题的结果，用来进行验证判断
            img = cv2.cvtColor(np.asarray(image), cv2.COLOR_RGB2BGR)
            for resu in results:
                cv2.rectangle(img, (resu[0], resu[1]), (resu[2], resu[3]), (0, 255, 0), 1)
                font = cv2.FONT_HERSHEY_SIMPLEX
                cv2.putText(img, users_word[resu[4]], (resu[0], resu[1]), font, 1, (0, 255, 0), 1)
            for ans in answers:
                cv2.rectangle(img, (ans['boxs'][0], ans['boxs'][1]), (ans['boxs'][2], ans['boxs'][3]), (0, 0, 255), 1)
                font = cv2.FONT_HERSHEY_SIMPLEX
                cv2.putText(img, ans['ans_type'], (ans['boxs'][0], ans['boxs'][1]), font, 1, (0, 0, 255), 1)

            cv2.imwrite(self.vis_dir + '{}_{}.jpg'.format(img_name.split('.')[0], index), img)

        # if len(results) == 0:
        #     return None
        final_results = judge_answer(answers, results, item_start)
        return final_results
