# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_vertical_calculation.py
import json
import traceback

from PIL import Image
import numpy as np
from embedding_service.models.eva02 import EvaNet
import torchvision.transforms as T
import torch

from base_common import Constants, LoggerFactory, MissionMode
from base_common.service.service_of_base import BaseModelService

log = LoggerFactory.get_logger('EmbeddingService')

class EmbeddingService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.EMBEDDING_MISSION2
        # 设备选择
        dev = "cuda"
        self.device = torch.device(dev)

        # 加载新模型
        self.model_emb = EvaNet()
        self.model_emb.to(self.device)
        self.model_emb.eval()
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/embedding_service/model_best_0.989.pth'
        # 加载模型权重
        model_dict = torch.load(model_path, map_location=self.device)["model"]

        self.model_emb.load_state_dict(model_dict, strict=True)
        log.info("Model successfully loaded")

        # 定义图像预处理变换
        self.transforms = T.Compose(
            [
                T.Resize(size=(512, 512)),
                T.ToTensor(),
                T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]
        )

    def do_post(self, data_json):
        try:
            imgs = data_json['imgs']
            log.info(f"{json.dumps(data_json)}")
            inputs = []
            for data in imgs:
                inputs.append(self.get_image(data))
            embeddings = []

            for img in inputs:
                img = Image.fromarray(img)
                img = self.transforms(img).unsqueeze(0).to(self.device)  # 应用预处理变换并添加批次维度
                with torch.no_grad():
                    embedding = self.model_emb(img).data.cpu().numpy()
                embeddings.append(np.transpose(embedding))
            # 返回嵌入结果
            embeddings = [embedding.tolist() for embedding in embeddings]
            return embeddings
        except:
            log.error(f'Error in processing: {traceback.format_exc()}')
            return []
