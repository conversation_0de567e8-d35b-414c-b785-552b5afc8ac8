# export HF_ENDPOINT="https://hf-mirror.com"
# python eva02.py

import os
import torch
import torch.nn.functional as F
from torch import nn
import timm
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

class EvaNet(nn.Module):
    def __init__(self):
        super().__init__()
        self.model = timm.create_model('efficientnet_b3a', pretrained=False, num_classes=0, global_pool='')
        self.head = nn.Linear(1536, 512) #1536,768
        
        
    def logits(self, features):
        x = F.adaptive_max_pool2d(features, output_size=1)
        x = x.view(x.size(0), -1)
        return x
        
    def forward(self, x):
        x = self.model(x)
        x = self.logits(x)
        x = self.head(x)
        x = F.normalize(x, p=2, dim=1)
        
        return x
        
if __name__ == "__main__":
    
    model = EvaNet()
    
    inputs = torch.randn((5,3,448,448))# 448,512
    outputs = model(inputs)
    print(outputs.size())