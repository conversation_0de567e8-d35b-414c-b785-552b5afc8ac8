import torch
print(torch.__version__)
print(torch.cuda.is_available())
print('md_v4_b')
import transformers
print(transformers.__version__)

from transformers import TrOCRProcessor, VisionEncoderDecoderModel
import requests
import os
from PIL import Image

def hashf(fn):
    import sys
    import hashlib
    BUF_SIZE = 65536  # lets read stuff in 64kb chunks!
    
    sha256_hash = hashlib.sha256()
    #sha1 = hashlib.sha1()
    
    with open(fn, 'rb') as f:
        while True:
            data = f.read(BUF_SIZE)
            if not data:
                break
            sha256_hash.update(data)
            #sha1.update(data)
    print(sha256_hash.hexdigest())
    return sha256_hash.hexdigest()

'''
print(VisionEncoderDecoderModel)
path = os.path.dirname(transformers.__file__)
print(path)
'''

from io import BytesIO
import base64

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(device)
#mdpsspath ='./trocr_sm2/'# './load_ckpt/'
#psspath =  './trocr_sm2/' # './trocr_base_handwritten/' #

class trocr_hw_en:
    def __init__(self,num_beams=1):
        self.cel = torch.nn.CrossEntropyLoss()
        self.crossentropyloss=torch.nn.CrossEntropyLoss()
        self.num_beams=num_beams
        
    def load_model(self,mdpsspath ='./sm595/',psspath =  './sm595/'):
        print('--load_model',mdpsspath,psspath)
        print('* trocr_sm2 sha256 should be aa1135ff78f2609e88a154f67792350fe6eb72ba3ea96d9ce63c50d21f389966')
        self.processor = TrOCRProcessor.from_pretrained(psspath, local_files_only=True) 
        self.model = VisionEncoderDecoderModel.from_pretrained(mdpsspath, local_files_only=True).to(device)
        #self.model = VisionEncoderDecoderModel.from_pretrained(mdpsspath, local_files_only=True, output_hidden_states=True).to(device)
        try:
            hashf(os.path.join(psspath,'pytorch_model.bin'))
        except Exception as e:
            print('--hash failed',e)
    
    def load_img_batch(self,dt):#dt=list  [[dt,ttype]]
        print('-load_img_batch num',len(dt))
        imgs=[]
        for q in dt:
            ttype=q[1]
            img=q[0]
            image=0
            if(ttype=='base64'):
                image = Image.open(BytesIO(base64.b64decode(img))).convert("RGB")
            elif(ttype=='url'):
                image = Image.open(requests.get(img, stream=True).raw).convert("RGB")
                print('img got',img)
            elif(ttype=='pil'):
                image = img.convert("RGB")#....
            else:
                print( '!!trocr_en_load_img--error--')
                return -1
            imgs.append(image)
        self.pixel_values = self.processor(imgs, return_tensors="pt").pixel_values.to(device)

        return 0
    
    def run_batch(self,return_loss=True):
        
        generated_ids=self.model.generate(self.pixel_values,
                                          #num_return_sequences=1,
                                          num_beams=self.num_beams,
                                          output_scores=return_loss,
                                          return_dict_in_generate=return_loss,
                                          use_cache=True,
                                          )

        
        #print(generated_ids)
        
        generated_text = []
        if(return_loss):
            generated_text=self.processor.batch_decode(generated_ids.sequences, skip_special_tokens=True)
        else:
            generated_text=self.processor.batch_decode(generated_ids, skip_special_tokens=True)
            
        print('-run_batch',generated_text)
        
        
        loss=[]
        if(return_loss):
            sc=generated_ids.scores
            
            #print('------sc',sc)
            
            
            for q in range(len(sc[0])):
                logits=[]
                #print(sc[q].shape)
                #print(generated_ids.sequences[q])
                for w in range(len(generated_ids.sequences[q])):
                    if(generated_ids.sequences[q][w]==2): # 2 -> end
                        break
                    logits.append(sc[w][q])
                    
                    
                
                logits = torch.stack(logits,0)
                #print('----logits',logits)

                labels=torch.argmax(logits, dim=1)
                #print(labels)
                
                qloss = self.cel(logits,labels)
                
                loss.append(qloss.mean().cpu().item())
                
            print('-generate loss',loss)
        
        return generated_text,loss
    
    def bt_cp_loss(self,cplabels):
        bt_label=[]
        for q in cplabels:
            labels = self.processor.tokenizer(
                    q, padding="max_length",
                    max_length=128).input_ids
            labels = [
                label if label != self.processor.tokenizer.pad_token_id else -100
                for label in labels
            ]
            bt_label.append(labels)
            #bt_label=[labels]
        
        labels=torch.tensor(bt_label).to(device)
        
        self.model.eval()
        with torch.no_grad():
            #print(type(self.pixel_values),self.pixel_values)
            #print(type(labels),labels)
            #outputs = self.model.generate(pixel_values=self.pixel_values,labels=labels,output_scores=True,return_dict_in_generate=True)#,num_beams=1)
            outputs = self.model(pixel_values=self.pixel_values,labels=labels)#, use_cache=True  )#,num_beams=1)
            
        #print(outputs)
        #batchloss = outputs.loss
        
        lossli=[]
        ###########################
        
        for w in range(len(labels)):
            lgtp=[]
            lbtp=[]
            for q in range(len(labels[w])):

                if(labels[w][q]==-100):#!!!care
                    #lg.append(lgtp)
                    #lb.append(lbtp)
                    break
                lgtp.append(outputs.logits[w][q])
                lbtp.append(labels[w][q])
            
        
        
            lgcp = torch.stack(lgtp,0).to(device)
            lbcp = torch.tensor(lbtp).to(device)
            
            #print(lgcp)
            #print(lbcp)
            celoss = self.crossentropyloss(lgcp,lbcp)
            torch.set_printoptions(precision=12)
            #print('-celoss',celoss)
            lossli.append(celoss.cpu().item())

        print('-bt loss',lossli)
        ##########################
        
        return lossli
    

#####################################################
import sklearn
from sklearn.svm import LinearSVC
from sklearn.pipeline import make_pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.datasets import make_classification
print('sklearn version (1.3.2 is ok) ',sklearn.__version__)
#pip install -U scikit-learn==1.3.2

import json
class svmlin:
    def __init__(self,fn='ocren_svm_data.json'):
        self.fn = fn
        print(os.path.abspath(fn))
        f=open(fn,'r')
        self.d=json.load(f)
        f.close()
        clf = make_pipeline(StandardScaler(),
                    LinearSVC(dual="auto", random_state=0, tol=1e-5))
        clf.fit(self.d['X'], self.d['y'])
        #print(X,y)  [gtt[q],prsvm[q]]
        print(clf.named_steps['linearsvc'].coef_)
        print(clf.named_steps['linearsvc'].intercept_)
        print(clf.predict([[3, 1],[1.1,1]]))
        self.clf=clf
        print('--svmlin loaded')
 
    # Sample Method
    def run(self,dt):#dt=([loss,toploss]
        #1 correct 0 wrong
        if(isinstance(dt, list)):
            pass
        elif(isinstance(dt, dict)):
            dt=[dt['toploss'][0],dt['loss'][0]-0.3]
        else:
            print('svm dt type error')
            return [0]
            
        return self.clf.predict([dt]).tolist()[0]
        


def testbt(ipt,asw):
    global trmd
    imgbt=[]
    for q in range(len(ipt)):
        #trmd.load_img(url,ttype='url')
        pilimg=Image.open(ipt[q]).convert("RGB")
        imgbt.append([pilimg,'pil'])

    trmd.load_img_batch(imgbt)
    trmd.run_batch()
    trmd.bt_cp_loss(asw)
    print('---asw',asw)
#######################################################
