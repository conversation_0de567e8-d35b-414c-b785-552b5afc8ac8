# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_vertical_calculation.py
import traceback

from base_common import Context, Constants, LoggerFactory, MissionMode
from base_common.service.service_of_base import BaseModelService
from en_hw_ocr_service.model.md_v4 import trocr_hw_en, svmlin
log = LoggerFactory.get_logger('EnHwService')

class EnHwService(BaseModelService):

    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.EN_HW_OCR_MISSION
        log.info('---init startup')
        self.bs = 6 if Context.is_ecs() else 8
        self.trmd = trocr_hw_en(num_beams=1)
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/en_hw_ocr_service/'
        self.trmd.load_model(model_path, model_path)
        log.info('--model loaded')
        self.svm = svmlin(f'{model_path}ocren_svm_data.json')

        # update bs
        log.info('batchsize', self.bs)
        log.info('---init end')

    def do_post(self, data_json):
        try:
            out = []
            try:
                _, out, detail = self.run_me(data_json)
            except Exception as e:
                log.error('error run.', e)
                out = ['' for q in data_json['the_input']]
            if 'detail' in data_json:
                return detail
            return out
        except Exception as e:
            log.error(f'--????? def error {traceback.format_exc()}')

    def run_me(self, js, returndetail=True):
        out = []
        asc = []
        anslist = []
        ori_predict = []
        task = {'imgs': [], 'ans': []}
        for i in range(len(js['the_input'])):

            q = js['the_input'][i]
            ans = ''

            try:
                ei = js['inputs_en_ids'][i]
                ans = js['answer'][ei]
            except Exception as e:
                log.info(f'--no correct ans{e}')

            anslist.append(ans)
            task['imgs'].append([q, 'base64'])
            task['ans'].append(ans)

        ith = 0
        n_task = len(task['imgs'])
        sploss = []
        scploss = []
        log.info(len(task['imgs']), len(task['ans']))
        while True:
            bnum = self.bs
            if ith >= n_task:
                break
            if ith + self.bs >= n_task:
                bnum = n_task - ith

            self.trmd.load_img_batch(task['imgs'][ith:ith + bnum])
            rt, ploss = self.trmd.run_batch()
            cploss = self.trmd.bt_cp_loss(task['ans'][ith:ith + bnum])
            ith += bnum
            sploss += ploss
            scploss += cploss
            ori_predict += rt
            out += rt

        for q in range(len(js['the_input'])):
            tf = self.svm.run([scploss[q], sploss[q]])
            log.info('--tpsvmtest', [scploss[q], sploss[q]], tf)

            asc.append(tf)
            if tf:
                out[q] = task['ans'][q]

        if returndetail:
            return ori_predict, out, {'loss': scploss, 'toploss': sploss, 'predicts': out, 'answer_check': asc,
                                      'ori_predicts': ori_predict, 'correct_ans': anslist}

        return ori_predict, out, {}
