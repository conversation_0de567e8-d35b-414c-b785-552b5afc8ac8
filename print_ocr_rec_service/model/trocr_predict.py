import torch
from transformers import TrOCRProcessor, VisionEncoderDecoderModel
from base_common import Constants

replace_chars = {'＞':'>','＜':'<','＝':'=','＋':'+','－':'-'}
remove_chars = ['(',')','=','{','}','[',']','+','%']
def decode_text(tokens, vocab, vocab_inp):
    ##decode trocr
    s_start = vocab.get('<s>')
    s_end = vocab.get('</s>')
    unk = vocab.get('<unk>')
    pad = vocab.get('<pad>')
    text = ''
    for tk in tokens:
        if tk not in [s_end, s_start , pad, unk]:
            text += vocab_inp[tk]
    return text

class Trocr_Predictor:
    def __init__(self, model_path):
        self.processor = TrOCRProcessor.from_pretrained(model_path)
        self.vocab = self.processor.tokenizer.get_vocab()
        self.vocab_inp = {self.vocab[key]: key for key in self.vocab}
        self.model = VisionEncoderDecoderModel.from_pretrained(model_path)
        self.model.eval()
        self.model.cuda()
        self.crossentropyloss = torch.nn.CrossEntropyLoss(reduction='none')

    def predict(self,imgs,labels=None):
        if labels is None:
            return self.predict_no_label(imgs)
        else:
            return self.predict_with_label(imgs,labels)
            
    def predict_no_label(self,imgs):
        pixel_values = self.processor(imgs, return_tensors="pt").pixel_values
        with torch.no_grad():
            generated_ids = self.model.generate(pixel_values[:, :, :].cuda(),num_return_sequences=1,num_beams=1,return_dict_in_generate=True,output_scores=True,early_stopping=False,length_penalty=1.0)
            #num_return_sequences输出几个预测结果，num_beams表示有几个备选预测，影响速度
        
        generated_texts = []
        for i in range(len(imgs)):
            generated_text = decode_text(generated_ids.sequences[i].cpu().numpy(), self.vocab, self.vocab_inp)
            generated_texts.append(generated_text)
        return generated_texts
        
    def get_label_loss(self,img,label):
        pixel_values = self.processor([img], return_tensors="pt").pixel_values            
        pixel_values = pixel_values.to('cuda:0')

        ##### model methode

        lbl = [-100]*128
        lbl[0] = 0
        for i,l in enumerate(label):
            try:
                lbl[i+1] = self.vocab[l]
            except:
                continue
        lbl[i+2] = 2
        tensor_labels = torch.tensor(lbl).unsqueeze(0).to('cuda:0')
        
        with torch.no_grad():
            outputs = self.model(pixel_values=pixel_values,labels=tensor_labels)
        return outputs.loss.cpu().item()

    def predict_loss(self,imgs):
    
        pixel_values = self.processor(imgs, return_tensors="pt").pixel_values
        with torch.no_grad():
            generated_ids = self.model.generate(pixel_values[:, :, :].cuda(),num_return_sequences=1,num_beams=1,return_dict_in_generate=True,output_scores=True,early_stopping=False,length_penalty=1.0)
            #num_return_sequences输出几个预测结果，num_beams表示有几个备选预测，影响速度
        
        generated_texts = []
        losses_mean = []
        for i in range(len(imgs)):
            generated_text = decode_text(generated_ids.sequences[i].cpu().numpy(), self.vocab, self.vocab_inp)
            generated_texts.append(generated_text)
            #print(generated_ids.sequences[i].cpu().numpy(),generated_text)
        
            logits = []
            for ii in range(len(generated_text)+2):
                logits.append(generated_ids.scores[ii][i])
            logits = torch.stack(logits,0).to('cuda:0')
            
            labels = [0]*(len(generated_text)+2)
            for it,tt in enumerate(generated_text):
                labels[it+1] = self.vocab[tt]
            labels[-1] = 2
            tensor_labels = torch.tensor(labels).to('cuda:0')
            
            loss = self.crossentropyloss(logits,tensor_labels)
            losses_mean.append(loss.mean().cpu().item())
        
        return generated_texts,losses_mean
        
    def judge_pred(self,label_loss_mean,pred_loss_mean,lbl):
        len_label_text = len(lbl)
        if label_loss_mean < pred_loss_mean and label_loss_mean> 1.5:
            return False
        if label_loss_mean > 2:
            return False
        if label_loss_mean < 0.1:
            return True
        
        times = (0.1/pred_loss_mean)**0.5 + 2
        return label_loss_mean/pred_loss_mean < times

        
    def predict_with_label(self,imgs,labels):
        pred_labels = []
        generated_texts,losses_mean = self.predict_loss(imgs)
        for i in range(len(imgs)):
            pred_text = generated_texts[i]
            #print('pred_text: ',pred_text)
            labels_i = labels[i].split('\t')
            
            get_right_answer = False
            for lbl in labels_i:
            
                if pred_text == lbl:
                    pred_labels.append(lbl)
                    get_right_answer = True
                    break
                    
                for kk in replace_chars.keys():#解决一些答案录入格式不同，影响模糊批改的判断。
                    lbl = lbl.replace(kk, replace_chars[kk])
                    pred_text = pred_text.replace(kk, replace_chars[kk])
                    
                if pred_text == lbl:
                    pred_labels.append(lbl)
                    get_right_answer = True
                    break

                
                # 处理识别结果前后多出一个印刷字符，remove_chars中的字符都是有概率被误识别的印刷体，可以根据实际情况增减。
                # 只有当remove_chars中的字符再首位时候才会删除
                if len(pred_text)>1:
                    pred_text_copy = pred_text+''
                    if_stop = False
                    for char in remove_chars:
                        if pred_text_copy.startswith(char):
                            if pred_text_copy[1:] == lbl:
                                pred_labels.append(lbl)
                                get_right_answer = True
                                if_stop = True
                                break
                            
                        if pred_text_copy.endswith(char):
                            if pred_text_copy[:-1] == lbl:
                                pred_labels.append(lbl)
                                get_right_answer = True
                                if_stop = True
                                break
                    if if_stop:
                        break

                pred_loss_mean = losses_mean[i]#第一预测答案的loss        
                label_loss_mean = self.get_label_loss(imgs[i],lbl)#期望答案的loss
                
                # judge=True认为可以模糊替换,else...
                if abs(len(lbl) - len(generated_texts[i])) >= 2:
                    judge = False
                else:
                    judge = self.judge_pred(label_loss_mean,pred_loss_mean,lbl)
                    #print(judge,lbl,label_loss_mean,generated_texts[i],pred_loss_mean)
                if judge :
                    pred_labels.append(lbl)
                    get_right_answer = True
                    break
                    
            if not get_right_answer:
                pred_labels.append(pred_text)
          
        return pred_labels
		
