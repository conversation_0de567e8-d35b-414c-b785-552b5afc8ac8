# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 9:12 
# @Description  : server_print_ocr_rec.py
import sys

from base_common import Context, LoggerFactory, Constants, ParamLoader
from print_ocr_rec_service.service.service_of_rec import PrintOcrRecService
log = LoggerFactory.get_logger('PrintOcrRecService')
if __name__ == '__main__':
    args = sys.argv[1:]
    params = ParamLoader(args)
    if not params.is_success():
        log.error('Usage: python xx.py <port> <gpu_num>')
        exit(1)

    Context.setup()
    Constants.setup(Context.is_product())
    Context.set_gpu_num(params.get_gpu_num())
    service = PrintOcrRecService()
    service.set_use_ai_lab(True)
    service.set_stop_time(params.get_stop_time())
    service.start(params.get_port())
