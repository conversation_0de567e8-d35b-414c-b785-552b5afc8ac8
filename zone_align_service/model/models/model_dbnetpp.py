from torch import nn
import torch.nn.functional as F

from zone_align_service.model.models.backbone.resnet import deformable_resnet18
from zone_align_service.model.models.seg_detector_asf import SegSpatialScaleDetector

class Model_Dbnetpp(nn.Module):
    def __init__(self, model_config: dict):
        super().__init__()
        self.backbone = deformable_resnet18(in_channels=3)
        self.decoder = SegSpatialScaleDetector()
        
        self.name = 'dbnet_pp'
    def forward(self, x):
        result = self.decoder(self.backbone(x))
#         print(result.size())
        return result