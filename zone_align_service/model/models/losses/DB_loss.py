# -*- coding: utf-8 -*-
# @Time    : 2019/8/23 21:56
# <AUTHOR> <PERSON><PERSON><PERSON>
from torch import nn
import torch

from zone_align_service.model.models.losses.basic_loss import BalanceCrossEntropyLoss, MaskL1Loss, DiceLoss


class DBLoss(nn.Module):
    def __init__(self, alpha=1.0, beta=10, ohem_ratio=3,loss2=False,focal_flag=False, reduction='mean', eps=1e-6):
        """
        Implement PSE Loss.
        :param alpha: binary_map loss 前面的系数
        :param beta: threshold_map loss 前面的系数
        :param ohem_ratio: OHEM的比例
        :param reduction: 'mean' or 'sum'对 batch里的loss 算均值或求和
        """
        super().__init__()
        assert reduction in ['mean', 'sum'], " reduction must in ['mean','sum']"
        self.alpha = alpha
        self.beta = beta
        self.loss2 = loss2
        self.bce_loss = BalanceCrossEntropyLoss(negative_ratio=ohem_ratio,focal_flag=focal_flag)
        self.dice_loss = Di<PERSON>Loss(eps=eps)
        self.l1_loss = MaskL1Loss(eps=eps)
        self.ohem_ratio = ohem_ratio
        self.reduction = reduction

    def forward(self, pred, batch):
        metrics = {}
        loss_shrink = torch.tensor(0.).cuda()
        loss_threshold = torch.tensor(0.).cuda()
        loss_binary = torch.tensor(0.).cuda()
        loss = torch.tensor(0.).cuda()
        
        shrink_maps = pred[:, 0, :, :]
        threshold_maps = pred[:, 1, :, :]
        binary_maps = pred[:, 0, :, :]
        pred_positions = pred[:, -2:, :, :]

        loss_shrink_maps,loss_n = self.bce_loss(shrink_maps, batch['shrink_map'], batch['shrink_mask'])
        loss_threshold_maps = self.l1_loss(threshold_maps, batch['threshold_map'], batch['threshold_mask'])
        loss_positions = self.l1_loss(pred_positions, batch['targetzone_values'], batch['targetzone_values'][:,:1,:,:]>0)
    
        if pred.size()[1] >= 2:
            loss_binary_maps = self.dice_loss(binary_maps, batch['shrink_map'], batch['shrink_mask'])
            if self.loss2:
                loss = self.alpha * loss_shrink_maps + self.beta * loss_threshold_maps + loss_positions
            else:
                loss = self.alpha * loss_shrink_maps + self.beta * loss_threshold_maps + loss_binary_maps + loss_positions
            
        else:
            loss = loss_shrink_maps + loss_positions
        
        loss = loss_positions
           
        metrics['loss_shrink_maps'] = loss_shrink_maps
        metrics['loss_threshold_maps'] = loss_threshold_maps
        metrics['loss_binary_maps'] = loss_binary_maps
        metrics['loss_positions'] = loss_positions
        metrics['loss'] = loss
        
        return metrics
