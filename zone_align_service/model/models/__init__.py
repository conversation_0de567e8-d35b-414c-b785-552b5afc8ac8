# -*- coding: utf-8 -*-
# @Time    : 2019/8/23 21:55
# <AUTHOR> <PERSON><PERSON>jun
import copy
from .model import Model,<PERSON><PERSON><PERSON>,Model_Light,Model_Imgtable_For
from .model_dbnetpp import Model_Dbnetpp
from .losses import build_loss

__all__ = ['build_loss', 'build_model']
support_model = ['Model','Hrnet','Model_Light','Model_Imgtable_For','Model_Dbnetpp']


def build_model(config):
    """
    get architecture model class
    """
    copy_config = copy.deepcopy(config)
    arch_type = copy_config.pop('type')
    assert arch_type in support_model, f'{arch_type} is not developed yet!, only {support_model} are support now'
    arch_model = eval(arch_type)(copy_config)
    return arch_model
