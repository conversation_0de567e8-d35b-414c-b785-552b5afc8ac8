import os
import numpy as np
import shutil
import cv2
import torch
from urllib.request import urlopen
from zone_align_service.model.data_loader import get_transforms
from zone_align_service.model.models import build_model
from zone_align_service.model.utils import draw_result,draw_result_tmp
from base_common import LoggerFactory, ImageUtil

log = LoggerFactory.get_logger('ZoneAlign')

def shrink_polygon_pyclipper(polygon, shrink_ratio):
    from shapely.geometry import Polygon
    import pyclipper
    polygon_shape = Polygon(polygon)
    distance = polygon_shape.area * (1 - np.power(shrink_ratio, 2)) / polygon_shape.length
#     distance = 4
    subject = [tuple(l) for l in polygon]
    
    padding = pyclipper.PyclipperOffset()
    padding.AddPath(subject, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)
    shrinked = padding.Execute(-distance)
    if shrinked == []:
        shrinked = np.array(shrinked)
    else:
        shrinked = np.array(shrinked[0]).reshape(-1, 2)
        
    return shrinked

def resize_image_long(img):
    height, width = img.shape
    if height > width:
        long_size = (896, 1280)  # (896,1280)
    else:
        long_size = (1280, 896)
    scale = min(long_size[1] / height, long_size[0] / width)
    new_height = int(round(round(height * scale) / 32) * 32)
    new_width = int(round(round(width * scale) / 32) * 32)
    resized_img = cv2.resize(img, (new_width, new_height))
    return resized_img

def download_photo_oss(url):
    photo_response = urlopen(url, timeout=1)#0.03-0.09
    img_photo = photo_response.read()
    img_photo = np.asarray(bytearray(img_photo), dtype="uint8")#0.05
    img_photo_cv2 = cv2.imdecode(img_photo, cv2.IMREAD_COLOR)#0.005s
    return img_photo_cv2
    
class Pytorch_model:
    def __init__(self, model_path, gpu_id=0):
        '''
        初始化pytorch模型
        :param model_path: 模型地址(可以是模型的参数或者参数和计算图一起保存的文件)
        :param gpu_id: 在哪一块gpu上运行
        '''
        self.gpu_id = gpu_id

        if self.gpu_id is not None and isinstance(self.gpu_id, int) and torch.cuda.is_available():
            self.device = torch.device("cuda:%s" % self.gpu_id)
        else:
            self.device = torch.device("cpu")
        checkpoint = torch.load(model_path, map_location=self.device)

        config = checkpoint['config']
        config['arch']['backbone']['pretrained'] = False

        self.model = build_model(config['arch'])
        self.model.load_state_dict(checkpoint['state_dict'])
        self.model.to(self.device)
        self.model.eval()

        self.transform = []
        for t in config['dataset']['train']['dataset']['args']['transforms']:
            if t['type'] in ['ToTensor', 'Normalize']:
                self.transform.append(t)
        self.transform = get_transforms(self.transform)

        self.vis_dir = './vis_zone_predict/'
        if os.path.exists(self.vis_dir):
            shutil.rmtree(self.vis_dir)
        os.mkdir(self.vis_dir)

        self.size = (896, 1280)
    def __get_img(self, img_data):
        try:
            if os.path.exists(img_data):
                return cv2.imread(img_data)
            else:
                return ImageUtil.base64_2_numpy(img_data)
        except:
            return None
    def predict(self, photo_ori, pdf_path, boxs_json, photo_crop, temp_crop):
        # pdf_url: pdf图片得url路劲；photo_url:对齐后的用户照片url路径
        # boxs_json: 'boxs': {'3678509_0_0': [65, 175, 191, 239], '3678509_1_0': [238, 175, 363, 239]...}pdf录入的答题框坐标，其起始点是相对照片的坐标
        #  photo_crop:[100,0,1204,1560] 用户照片相对于pdf的区域
        pdf_ori = self.__get_img(pdf_path)
        if pdf_ori is None:
            return boxs_json

        if len(pdf_ori.shape) > 2:#转换成灰度图
            pdf_ori = cv2.cvtColor(pdf_ori, cv2.COLOR_BGR2GRAY)
        if len(photo_ori.shape) > 2:
            photo_ori = cv2.cvtColor(photo_ori, cv2.COLOR_BGR2GRAY)
        #log.info(f'pdf_ori:{pdf_ori.shape},photo_crop:{photo_crop},temp_crop:{temp_crop}')
        h,w = photo_ori.shape
        if len(temp_crop) == 0:
            temp_crop = [0,0,0,0]
        if len(photo_crop) == 0:
            photo_crop = [0,0,w,h]
        try:
            pdf_ori = pdf_ori[photo_crop[1]+temp_crop[1]:photo_crop[3]+temp_crop[1],photo_crop[0]+temp_crop[0]:photo_crop[2]+temp_crop[0]]# pdf图片裁剪
        except:
            log.error(f'temp_crop:{temp_crop}')
            pdf_ori = pdf_ori[photo_crop[1]:photo_crop[3],photo_crop[0]:photo_crop[2]]# pdf图片裁剪
        
        pdf_boxs = []
        pdf_boxs_id = []
        for key in boxs_json.keys():
            box = boxs_json[key]

            box[0] = max(0,box[0])
            box[1] = max(0,box[1])
            box[2] = min(w,box[2])
            box[3] = min(h,box[3])
            
            if box[2] - box[0] > 0 and box[3] - box[1] > 0:# 扔掉在照片区域的box
                pdf_boxs.append([[box[0],box[1]],[box[2],box[1]],[box[2],box[3]],[box[0],box[3]]])
                pdf_boxs_id.append(key)
        pdf_boxs = np.array(pdf_boxs).astype(np.float64)
        
        #draw_result_tmp(self.vis_dir,pdf_ori,pdf_boxs)
        
        ratio = min(self.size[0]/w,self.size[1]/h)#图片resize到输入尺寸     
        new_w,new_h = int(ratio*w),int(ratio*h)
        photo = cv2.resize(photo_ori,(new_w,new_h))
        pdf = cv2.resize(pdf_ori,(new_w,new_h))

        new_img = np.zeros((self.size[1],self.size[0]),dtype=np.uint8)
        new_pdf = np.zeros((self.size[1],self.size[0]),dtype=np.uint8)
        new_img[:new_h,:new_w] = photo
        new_pdf[:new_h,:new_w] = pdf
        
        input_mask = np.zeros((self.size[1],self.size[0]), dtype=np.uint8)
        for i in range(len(pdf_boxs)):# 生成pdf剥削的热力图
            pdf_poly = np.array(pdf_boxs[i]).astype(np.float64)
            pdf_poly[:, 0] *= new_w/w
            pdf_poly[:, 1] *= new_h/h
            shrinked = shrink_polygon_pyclipper(pdf_poly,0.8)
            cv2.fillPoly(input_mask,[shrinked.astype(np.int32)], 255)
        
        input_img = np.stack((new_img,new_pdf,input_mask),axis=2)# 整合pdf,photo,mask为输入
        tensor = self.transform(input_img)
        tensor = tensor.unsqueeze_(0)

        tensor = tensor.to(self.device)

        with torch.no_grad():
            if str(self.device).__contains__('cuda'):
                torch.cuda.synchronize(self.device)

            preds = self.model(tensor)
            boxes_all_adjusted = []#相对于照片的预测坐标框，用来返回结果
            for pdf_box in pdf_boxs:
                
                x0 = int(pdf_box[:,0].mean())#中心点
                y0 = int(pdf_box[:,1].mean())
                
                w0 = int(pdf_box[1][0] - pdf_box[0][0])
                h0 = int(pdf_box[2][1] - pdf_box[0][1])
                
                #x1 = int((preds[0,2,int(y0*new_h/h),int(x0*new_w/w)]*w0+x0).cpu())#偏移后的中心点
                #y1 = int((preds[0,3,int(y0*new_h/h),int(x0*new_w/w)]*h0+y0).cpu())
                
                x1 = int((preds[0,2,int(y0*new_h/h),int(x0*new_w/w)]*50+x0).cpu())#偏移后的中心点
                y1 = int((preds[0,3,int(y0*new_h/h),int(x0*new_w/w)]*50+y0).cpu())
                
                xmin = max(0,x1-w0//2)
                ymin = max(0,y1-h0//2)
                xmax = min(w,x1+w0//2)
                ymax = min(h,y1+h0//2)
                boxes_all_adjusted.append([xmin,ymin,xmax,ymax])
                
            boxes_all_adjusted = np.array(boxes_all_adjusted)
            
            try:
                log.info(f'photo_ori:{photo_ori.shape},pdf_ori:{pdf_ori.shape}')
                draw_result(self.vis_dir, photo_ori, boxes_all_adjusted,pdf_ori,pdf_boxs)  # 可视化结果
            except:
                pass
            
        for ii,box_id in enumerate(pdf_boxs_id):
            new_box = boxes_all_adjusted[ii]
            #限制预测框不超出pdf区域，超出的box不做调整
            if new_box[0] >= w or new_box[1] >= h or new_box[2] <= 0 or new_box[3] <= 0:
                continue
                
            boxs_json[box_id] = new_box.tolist()

        return boxs_json



# if __name__ == '__main__':
#
#     model_path = './weights/model_latest.pth'
#     model = Pytorch_model(model_path, gpu_id=0)
#
#     photo = cv2.imread('/home/<USER>/align/traindata_v1_normal/photos/437920_325729.jpg',0)
#     pdf = cv2.imread('/home/<USER>/align/traindata_v1_normal/pdfs/437920_325729.jpg',0)
#
#     boxs = [[95,396,337,475],[94,638,951,841],[336,397,614,475],[613,221,878,309],[603,396,870,472]]
#
#     result_boxs,cost_t = model.predict(pdf,photo,boxs)
#     print(result_boxs)
    
    

