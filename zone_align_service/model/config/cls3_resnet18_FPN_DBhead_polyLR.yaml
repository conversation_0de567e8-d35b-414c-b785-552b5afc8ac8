name: DBNet
base: ['config/icdar2015.yaml']
cls_list: ['txt']
cls_weight: [1]
arch:
  type: Model_Light
  cls_list: []
  backbone:
    type: resnet18
  neck:
    type: FPN
    inner_channels: 256
  head:
    type: SEGHead
    out_channels: 2
    k: 50
post_processing:
  type: SegDetectorRepresenter
  args:
    thresh: 0.2
    box_thresh: 0.2
    max_candidates: 1000
    unclip_ratio: 1.2 #0. #1.5 # from paper
metric:
  type: QuadMetric
  args:
    is_output_polygon: false
loss:
  type: DBLoss
  alpha: 1
  beta: 10
  ohem_ratio: 3
  loss2: false
  focal_flag: true
optimizer:
  type: Adam
  args:
    lr: 0.0001 # 变化采用PolyLR模式，最后会减小到0，可以设置最小的lr：target_lr
    weight_decay: 0
    amsgrad: true
lr_scheduler:
  type: WarmupPolyLR #WarmupMultiStepLR #WarmupPolyLR
  args:
    power: 2
    warmup_epoch: 1
#     milestones: [5,8]
trainer:
  seed: 1
  epochs: 50
  log_iter: 10
  show_images_iter: 1000
  resume_checkpoint: ''
  finetune_checkpoint: "/home/<USER>/handwrite/dbnet/weights/output_txt_04_v1/DBNet_resnet18_FPN_SEGHead/checkpoint/model_best.pth"
  output_dir: ./weights/output_txt_06_v2
  tensorboard: true
dataset:
  train:
    dataset:
      args:
        cls_list: []
        data_path:
          - ../data/train_all.txt
        img_mode: RGB
        pre_processes: # 数据的预处理过程，包含augment和标签制作
          - type: IaaAugment # 使用imgaug进行变换
            args:
              - {'type':Fliplr, 'args':{'p':0.5}}
              - {'type': Affine, 'args':{'rotate':[-10,10]}}
              - {'type':Resize,'args':{'size':[0.5,2]}} # resize 图片为[W*v,H*v],v为（0.5-2）之间的随机值
          - type: EastRandomCropData # 对图片进行随机截取，并保证不截断文本
            args:
              size: [896,1280] # w,h [1280,896]
              max_tries: 50
              keep_ratio: true # 图片截取后缩放到640的过程中是否keep_ratio,如果不，直接resize，如果是，那么进行padding
          - type: MakeBorderMap # 生成阈值图标签
            args:
              shrink_ratio: 0.4
              thresh_min: 0.3
              thresh_max: 0.7
          - type: MakeShrinkMap # 生成概率图和二值化图标签
            args:
              shrink_ratio: 0.6 #1. #0.4
              min_text_size: 8
              shrink_type:  'pyclipper' #'pyclipper_weight' #决定是否使用gaussian顶点weight增强
    loader:
      batch_size: 6
      shuffle: true
      pin_memory: true
      num_workers: 0
      collate_fn: ''
  validate:
    dataset:
      args:
        cls_list: []
        data_path:
          - ../data/test_all.txt
        pre_processes:
          - type: ResizeConstraintedSize
            args:
              short_size: [896,1280]
              resize_text_polys: false
        img_mode: RGB
    loader:
      batch_size: 1
      shuffle: true
      pin_memory: false
      num_workers: 1
      collate_fn: ICDARCollectFN
