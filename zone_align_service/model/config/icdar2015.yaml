name: DBNet

loss:
  type: DBLoss
  alpha: 1
  beta: 10
  ohem_ratio: 3
  loss2: false
dataset:
  train:
    dataset:
      type: ICDAR2015Dataset # 数据集类型
      args:
        data_path: # 一个存放 img_path \t gt_path的文件
          - ''
        pre_processes: # 数据的预处理过程，包含augment和标签制作
          - type: IaaAugment # 使用imgaug进行变换
            args:
              - {'type':Fliplr, 'args':{'p':0.5}}
              - {'type': Affine, 'args':{'rotate':[-10,10]}}
              - {'type':Resize,'args':{'size':[0.5,2]}} # resize 图片为[W*v,H*v],v为（0.5-2）之间的随机值
          - type: EastRandomCropData # 对图片进行随机截取，并保证不截断文本
            args:
              size: [640,896] # w,h
              max_tries: 50
              keep_ratio: true # 图片截取后缩放到640的过程中是否keep_ratio,如果不，直接resize，如果是，那么进行padding
          - type: MakeBorderMap # 生成阈值图标签
            args:
              shrink_ratio: 0.4
              thresh_min: 0.3
              thresh_max: 0.7
          - type: MakeShrinkMap # 生成概率图和二值化图标签
            args:
              shrink_ratio: 0.4
              min_text_size: 8
              
        transforms: # 对图片进行的变换方式
#           - type: ColorJitter
#             args:
#               brightness: 0.25
#               contrast: 0.25
#               saturation: 0.25
#               hue: 0.1
#           - type: GaussianBlur
#             args:
#               kernel_size: [3,7]
#               sigma: [0.5,1.5]
          - type: ToTensor
            args: {}
          - type: Normalize
            args:
              mean: [0.485, 0.456, 0.406]
              std: [0.229, 0.224, 0.225]
        img_mode: RGB
        #['img_path', 'img_name', 'text_polys', 'texts', 'ignore_tags', 'pdfs_polys', 'pdfs_lbls', 'img', 'shape', 'threshold_map', 'threshold_mask', 'shrink_map', 'shrink_mask', 'full_map', 'full_mask', 'targetzone_values']
        filter_keys: [img_name,text_polys,texts,ignore_tags,shape,pdfs_polys,pdfs_lbls] # 返回数据之前，从数据字典里删除的key, ---img_path
        ignore_tags: ['*', '###']
    loader:
      batch_size: 1
      shuffle: true
      pin_memory: false
      num_workers: 0
      collate_fn: ''
  validate:
    dataset:
      type: ICDAR2015Dataset
      args:
        data_path:
          - ''
        pre_processes:
          - type: ResizeConstraintedSize # 保证短边 >= short_size，如果短边超过short_size,则不做处理
            args:
              short_size: (896,640)
              resize_text_polys: false
        transforms:
          - type: ToTensor
            args: {}
          - type: Normalize
            args:
              mean: [0.485, 0.456, 0.406]
              std: [0.229, 0.224, 0.225]
        img_mode: RGB
        filter_keys: []
        ignore_tags: ['*', '###']
    loader:
      batch_size: 1
      shuffle: true
      pin_memory: false
      num_workers: 0
      collate_fn: ICDARCollectFN