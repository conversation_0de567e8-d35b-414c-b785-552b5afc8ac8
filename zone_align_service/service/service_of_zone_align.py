# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/12/6 10:22 
# @Description  : service_of_zone_align.py
import traceback

from base_common import Constants, LoggerFactory, MissionMode
from base_common.service.service_of_base import BaseModelService
from zone_align_service.model.interface import Pytorch_model

log = LoggerFactory.get_logger('ZoneAlignService')

class ZoneAlignService(BaseModelService):
    def __init__(self, gpu_id=0):
        super().__init__()
        self.mission_mode = MissionMode.ZONE_ALIGN_MISSION
        model_path = f"{Constants.MODEL_WEIGHT_PATH}/zone_align_service/model_latest.pth"
        self._model = Pytorch_model(model_path, gpu_id=gpu_id)

    def do_post(self, data_json):
        try:
            boxs_ori = data_json['boxs_ori']
            align_img = self.get_image(data_json)
            if align_img is None:
                return boxs_ori
            temp_boxs = boxs_ori.copy()
            pdf_path = data_json['pdf_path']
            temp_crop = data_json['temp_crop']
            photo_crop = data_json['photo_crop']
            boxs_json = self._model.predict(align_img, pdf_path, boxs_ori, photo_crop,temp_crop)
            return boxs_json
        except:
            log.error(f"对齐失败 {traceback.format_exc()}")
            return temp_boxs