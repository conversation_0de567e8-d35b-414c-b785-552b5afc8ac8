# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 16:17 
# @Description  : service_of_detection.py
import time
import traceback

import cv2
import torch
import numpy as np

from base_common import LoggerFactory, Constants, ImageUtil, MissionMode
from base_common.service.service_of_base import BaseModelService
from base_common.util.util_of_detection_cpu import ImageProcessorCpu
from base_common.util.util_of_detection_gpu import ImageProcessorGpu

log = LoggerFactory.get_logger('DetectionService')

class DetectionService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.DETECTION_MISSION
        self.deivce = 'cuda'
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/detection_service/detection_s_320.pt'
        self.detection_fn = torch.load(model_path, map_location='cpu')['model'].to(torch.device(self.deivce)).float().eval()

        self.__empty_resp = {'img_key': None, 'img_type': None}
        log.info('模型加载并预热完成！')

    def do_post(self, data_json):
        t0 = time.time()
        try:
            img = self.get_image(data_json)
            img_tmp = img.copy()
            img_shape = img.shape
            img_pre, IM = ImageProcessorCpu.preprocess_warpAffine(img, dst_width=320, dst_height=320)

            img = ImageUtil.preprocess_batch(img_pre)
            img = img[None]
            img_pre = torch.from_numpy(img).to(self.deivce)
            out = self.detection_fn(img_pre)
            masks = ImageProcessorGpu.detect_and_process(out, img_pre.shape[2:])
            if len(masks) == 0:
                log.error('masks 为空')
                return self.__empty_resp
            mask = masks[0].detach().cpu().numpy().astype(np.uint8)  # 取第一个掩码
            out = cv2.warpAffine(mask, IM, (img_shape[1], img_shape[0]), flags=cv2.INTER_LINEAR)

            kernel_size = 35
            kernel = np.ones((kernel_size, kernel_size), dtype=np.uint8)

            # 对掩码进行膨胀操作
            dilated_mask = cv2.dilate(out, kernel)
            # 取最小外接多边形
            contours, _ = cv2.findContours(dilated_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                log.error('contours 为空')
                return self.__empty_resp

            largest_contour = max(contours, key=cv2.contourArea)  # 找到最大的轮廓
            largest_contour = largest_contour.tolist()
            img_user = ImageProcessorCpu.draw_contours_cut(img_tmp, largest_contour)
            log.info(f'任务结束>>> {time.time() - t0} sec {self.deivce}')
            img_key, img_type = self.set_image(img_user)
            return {'img_key': img_key, 'img_type': img_type}
        except:
            log.error(f'Error in processing: {traceback.format_exc()}')
            return self.__empty_resp
