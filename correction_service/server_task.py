# -*- coding: utf-8 -*-#
'''主要为拍照批改异步任务的获取及执行'''

import time
import json
import uuid
import socket
import requests
import traceback
import concurrent.futures
import datetime
import tornado.web
import tornado.ioloop
import tornado.httpserver
from base_common import LoggerFactory, Context
from base_common.service.service_of_redis import RedisManager

log = LoggerFactory.get_logger('task', 'task')
biz_type_code = 'camera_correct_v5'
class StopHandler(tornado.web.RequestHandler):
    taskRunner = None
    def initialize(self, _runner):
        StopHandler.taskRunner = _runner
    def get(self):
        log.info(f'停止获取异步任务')
        StopHandler.taskRunner.stop()
        self.write('task stoped.')

    def post(self):
        log.info(f'停止获取异步任务')
        StopHandler.taskRunner.stop()
        self.write('task stoped.')

class Worker:
    def __init__(self, redisService, threads, drop):
        self._redisService = redisService
        self._threads = threads
        self._timeout = drop
        self._lost_task_key = None
        self._increment_task = False
        self._stop_flag = False
        self._pending = False
        self._task_req_url = Context.get_task_req_url()
        self._task_resp_url = Context.get_task_resp_url()
        self._notify_url = Context.get_notify_url()
        self._correction_url = 'http://127.0.0.1:8886/correct'
        self._working_count_key = Context.get_working_task_count_key()
        self._task_req_data = {'bizTypeCode': biz_type_code, 'workGroup': 'product' if Context.is_product() else 'daily', 'getLimit': 1}
    def _updateDrop(self):
        try:
            self._redisService.increment(self._lost_task_key)
        except:
            log.error(f'上报丢失任务异常 \n{traceback.format_exc()}')
    def _updateThreads(self, prefix, num):
        try:
            today = datetime.date.today().strftime("%Y-%m-%d")
            self._lost_task_key = f"{today}:lost_task_count"
            self._redisService.set(f"{today}:threads:{prefix}_{num}", "1")
        except:
            pass
    def _notify(self, message):
        try:
            response = requests.post(self._notify_url, data=message)
            log.debug(f"request notify feishu success {response.text}")
        except:
            log.error(f"推送飞书消息失败!{traceback.format_exc()}")

    def run(self, prefix, num):
        log.info(f'取任务线程{num}开始工作...')
        while not self._stop_flag:
            try:
                if self._pending:
                    log.info(f'取任务线程{num}休眠中...')
                    time.sleep(2)
                    continue
                resp = requests.post(url=self._task_req_url, params=self._task_req_data, timeout=10)
                if not resp:
                    log.warn(f'{num}访问批改任务失败！')
                    if Context.is_product():
                        self._notify(f"无法从异步任务获取到批改任务".encode('utf-8'))
                else:
                    log.debug(f"{num}访问批改任务成功！")
                    self._updateThreads(prefix, num)
                    data_all = resp.json().get('data', [])
                    if len(data_all) > 0:
                        log.debug(f"{num}获取到1条任务")
                        data_all = data_all[0]
                        ctime = data_all['gmtCreate'] / 1000.0
                        params = json.loads(data_all['bizParams'])
                        task_create_date = datetime.datetime.fromtimestamp(ctime)
                        log.info(f"(record_id:{params['recordId']}) 异步任务创建于: {task_create_date}, 任务参数 {params}")
                        # 如果异步任务创建时间与当前获取任务时间的间隔超过5s，则直接报错不批改。
                        params.update({'create_date': ctime, 'current_thread': num, 'total_thread': self._threads})
                        if (time.time() - ctime) > self._timeout and Context.is_product():
                            params.update({'errorCode': 20013,"originImageUrl": params['imageUrl']})
                            message = {'bizTypeCode': biz_type_code, 'result': json.dumps(params),
                                       'id': data_all['id']}
                            log.warn(f"(record_id:{params['recordId']}) 任务已经超过{self._timeout}秒，不进行批改。响应20013")
                            self._updateDrop()
                        else:
                            message = self._do_mission(data_all, params)
                        try:
                            log.info(f"(record_id:{params['recordId']}) 将批改结果推送到异步任务池,"
                                     f"任务结果 {json.dumps(message, ensure_ascii=False)}")
                            resp_ = requests.post(url=self._task_resp_url, data=message)
                            log.info(f"(record_id:{params['recordId']}) 推送批改结果结束,"
                                     f"服务器响应：{resp_.status_code} {resp_.text}")
                        except:
                            log.error(f"report correction result error\n{traceback.format_exc()}")
                    else:
                        log.debug(f"{num}当前无任务")
                        time.sleep(0.1)
            except:
                log.error(f'异步任务线程{num}异常，\n{traceback.format_exc()}')

        log.info(f'取任务线程{num}停止.')
        return None
    def pending(self):
        self._pending = True
    def running(self):
        self._pending = False
    def stop(self):
        self._stop_flag = True
    def _do_mission(self, data_all, params):
        record_id = params.get('recordId', None)
        today = datetime.date.today().strftime("%Y-%m-%d")
        if record_id is None:
            params.update({'errorCode': 20012})
            message = {'bizTypeCode': biz_type_code, 'result': json.dumps(params), 'id': data_all['id']}
            log.error(f"(record_id:{record_id}) 任务参数异常，响应20012")
            self._updateError(today, json.dumps(params))
            return message
        unikey = self._working_count_key + ":" + str(uuid.uuid4()).replace("-", '').upper()
        self._redisService.set(unikey, "1", 16)
        try:
            log.info(f"(record_id:{record_id}) 请求批改主程序...")
            judge_start_time = time.time()
            resp_pg = requests.post(url=self._correction_url, data=json.dumps(params), timeout=40)
            judge_end_time = time.time()
            log.info(
                f"(record_id:{record_id}) 主程序处理完成，耗时： {'%.2f seconds.' % (judge_end_time - judge_start_time)}")
            if resp_pg.status_code == 200:
                message = {'bizTypeCode': biz_type_code, 'record_id': params['recordId'],
                           'result': resp_pg.text, 'id': data_all['id']}
            else:
                params.update({'errorCode': 20012,"originImageUrl": params['imageUrl']})
                message = {'bizTypeCode': biz_type_code, 'result': json.dumps(params), 'id': data_all['id']}
                self._updateError(today, json.dumps(params))
            return message
        except:
            log.error(f"(record_id:{record_id}) 任务处理异常 \n{traceback.format_exc()}")
            params.update({'errorCode': 20012, "originImageUrl": params['imageUrl']})
            message = {'bizTypeCode': biz_type_code, 'result': json.dumps(params), 'id': data_all['id']}
            self._updateError(today, json.dumps(params))
            return message
        finally:
            try:
                self._redisService.remove(unikey)
            except:
                log.error(f"(record_id:{record_id}) 删除redis key {unikey} 失败！\n{traceback.format_exc()}")
    def _updateError(self, today, req_data):
        self._redisService.increment(f"{today}:error_service:20012")
        self._redisService.right_push(f"{today}:error_service_list:20012", req_data)
class Runner:
    def __init__(self):
        self._redisService = RedisManager.get_mission_queue_redis()
        try:
            self._prefix_str = socket.gethostbyname(socket.gethostname())
        except:
            self._prefix_str = str(uuid.uuid4()).replace('-', '')

        self._pending_key = 'WORKING_THREAD_PENDING'
        _threadStr = self._redisService.get_val("CONST:THREAD_NUMBER")
        _timeoutStr = self._redisService.get_val("CONST:TASK_TIMEOUT")
        self._thread = int(_threadStr) if _threadStr is not None else 8
        self._timeout = int(_timeoutStr) if _timeoutStr is not None else 5
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=self._thread + 1)
        self._futures = []
        self._workers = []
        self._stop_flag = False
    def check_pending(self):
        while not self._stop_flag:
            try:
                val = self._redisService.get_val(self._pending_key)
                if val is not None:
                    pending_dict = json.loads(val)
                    for key, value in pending_dict.items():
                        thread_num = int(key.replace('threadNum', ''))
                        if thread_num < len(self._workers):
                            if value == 'on':
                                self._workers[thread_num].running()
                            else:
                                self._workers[thread_num].pending()
            except:
                pass
            time.sleep(10)

    def start(self):
        # 提交任务到线程池
        redisService = RedisManager.get_mission_queue_redis()
        for i in range(self._thread):
            _worker = Worker(redisService, self._thread, self._timeout)
            self._workers.append(_worker)
            self._futures.append(self._executor.submit(_worker.run, self._prefix_str, i))
        self._futures.append(self._executor.submit(self.check_pending))

    def stop(self):
        self._stop_flag = True
        for _worker in self._workers:
            _worker.stop()
        self._executor.shutdown(wait=True)

if __name__ == '__main__':
    Context.setup()
    runner = Runner()
    runner.start()
    _app = tornado.web.Application([(r"/stop", StopHandler, dict(_runner=runner))])
    _server = tornado.httpserver.HTTPServer(_app)
    _server.listen(18886)
    log.info(f"Tornado server started on port 18886")

    try:
        tornado.ioloop.IOLoop.current().start()
    except KeyboardInterrupt:
        log.info("Stopping server...")
        runner.stop()
        log.info("Server stopped and all tasks completed.")
