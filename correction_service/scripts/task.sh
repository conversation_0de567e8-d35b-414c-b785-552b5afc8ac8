#!/bin/bash

export CURRENT_LOG_NAME=task

set_and_export_vals(){
  APPLICATION_NAME="server_$CURRENT_LOG_NAME"
  SCRIPT_PATH="$APPLICATION_NAME.py"
  CURRENT_DIR=$(pwd)
  WORK_HOME=/usr/servering/correction_runtime
  CONF_SCRIPT_PATH=$WORK_HOME/base_common/scripts/get_sys_conf.sh
  PID_FILE="$WORK_HOME/pids/$APPLICATION_NAME.pid"
  chmod +x $CONF_SCRIPT_PATH
  ENVTYPE=$($CONF_SCRIPT_PATH "EnvType")
  CONDA_ENV=$($CONF_SCRIPT_PATH "Conda" "server_correction")

  source ~/anaconda3/etc/profile.d/conda.sh
  conda activate $CONDA_ENV
  export EnvType=$ENVTYPE

  export PYTHONPATH=$WORK_HOME
  echo "python working dir: $WORK_HOME"
  echo "log name: $CURRENT_LOG_NAME"
  echo "PID_FILE name: $PID_FILE"
  echo "CONDA_ENV name: $CONDA_ENV"
}

start_server(){
  if [ -f "$PID_FILE" ]; then
    pids=$(cat "$PID_FILE")
    if ps -p $pids > /dev/null; then
      echo "Server already running with PID(s):"
      echo $pids
      return
    else
      echo "Removing stale PID file."
      rm -f "$PID_FILE"
    fi
  fi

  echo "Starting server..."
  nohup python -u $SCRIPT_PATH &> /dev/null &
  echo "run scripts--> $SCRIPT_PATH"
  echo $! > "$PID_FILE"
  echo "Server started with PID(s):"
  cat "$PID_FILE"
  sleep 2
  ps -ef|grep $SCRIPT_PATH
}

stop_server() {
  response=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:18886/stop)
  if [ "$response" -eq 200 ]; then
    echo "Stop request was successful"
  else
    echo "Stop request failed with status code $response"
  fi
  sleep 1
  if [ ! -f "$PID_FILE" ]; then
    echo "No server process found."
  else
    pids=$(cat "$PID_FILE")
    if [ -z "$pids" ]; then
      echo "No server process found."
    else
      if ps -p $pids > /dev/null; then
        echo "Stopping server..."
        for pid in $pids; do
          echo "Killing process ID $pid"
          kill -9 $pid
        done
        rm -f "$PID_FILE"
        echo "Server stopped."
      else
        echo "No server process found."
        rm -f "$PID_FILE"
      fi
    fi
  fi
}

handle_command() {
  case "$1" in
      start)
          start_server
          ;;
      stop)
          stop_server
          ;;
      *)
          stop_server
          start_server
          ;;
  esac
}

#sync; echo 3 > /proc/sys/vm/drop_caches

set_and_export_vals
handle_command "$1"
exit 0