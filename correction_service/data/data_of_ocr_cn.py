# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/7 11:52 
# @Description  : data_of_ocr_cn.py
import json

from base_common import BaseData


class OcrCnData(BaseData):
    def __init__(self, record_id=None, batch_imgs=None, batch_labels=None):
        if batch_labels is None:
            batch_labels = []
        if batch_imgs is None:
            batch_imgs = []
        self._record_id = record_id
        self._batch_imgs = batch_imgs
        self._batch_labels = batch_labels

    def get_record_id(self): return self._record_id
    def get_batch_imgs(self): return self._batch_imgs
    def get_batch_labels(self): return self._batch_labels
    def set_record_id(self, record_id):
        self._record_id = record_id
        return self
    def set_batch_imgs(self, batch_imgs):
        self._batch_imgs = batch_imgs
        return self
    def set_batch_labels(self, batch_labels):
        self._batch_labels = batch_labels
        return self

    def to_json(self):
        return json.dumps({
            "recordId": self._record_id,
            "imgs": self._batch_imgs,
            "std_answers": self._batch_labels
        })
