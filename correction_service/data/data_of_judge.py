# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime_review 
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/12/9 15:13 
# @Description  : data_of_judge.py
import json
from typing import List
from base_common import BaseData
from base_common.constants import TopicType as TT

class JudgeData(BaseData):
    def __init__(self, itemInfo=None, cropCoord=None, pageId=None, alignUrl=None, originImageUrl=None, ksBools=None,
                 answered=None, height=None, width=None, bizType=None, correction=None, bookId=None):
        super().__init__()
        if itemInfo is None:
            itemInfo = []
        self._itemInfo: List = itemInfo
        self._cropCoord = cropCoord
        self._pageId = pageId
        self._alignUrl = alignUrl
        self._originImageUrl = originImageUrl
        self._ksBools = ksBools
        self._answered = answered
        self._height = height
        self._width = width
        self._bizType = bizType
        self._correction = correction
        self._bookId = bookId

    def setItemInfo(self, itemInfo):
        self._itemInfo = itemInfo

    def getItemInfo(self):
        return self._itemInfo
    def setCropCoord(self, cropCoord):
        self._cropCoord = cropCoord
    def getCropCoord(self):
        return self._cropCoord
    def setPageId(self, pageId):
        self._pageId = pageId
    def getPageId(self):
        return self._pageId

    def setAlignUrl(self, alignUrl):
        self._alignUrl = alignUrl
    def getAlignUrl(self):
        return self._alignUrl
    def setOriginImageUrl(self, originImageUrl):
        self._originImageUrl = originImageUrl
    def getOriginImageUrl(self):
        return self._originImageUrl
    def setKsBools(self, ksBools):
        self._ksBools = ksBools
    def getKsBools(self):
        return self._ksBools
    def setAnswered(self, answered):
        self._answered = answered
    def getAnswered(self):
        return self._answered
    def setHeight(self, height):
        self._height = height
    def getHeight(self):
        return self._height
    def setWidth(self, width):
        self._width = width
    def getWidth(self):
        return self._width
    def setBizType(self, bizType):
        self._bizType = bizType
    def getBizType(self):
        return self._bizType
    def setCorrection(self, correction):
        self._correction = correction
    def getCorrection(self):
        return self._correction
    def setBookId(self, bookId):
        self._bookId = bookId
    def set_book_id(self, bookId):
        self._bookId = bookId
    def getBookId(self):
        return self._bookId

    def toString(self):
        return json.dumps(self.toDict(), ensure_ascii=False)

    def toDict(self):
        json_data = {}
        items = self.__dict__.items()
        for attr, value in items:
            attr_name = attr[1:]
            if isinstance(value, List):
                arr = []
                for i in range(len(value)):
                    arr.append(value[i])
                json_data[attr_name] = arr
            elif value is not None:
                json_data[attr_name] = value
        return json_data

    def __str__(self):
        return self.toString()
    @staticmethod
    def __setArea(coord, w, h):
        if coord is None:
            return {}
        area = {}
        if isinstance(coord[0], List):
            """
            0---------1
            |         |
            |         |
            3---------2
            """
            # dewen update
            if isinstance(coord[0][0], List):
                area['x1'] = round(coord[0][0][0] / w, 4)
                area['y1'] = round(coord[0][0][1] / h, 4)
                area['x2'] = round(coord[0][2][0] / w, 4)
                area['y2'] = round(coord[0][2][1] / h, 4)
            else: 
            # dewen update
                area['x1'] = round(coord[0][0] / w, 4)
                area['y1'] = round(coord[0][1] / h, 4)
                area['x2'] = round(coord[2][0] / w, 4)
                area['y2'] = round(coord[2][1] / h, 4)
        else:
            area['x1'] = round(coord[0] / w, 4)
            area['y1'] = round(coord[1] / h, 4)
            area['x2'] = round(coord[2] / w, 4)
            area['y2'] = round(coord[3] / h, 4)
        return area
    @staticmethod
    def process_answer(key, answerInfo, correctData):
        """
        处理单个答案的逻辑，包括题干、答案、识别结果等。
        """
        user_answer = {}

        # 处理智能口算答案
        if key in correctData.get_answer_ks():
            user_answer['quickCorrectionSuggestAnswer'] = correctData.get_answer_ks()[key][0]
            del correctData.get_answer_ks()[key]

        # 处理口算识别结果
        if key in correctData.get_rec_ks():
            user_answer['quickCorrectionRecognition'] = correctData.get_rec_ks()[key][0]
            del correctData.get_rec_ks()[key]

        # 处理智能口算题干
        if key in correctData.get_stem_ks():
            user_answer['quickCorrectionStem'] = correctData.get_stem_ks()[key]
            del correctData.get_stem_ks()[key]

        # 处理所有识别结果
        if key in correctData.get_total_recs():
            user_answer['recognition'] = correctData.get_total_recs()[key]
            del correctData.get_total_recs()[key]

        # 处理小题坐标
        if key in correctData.get_reg_coords():
            user_answer['area'] = JudgeData.__setArea(correctData.get_reg_coords()[key], correctData.get_w(), correctData.get_h())
            del correctData.get_reg_coords()[key]
        # update by dewen
        tmp_iteminfo = []
        for i in range(len(correctData.get_item_info())):
            if correctData.get_item_info()[i]['itemType'] == 18:
                tmp_iteminfo.append(str(correctData.get_item_info()[i]['itemId']))
        # update by dewen
                
        # 处理识别类型和正确结果
        if key in correctData.get_reg_class():
            # update by dewen
            if key.split('_')[0] in tmp_iteminfo:
                user_answer['recognitionType'] = 1
            else:
                user_answer['recognitionType'] = correctData.get_reg_class()[key]
            # update by dewen
            user_answer['correctResult'] = correctData.get_reg_class()[key]
            del correctData.get_reg_class()[key]

        # 添加答案到answerInfo
        answerInfo['userAnswer'].append(user_answer)
        return user_answer

    @staticmethod
    def transform_quick_correction_data(correctData, itemInfo):
        itemInfo['answerInfo'] = []
        itemId = str(itemInfo['itemId'])

        if itemId in correctData.get_ref_coords():  # 题目坐标
            itemInfo['realCoord'] = correctData.get_ref_coords()[itemId]

        # 处理每个题目
        _stem_ks = correctData.get_stem_ks().copy()
        for key in _stem_ks:
            if not key.startswith(itemId):
                continue
            answerInfo = {'userAnswer': []}
            JudgeData.process_answer(key, answerInfo, correctData)
            itemInfo['answerInfo'].append(answerInfo)

    @staticmethod
    def no_answer_check(correctData, answerInfos, itemInfo, itemId, itemType, __new_answe_info):
        _answerKs = correctData.get_answer_ks()
        if len(answerInfos) == 0:
            if itemType == TT.INTELLIGENT_ORAL_CALC:
                del_keys = []
                for key in correctData.get_stem_ks():
                    if not key.startswith(itemId):
                        continue
                    answerInfo = {'userAnswer': []}
                    user_answer = {}

                    # 处理智能口算答案
                    if key in correctData.get_answer_ks():
                        user_answer['quickCorrectionSuggestAnswer'] = correctData.get_answer_ks()[key][0]
                        del correctData.get_answer_ks()[key]

                    # 处理口算识别结果
                    if key in correctData.get_rec_ks():
                        user_answer['quickCorrectionRecognition'] = correctData.get_rec_ks()[key][0]
                        del correctData.get_rec_ks()[key]

                    # 处理智能口算题干
                    if key in correctData.get_stem_ks():
                        user_answer['quickCorrectionStem'] = correctData.get_stem_ks()[key]

                    # 处理所有识别结果
                    if key in correctData.get_total_recs():
                        user_answer['recognition'] = correctData.get_total_recs()[key]
                        del correctData.get_total_recs()[key]

                    # 处理小题坐标
                    if key in correctData.get_reg_coords():
                        user_answer['area'] = JudgeData.__setArea(correctData.get_reg_coords()[key], correctData.get_w(), correctData.get_h())
                        del correctData.get_reg_coords()[key]

                    # 处理额外的小题坐标
                    if key in correctData.get_reg_coords_():
                        user_answer['area'] = JudgeData.__setArea(correctData.get_reg_coords_()[key], correctData.get_w(), correctData.get_h())
                        del correctData.get_reg_coords_()[key]

                    # 处理识别类型和正确结果
                    if key in correctData.get_reg_class():
                        user_answer['recognitionType'] = correctData.get_reg_class()[key]
                        user_answer['correctResult'] = correctData.get_reg_class()[key]
                        del correctData.get_reg_class()[key]

                    # 添加答案到answerInfo
                    answerInfo['userAnswer'].append(user_answer)
                    __new_answe_info.append((itemInfo, answerInfo))
                    del_keys.append(key)
                for key in del_keys:
                    del correctData.get_stem_ks()[key]
            return True
        return False
    @staticmethod
    def answer_check(correctData, answerInfos, itemType):
        for answerInfo in answerInfos:
            if 'userAnswer' not in answerInfo:
                answerInfo['userAnswer'] = []
            blockName = answerInfo['blockName']
            blockType = answerInfo['blockType']
            #  pass
            if itemType == TT.WORD_PROBLEM:
                total_recs = correctData.get_total_recs()
                del_keys = []
                for k in total_recs:
                    if k.startswith(blockName + '_'):
                        answerInfo['userAnswer'].append({
                            'recognitionType': correctData.get_reg_class()[k],
                            'correctResult': correctData.get_reg_class()[k],
                            'recognition': total_recs[k],
                            'area': JudgeData.__setArea(correctData.get_reg_coords()[k], correctData.get_w(), correctData.get_h())
                        })
                        del_keys.append(k)
                for k in del_keys:
                    del total_recs[k]
                    del correctData.get_reg_coords()[k]
                    del correctData.get_reg_class()[k]
                continue

            if itemType in [TT.VERTICAL_CALC_WITH_CHECK, TT.VERTICAL_CALC]:
                JudgeData.process_answer(blockName, answerInfo, correctData)
                continue

            areas = answerInfo.get('area', [])
            for i in range(len(areas)):
                key = f"{blockName}_{i}"
                has_rec_res = key in correctData.get_total_recs()
                user_answer = JudgeData.process_answer(key, answerInfo, correctData)
                if itemType == TT.OFF_COMPUTE:
                    # 处理额外的小题坐标
                    if key in correctData.get_reg_coords_():
                        user_answer['area'] = JudgeData.__setArea(correctData.get_reg_coords_()[key], correctData.get_w(), correctData.get_h())
                        del correctData.get_reg_coords_()[key]
                else:
                    if blockType == 0 and itemType != TT.DRAW_IMG:
                        if not has_rec_res and key in correctData.get_reg_coords_():
                            user_answer['area'] = JudgeData.__setArea(correctData.get_reg_coords_()[key], correctData.get_w(), correctData.get_h())
                            del correctData.get_reg_coords_()[key]
    @staticmethod
    def fromCorrection(correctData, bizType):
        _itemInfo = correctData.get_item_info()
        if correctData.is_ks_bools():
            for itemInfo in _itemInfo:
                if 'itemId' not in itemInfo or 'itemType' not in itemInfo:
                    continue
                JudgeData.transform_quick_correction_data(correctData, itemInfo)
        else:
            __new_answe_info = []
            for itemInfo in _itemInfo:
                if 'itemId' not in itemInfo or 'itemType' not in itemInfo:
                    continue
                answerInfos = itemInfo.get('answerInfo', [])
                itemId = str(itemInfo['itemId'])
                itemType = itemInfo['itemType']

                if itemId in correctData.get_ref_coords():
                    itemInfo['realCoord'] = correctData.get_ref_coords()[itemId]
                    del correctData.get_ref_coords()[itemId]

                if JudgeData.no_answer_check(correctData, answerInfos, itemInfo, itemId, itemType, __new_answe_info):
                    continue
                if itemType == TT.INTELLIGENT_ORAL_CALC:
                    answerInfos = []
                    stem_ks = correctData.get_stem_ks().copy()
                    for blockName in stem_ks.keys():
                        if not blockName.startswith(itemId):
                            continue
                        answerInfo = {
                            'correct': 1,
                            'verifyCal': 0,
                            'blockType': 0,
                            'blockName': blockName,
                            'userAnswer': []
                        }
                        JudgeData.process_answer(blockName, answerInfo, correctData)
                        answerInfos.append(answerInfo)
                    itemInfo['answerInfo'] = answerInfos
                    continue
                JudgeData.answer_check(correctData, answerInfos, itemType)

            if len(__new_answe_info) != 0:
                for itemInfo, answerInfo in __new_answe_info:
                    if 'answerInfo' not in itemInfo:
                        itemInfo['answerInfo'] = [answerInfo]
                    else:
                        itemInfo['answerInfo'].append(answerInfo)

        return JudgeData(
            itemInfo=_itemInfo,
            cropCoord=correctData.get_crop_coord(),
            pageId=correctData.get_page_id(),
            alignUrl=correctData.get_align_url(),
            originImageUrl=correctData.get_origin_image_url(),
            ksBools=correctData.is_ks_bools(),
            answered=correctData.get_answered(),
            height=correctData.get_h(),
            width=correctData.get_w(),
            bizType=bizType,
            correction=correctData.is_correction()
        )
