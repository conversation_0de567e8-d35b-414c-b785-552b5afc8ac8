# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/29 8:02 
# @Description  : data_of_answer_detect_response_info.py

class AnswerDetectResponseInfo:
    def __init__(self, flag=1000, boxs=None, types=None, recs=False, answers=None, stems=None):
        if boxs is None:
            boxs = {}
        if types is None:
            types = {}
        if recs is None:
            recs = {}
        if answers is None:
            answers = {}
        if stems is None:
            stems = {}
        self._flag = flag
        self._boxs = boxs
        self._types = types
        self._recs = recs
        self._answers = answers
        self._stems = stems

    def get_flag(self): return self._flag
    def set_flag(self, flag): self._flag = flag
    def get_boxs(self): return self._boxs
    def set_boxs(self, boxs): self._boxs = boxs
    def get_types(self): return self._types
    def set_types(self, types): self._types = types
    def get_recs(self): return self._recs
    def set_recs(self, recs): self._recs = recs
    def get_answers(self): return self._answers
    def set_answers(self, answers): self._answers = answers
    def get_stems(self): return self._stems
    def set_stems(self, stems): self._stems = stems