# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/7 10:49 
# @Description  : data_of_correction_api_response.py

import json
import time
from base_common import BaseResponse


class CorrectResponse(BaseResponse):
    def __init__(self, st=None, et=time.time(), message=None, error_code=None, success=False, response=None,
                 origin_image_url=None, book_id=None, page_id=None, align_info=None):
        super().__init__(st, et, message, error_code, success, response)
        self._origin_image_url = origin_image_url
        self._book_id = book_id
        self._page_id = page_id
        if not align_info:
            align_info = []
        self._align_info = align_info

    def get_align_info(self):
        if not self._align_info:
            self._align_info = []
        return self._align_info
    def set_align_info(self, align_info):
        if not align_info:
            align_info = []
        self._align_info = align_info
        return self
    def set_origin_image_url(self, origin_image_url=None):
        self._origin_image_url = origin_image_url
        return self
    def get_origin_image_url(self): return self._origin_image_url
    def set_book_id(self, book_id=None):
        self._book_id = book_id
        return self
    def get_book_id(self): return self._book_id
    def set_page_id(self, book_id=None):
        self._page_id = book_id
        return self
    def get_page_id(self): return self._page_id
    def to_dict(self) -> dict:
        resp = {"success": self._success}
        if self._message is not None:
            resp['message'] = self._message
        if self._origin_image_url is not None:
            resp['originImageUrl'] = self._origin_image_url
        if self._error_code is not None:
            resp['iErrorCode'] = self._error_code
            resp['errorCode'] = self._error_code
            if self._error_code == 20017 or self._error_code == 20022:
                resp['errorCode'] = 20011

        if self._book_id is not None:
            resp['bookId'] = self._book_id
        if self._page_id is not None:
            resp['pageId'] = self._page_id
        return resp

    def to_json(self):
        if not self._success:
            return self.to_dict()
        self._response['success'] = True
        if 'errorCode' in self._response:
            ec = self._response['errorCode']
            self._response['iErrorCode'] = ec
            if ec == 20017 or ec == 20022:
                self._response['errorCode'] = 20011
        return self._response

    def to_string(self, ensure_ascii=False) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False)
