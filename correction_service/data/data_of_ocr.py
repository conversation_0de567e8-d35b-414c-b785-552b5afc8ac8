# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 12:05 
# @Description  : data_of_ocr.py

from base_common import BaseData


class OcrData(BaseData):
    def __init__(self):
        self._names_text = []
        self._inputs_text = []

        self._names_en = []
        self._inputs_en = []
        self._inputs_en_ids = []

        self._names_formula = []
        self._inputs_formula = []

    def append_names_text(self, val):
        self._names_text.append(val)
        return self
    def append_inputs_text(self, val):
        self._inputs_text.append(val)
        return self
    def append_names_en(self, val):
        self._names_en.append(val)
        return self
    def append_inputs_en(self, val):
        self._inputs_en.append(val)
        return self
    def append_inputs_en_ids(self, val):
        self._inputs_en_ids.append(val)
        return self
    def append_names_formula(self, val):
        self._names_formula.append(val)
        return self
    def append_inputs_formula(self, val):
        self._inputs_formula.append(val)
        return self
    def get_names_text(self): return self._names_text
    def get_inputs_text(self): return self._inputs_text
    def get_names_en(self): return self._names_en
    def get_inputs_en(self): return self._inputs_en
    def get_inputs_en_ids(self): return self._inputs_en_ids
    def get_names_formula(self): return self._names_formula
    def get_inputs_formula(self): return self._inputs_formula

    def to_predicts(self, ocr_cn_resp, ocr_en_resp, ocr_formula_resp) -> dict:
        zip_data = zip(self._names_text + self._names_en + self._names_formula,
                                     ocr_cn_resp + ocr_en_resp + ocr_formula_resp)
        return {k: v for k, v in zip_data}
