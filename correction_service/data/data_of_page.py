# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 14:49 
# @Description  : data_of_page.py


import time
from base_common import BaseResponse


class PageData(BaseResponse):
    def __init__(self, st=None, et=time.time(), message=None, error_code=None, success=False, response=None,
                 page_path=None, page_info=None, item_info=None, col_info=None, crop_info=None,
                 page_id=None, img_page=None, img_ref=None, col_index=None, book_json=None,
                 crop_x_min=None, crop_y_min=None, crop_x_max=None, crop_y_max=None, page_url=None):
        super().__init__(st, et, message, error_code, success, response)
        self._page_path = page_path
        self._page_info = page_info
        self._item_info = item_info
        self._col_info = col_info
        self._crop_info = crop_info
        self._page_id = page_id
        self._img_page = img_page
        self._img_ref = img_ref
        self._col_index = col_index
        self._book_json = book_json
        self._crop_x_min = crop_x_min
        self._crop_y_min = crop_y_min
        self._crop_x_max = crop_x_max
        self._crop_y_max = crop_y_max
        self._page_url = page_url
        try:
            if self._item_info is not None:
                for item in self._item_info:
                    if 'itemType' not in item:
                        item['itemType'] = -1
        except:
            pass
    def set_page_url(self, page_url=None):
        self._page_url = page_url
        return self
    def get_page_url(self):
        return self._page_url

    def set_page_path(self, page_path=None):
        self._page_path = page_path
        return self

    def get_page_path(self): return self._page_path

    def set_page_info(self, page_info=None):
        self._page_info = page_info
        return self

    def get_page_info(self): return self._page_info

    def set_item_info(self, item_info=None):
        self._item_info = item_info
        return self

    def get_item_info(self): return self._item_info

    def set_col_info(self, col_info=None):
        self._col_info = col_info
        return self

    def get_col_info(self): return self._col_info

    def set_page_id(self, page_id=None):
        self._page_id = page_id
        return self

    def get_page_id(self): return self._page_id

    def set_img_page(self, img_page=None):
        self._img_page = img_page
        return self

    def get_img_page(self): return self._img_page

    def set_img_ref(self, img_ref=None):
        self._img_ref = img_ref
        return self

    def get_img_ref(self): return self._img_ref

    def set_crop_info(self, crop_info=None):
        self._crop_info = crop_info
        return self

    def get_crop_info(self): return self._crop_info

    def set_col_index(self, col_index=None):
        self._col_index = col_index
        return self

    def get_col_index(self): return self._col_index

    def set_book_json(self, book_json=None):
        self._book_json = book_json
        return self

    def get_book_json(self): return self._book_json

    def set_crop_x_min(self, crop_x_min=None):
        self._crop_x_min = crop_x_min
        return self

    def get_crop_x_min(self): return self._crop_x_min

    def set_crop_y_min(self, crop_y_min=None):
        self._crop_y_min = crop_y_min
        return self

    def get_crop_y_min(self): return self._crop_y_min

    def set_crop_x_max(self, crop_x_max=None):
        self._crop_x_max = crop_x_max
        return self

    def get_crop_x_max(self): return self._crop_x_max

    def set_crop_y_max(self, crop_y_max=None):
        self._crop_y_max = crop_y_max
        return self

    def get_crop_y_max(self): return self._crop_y_max

    def get_crop_rect(self):
        return self._crop_x_min, self._crop_y_min, self._crop_x_max, self._crop_y_max
