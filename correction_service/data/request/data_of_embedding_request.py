# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/8 12:01 
# @Description  : data_of_detection_request.py
import json
from base_common import BaseData


class EmbeddingRequest(BaseData):

    def __init__(self, input_1=None):
        self._input_1 = input_1

    def get_input_1(self):
        return self._input_1
    def set_input_1(self, input_1):
        self._input_1 = input_1

    def to_json(self):
        return json.dumps({'input_1': self._input_1})
