# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 14:23 
# @Description  : data_of_search_page_request.py
import json
import sys
sys.path.append('..')
from base_common import BaseData


class SearchPageRequest(BaseData):
    def __init__(self, book_id=None, image_url=None, coord=None, is_origin_image=None, user_image=None,
                 just_page=True, record_id=None, mission_id=None, base_path=None, direction=True, book_json=None):
        self._book_id = book_id
        self._image_url = image_url
        self._coord = coord
        self._is_origin_image = is_origin_image
        self._just_page = just_page
        self._record_id = record_id
        self._mission_id = mission_id
        self._base_path = base_path
        self._direction = direction
        self._user_image = user_image
        self._book_json = book_json

    def get_book_json(self): return self._book_json

    def set_book_json(self, book_json):
        self._book_json = book_json
        return self
    def get_book_id(self): return self._book_id

    def set_book_id(self, book_id):
        self._book_id = book_id
        return self
    def get_user_image(self): return self._user_image

    def set_user_image(self, user_image):
        self._user_image = user_image
        return self

    def get_image_url(self): return self._image_url

    def set_image_url(self, image_url):
        self._image_url = image_url
        return self

    def get_coord(self): return self._coord

    def set_coord(self, coord):
        self._coord = coord
        return self

    def is_origin_image(self): return self._is_origin_image

    def set_is_origin_image(self, is_origin_image):
        self._is_origin_image = is_origin_image
        return self

    def is_just_page(self): return self._just_page

    def set_just_page(self, just_page):
        self._just_page = just_page
        return self

    def get_record_id(self): return self._record_id

    def set_record_id(self, record_id):
        self._record_id = record_id
        return self
    def get_mission_id(self): return self._mission_id

    def set_mission_id(self, mission_id):
        self._mission_id = mission_id
        return self

    def get_base_path(self): return self._base_path

    def set_base_path(self, base_path):
        self._base_path = base_path
        return self

    def is_direction(self): return self._direction

    def set_direction(self, direction):
        self._direction = direction
        return self

    @classmethod
    def fromReq(cls, req_data):
        search_page_data = SearchPageRequest(
            book_id=req_data.get_book_id(),
            image_url=req_data.get_image_url(),
            coord=req_data.get_coord(),
            is_origin_image=req_data.is_origin_img(),
            record_id=req_data.get_record_id(),
            mission_id=req_data.get_mission_id()
        )
        return search_page_data


    def to_json(self):
        return json.dumps({
            "bookId": self._book_id,
            "imageUrl": self._image_url,
            "coord": self._coord,
            "isOriginImg": self._is_origin_image,
            "justPage": self._just_page,
            "recordId": self._record_id,
            "base_path": self._base_path,
            "direction": self._direction
        })
