# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 13:42 
# @Description  : data_of_answer_item.py

from base_common import BaseData


class AnswerItemData(BaseData):
    def __init__(self, w=None, h=None, item_res=None, data_images=None, reg_names=None, reg_coords=None,
                 ref_coords=None, items_type=None, items_answer=None, verify_cal_dict=None, img_align=None,
                 im_reg=None, crop_coord=None, standing_form_check=False, img_key=None, img_type=None):
        if reg_names is None:
            reg_names = []
        if crop_coord is None:
            crop_coord = []
        if reg_coords is None:
            reg_coords = dict()
        if verify_cal_dict is None:
            verify_cal_dict = dict()
        if items_type is None:
            items_type = dict()
        if items_answer is None:
            items_answer = dict()
        if ref_coords is None:
            ref_coords = dict()
        if item_res is None:
            item_res = dict()
        if data_images is None:
            data_images = dict()
        self._w = w
        self._h = h
        self._item_res = item_res
        self._data_images = data_images
        self._reg_names = reg_names
        self._reg_coords = reg_coords
        self._ref_coords = ref_coords
        self._items_type = items_type
        self._items_answer = items_answer
        self._verify_cal_dict = verify_cal_dict
        self._img_align = img_align
        self._im_reg = im_reg
        self._crop_coord = crop_coord
        self._img_key = img_key
        self._img_type = img_type
        self._standing_form_check = standing_form_check

    def set_standing_form_check(self, standing_form_check=False):
        self._standing_form_check = standing_form_check
        return self

    def get_standing_form_check(self):
        return self._standing_form_check

    def set_crop_coord(self, crop_coord=None):
        self._crop_coord = crop_coord
        return self

    def get_crop_coord(self):
        return self._crop_coord

    def set_img_key(self, img_key=None):
        self._img_key = img_key
        return self

    def get_img_key(self):
        return self._img_key
    def set_img_type(self, img_type=None):
        self._img_type = img_type
        return self

    def get_img_type(self):
        return self._img_type

    def set_im_reg(self, im_reg=None):
        self._im_reg = im_reg
        return self

    def get_im_reg(self):
        return self._im_reg

    def set_img_align(self, img_align=None):
        self._img_align = img_align
        return self

    def get_img_align(self):
        return self._img_align

    def set_items_answer(self, items_answer=None):
        self._items_answer = items_answer
        return self

    def get_items_answer(self):
        return self._items_answer

    def set_items_type(self, items_type=None):
        self._items_type = items_type
        return self

    def get_items_type(self):
        return self._items_type

    def set_ref_coords(self, ref_coords=None):
        self._ref_coords = ref_coords
        return self

    def get_ref_coords(self):
        return self._ref_coords

    def set_reg_coords(self, reg_coords=None):
        self._reg_coords = reg_coords
        return self

    def get_reg_coords(self):
        return self._reg_coords

    def set_reg_names(self, reg_names=None):
        self._reg_names = reg_names
        return self

    def get_reg_names(self):
        return self._reg_names

    def set_data_images(self, data_images=None):
        self._data_images = data_images
        return self

    def get_data_images(self):
        return self._data_images

    def set_item_res(self, item_res=None):
        self._item_res = item_res
        return self

    def get_item_res(self):
        return self._item_res

    def set_verify_cal_dict(self, verify_cal_dict=None):
        self._verify_cal_dict = verify_cal_dict
        return self

    def get_verify_cal_dict(self):
        return self._verify_cal_dict

    def set_h(self, h=None):
        self._h = h
        return self

    def get_h(self):
        return self._h

    def set_w(self, w=None):
        self._w = w
        return self

    def get_w(self):
        return self._w
