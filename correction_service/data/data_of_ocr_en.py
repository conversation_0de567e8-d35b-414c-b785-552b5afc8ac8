# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 11:55 
# @Description  : data_of_ocr_en.py
import json

from base_common import BaseData

class OcrEnData(BaseData):
    def __init__(self, record_id=None, the_input=None, inputs_en_ids=None, answer=None):
        self._record_id = record_id
        self._the_input = the_input
        self._inputs_en_ids = inputs_en_ids
        self._answer = answer

    def get_record_id(self): return self._record_id

    def get_the_input(self): return self._the_input

    def get_inputs_en_ids(self): return self._inputs_en_ids

    def get_answer(self): return self._answer

    def set_record_id(self, record_id):
        self._record_id = record_id
        return self

    def set_the_input(self, the_input):
        self._the_input = the_input
        return self

    def set_inputs_en_ids(self, inputs_en_ids):
        self._inputs_en_ids = inputs_en_ids
        return self

    def set_answer(self, answer):
        self._answer = answer
        return self

    def to_json(self):
        return json.dumps({
            "recordId": self._record_id,
            "the_input": self._the_input,
            "inputs_en_ids": self._inputs_en_ids,
            "answer": self._answer
        })
		
