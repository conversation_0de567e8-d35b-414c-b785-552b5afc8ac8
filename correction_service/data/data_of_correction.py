# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/7 14:05 
# @Description  : data_of_correction.py
import json

from base_common import BaseData
from correction_service.data.data_of_judge import JudgeData

"""
iteminfo array 批改题内容
correction bool 整页是否批改
crop_coord array 坐标
pageId long 页id
alignUrl string 批改图
originImageUrl string 用户上传图
ksBools bool 是否整页为智能口算
Answered int 有答案
h int 图片高
w int 图片宽
"""
class CorrectionData(BaseData):
    def __init__(self, item_info=None, answer_ks=None, stem_ks=None, rec_ks=None, correction=True,
                 reg_coords=None, reg_class=None, total_recs=None, item_res=None, reg_names=None,
                 reg_coords_=None, ref_coords=None, crop_coord=None, page_id=None, align_url=None,
                 origin_image_url=None, ks_bools=None, answered=None, h=None, w=None):
        self._itemInfo = item_info
        self._answerKs = answer_ks
        self._stemKs = stem_ks
        self._recKs = rec_ks
        self._correction = correction
        self._regCoords = reg_coords
        self._regClass = reg_class
        self._totalRecs = total_recs
        self._itemRes = item_res
        self._regNames = reg_names
        self._regCoords_ = reg_coords_
        self._refCoords = ref_coords
        self._cropCoord = crop_coord
        self._pageId = page_id
        self._alignUrl = align_url
        self._originImageUrl = origin_image_url
        self._ksBools = ks_bools
        self._answered = answered
        self._h = h
        self._w = w

    def get_item_info(self): return self._itemInfo

    def get_answer_ks(self): return self._answerKs

    def get_stem_ks(self): return self._stemKs

    def get_rec_ks(self): return self._recKs

    def is_correction(self): return self._correction

    def get_reg_coords(self): return self._regCoords

    def get_reg_class(self): return self._regClass

    def get_total_recs(self): return self._totalRecs

    def get_item_res(self): return self._itemRes

    def get_reg_names(self): return self._regNames

    def get_reg_coords_(self): return self._regCoords_

    def get_ref_coords(self): return self._refCoords

    def get_crop_coord(self): return self._cropCoord

    def get_page_id(self): return self._pageId

    def get_align_url(self): return self._alignUrl

    def get_origin_image_url(self): return self._originImageUrl

    def is_ks_bools(self): return self._ksBools

    def get_answered(self): return self._answered

    def get_h(self): return self._h

    def get_w(self): return self._w

    def set_item_info(self, item_info=None):
        self._itemInfo = item_info
        return self

    def set_answer_ks(self, answer_ks=None):
        self._answerKs = answer_ks
        return self

    def set_stem_ks(self, stem_ks=None):
        self._stemKs = stem_ks
        return self

    def set_rec_ks(self, rec_ks=None):
        self._recKs = rec_ks
        return self

    def set_correction(self, correction=True):
        self._correction = correction
        return self

    def set_reg_coords(self, reg_coords=None):
        self._regCoords = reg_coords
        return self

    def set_reg_class(self, reg_class=None):
        self._regClass = reg_class
        return self

    def set_total_recs(self, total_recs=None):
        self._totalRecs = total_recs
        return self

    def set_item_res(self, item_res=None):
        self._itemRes = item_res
        return self

    def set_reg_names(self, reg_names=None):
        self._regNames = reg_names
        return self

    def set_reg_coords_(self, reg_coords_=None):
        self._regCoords_ = reg_coords_
        return self

    def set_ref_coords(self, ref_coords=None):
        self._refCoords = ref_coords
        return self

    def set_crop_coord(self, crop_coord=None):
        self._cropCoord = crop_coord
        return self

    def set_page_id(self, page_id=None):
        self._pageId = page_id
        return self

    def set_align_url(self, align_url=None):
        self._alignUrl = align_url
        return self

    def set_origin_image_url(self, origin_image_url=None):
        self._originImageUrl = origin_image_url
        return self

    def set_ks_bools(self, ks_bools=None):
        self._ksBools = ks_bools
        return self

    def set_answered(self, answered=None):
        self._answered = answered
        return self

    def set_h(self, h=None):
        self._h = h
        return self

    def set_w(self, w=None):
        self._w = w
        return self

    def set_answer_item_info(self, answer_item):
        self.set_item_res(answer_item.get_item_res())
        self.set_reg_names(answer_item.get_reg_names())
        self.set_crop_coord(answer_item.get_crop_coord())
        self.set_ref_coords(answer_item.get_ref_coords())
        self.set_w(answer_item.get_w())
        self.set_h(answer_item.get_h())
        return self

    def to_dict(self) -> dict:
        return {
            "iteminfo": self._itemInfo, "answer_ks": self._answerKs, "stem_ks": self._stemKs,
            "rec_ks": self._recKs, "correction": self._correction, "reg_coords": self._regCoords,
            "reg_class": self._regClass, "total_recs": self._totalRecs, "item_res": self._itemRes,
            "reg_names": self._regNames, "reg_coords_": self._regCoords_, "ref_coords": self._refCoords,
            "crop_coord": self._cropCoord, "pageid": self._pageId, "alignurl": self._alignUrl,
            "originImageUrl": self._originImageUrl, "ks_bools": self._ksBools,
            "Answered": self._answered, "h": self._h, "w": self._w
        }

    def transform_to_new_data(self, bizType):
        return JudgeData.fromCorrection(self, bizType)

    def to_json(self, ensure_ascii=False):
        return json.dumps(self.to_dict(), ensure_ascii=ensure_ascii)
