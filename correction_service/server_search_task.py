# -*- coding: utf-8 -*-#
'''主要为拍照搜页异步任务的获取及执行'''

import time
import json
import uuid
import socket
import requests
import traceback
import concurrent.futures
import datetime
import tornado.web
import tornado.ioloop
import tornado.httpserver
from base_common import LoggerFactory, Context
from base_common.service.service_of_redis import RedisManager

log = LoggerFactory.get_logger('search_task', 'search_task')
biz_type_code = 'camera_recognize_page'
class StopHandler(tornado.web.RequestHandler):
    taskRunner = None
    def initialize(self, _runner):
        StopHandler.taskRunner = _runner
    def get(self):
        log.info(f'停止获取异步任务')
        StopHandler.taskRunner.stop()
        self.write('task stoped.')

    def post(self):
        log.info(f'停止获取异步任务')
        StopHandler.taskRunner.stop()
        self.write('task stoped.')

class Worker:
    def __init__(self, redisService, threads, drop):
        self._redisService = redisService
        self._threads = threads
        self._timeout = drop
        self._lost_task_key = None
        self._increment_task = False
        self._stop_flag = False
        self._task_req_url = Context.get_task_req_url()
        self._task_resp_url = Context.get_task_resp_url()
        self._notify_url = Context.get_notify_url()
        self._correction_url = 'http://127.0.0.1:8887/search'
        self._working_count_key = "SEARCH_WORKING_COUNT"
        self._task_req_data = {'bizTypeCode': biz_type_code, 'workGroup': 'product' if Context.is_product() else 'daily', 'getLimit': 1}
    def _updateDrop(self):
        try:
            self._redisService.increment(self._lost_task_key)
        except:
            log.error(f'上报丢失任务异常 \n{traceback.format_exc()}')
    def _updateThreads(self, prefix, num):
        try:
            today = datetime.date.today().strftime("%Y-%m-%d")
            self._lost_task_key = f"{today}:lost_search_count"
            self._redisService.set(f"{today}:search_threads:{prefix}_{num}", "1")
        except:
            pass
    def _notify(self, message):
        try:
            response = requests.post(self._notify_url, data=message)
            log.debug(f"request notify feishu success {response.text}")
        except:
            log.error(f"推送飞书消息失败!{traceback.format_exc()}")

    def run(self, prefix, num):
        log.info(f'取任务线程{num}开始工作...')
        while not self._stop_flag:
            try:
                resp = requests.post(url=self._task_req_url, params=self._task_req_data, timeout=10)
                if not resp:
                    log.warn(f'{num}访问搜页任务失败！')
                    if Context.is_product():
                        self._notify(f"无法从异步任务获取到搜页任务".encode('utf-8'))
                else:
                    log.debug(f"{num}访问搜页任务成功！")
                    self._updateThreads(prefix, num)
                    data_all = resp.json().get('data', [])
                    if len(data_all) > 0:
                        log.debug(f"{num}获取到1条任务")
                        data_all = data_all[0]
                        ctime = data_all['gmtCreate'] / 1000.0
                        params = json.loads(data_all['bizParams'])
                        task_create_date = datetime.datetime.fromtimestamp(ctime)
                        log.info(f"(record_id:{params['recordId']}) 异步任务创建于: {task_create_date}, 任务参数 {params}")

                        params.update({'create_date': ctime, 'current_thread': num, 'total_thread': self._threads})
                        if (time.time() - ctime) > self._timeout and Context.is_product():
                            params.update({'errorCode': 20013,"originImageUrl": params['imageUrl']})
                            message = {'bizTypeCode': biz_type_code, 'result': json.dumps(params),
                                       'id': data_all['id']}
                            log.warn(f"(record_id:{params['recordId']}) 任务已经超过{self._timeout}秒，不进行搜页。响应20013")
                            self._updateDrop()
                        else:
                            message = self._do_mission(data_all, params)
                        try:
                            log.info(f"(record_id:{params['recordId']}) 将搜页结果推送到异步任务池,"
                                     f"任务结果 {json.dumps(message, ensure_ascii=False)}")
                            resp_ = requests.post(url=self._task_resp_url, data=message)
                            log.info(f"(record_id:{params['recordId']}) 推送搜页结果结束,"
                                     f"服务器响应：{resp_.status_code} {resp_.text}")
                        except:
                            log.error(f"report correction result error\n{traceback.format_exc()}")
                    else:
                        log.debug(f"{num}当前无任务")
                        time.sleep(0.1)
            except:
                log.error(f'异步任务线程{num}异常，\n{traceback.format_exc()}')

        log.info(f'取任务线程{num}停止.')
        return None

    def stop(self):
        self._stop_flag = True
    def _do_mission(self, data_all, params):
        record_id = params.get('recordId', None)
        if record_id is None:
            params.update({'errorCode': 20012})
            message = {'bizTypeCode': biz_type_code, 'result': json.dumps(params), 'id': data_all['id']}
            log.error(f"(record_id:{record_id}) 任务参数异常，响应20012")
            return message
        unikey = self._working_count_key + ":" + str(uuid.uuid4()).replace("-", '').upper()
        self._redisService.set(unikey, "1", 16)
        try:
            log.info(f"(record_id:{record_id}) 请求搜页主程序...")
            judge_start_time = time.time()
            resp_pg = requests.post(url=self._correction_url, data=json.dumps(params), timeout=40)
            log.info(
                f"(record_id:{record_id}) 主程序处理完成 Code: {resp_pg.status_code}，耗时： {'%.2f seconds.' % (time.time() - judge_start_time)}")
            if resp_pg.status_code == 200:
                message = {'bizTypeCode': biz_type_code, 'record_id': params['recordId'],
                           'result': resp_pg.text, 'id': data_all['id']}
            else:
                params.update({'errorCode': 20012,"originImageUrl": params['imageUrl']})
                message = {'bizTypeCode': biz_type_code, 'result': json.dumps(params), 'id': data_all['id']}
            return message
        except:
            log.error(f"(record_id:{record_id}) 任务处理异常 \n{traceback.format_exc()}")
            params.update({'errorCode': 20012, "originImageUrl": params['imageUrl']})
            message = {'bizTypeCode': biz_type_code, 'result': json.dumps(params), 'id': data_all['id']}
            return message
        finally:
            try:
                self._redisService.remove(unikey)
            except:
                log.error(f"(record_id:{record_id}) 删除redis key {unikey} 失败！\n{traceback.format_exc()}")
class Runner:
    def __init__(self):
        self._redisService = RedisManager.get_mission_queue_redis()
        try:
            self._prefix_str = socket.gethostbyname(socket.gethostname())
        except:
            self._prefix_str = str(uuid.uuid4()).replace('-', '')

        self._thread = 8
        self._timeout = 10
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=self._thread)
        self._futures = []
        self._workers = []
        self._stop_flag = False

    def start(self):
        # 提交任务到线程池
        redisService = RedisManager.get_mission_queue_redis()
        for i in range(self._thread):
            _worker = Worker(redisService, self._thread, self._timeout)
            self._workers.append(_worker)
            self._futures.append(self._executor.submit(_worker.run, self._prefix_str, i))

    def stop(self):
        self._stop_flag = True
        for _worker in self._workers:
            _worker.stop()
        self._executor.shutdown(wait=True)

if __name__ == '__main__':
    Context.setup()
    runner = Runner()
    runner.start()
    _app = tornado.web.Application([(r"/stop", StopHandler, dict(_runner=runner))])
    _server = tornado.httpserver.HTTPServer(_app)
    _server.listen(18887)
    log.info(f"Tornado server started on port 18887")

    try:
        tornado.ioloop.IOLoop.current().start()
    except KeyboardInterrupt:
        log.info("Stopping server...")
        runner.stop()
        log.info("Server stopped and all tasks completed.")
