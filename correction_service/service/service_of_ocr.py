# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/7 15:30 
# @Description  : service_of_ocr.py
import json
import time
import asyncio
import traceback

from text2vec.utils.tokenizer import Tokenizer

from base_common.service.service_of_base import BaseService
from base_common.auto_setting import AutoSetting
from base_common.mission_mode import MissionAlisa
from base_common.service.service_of_redis import RedisManager
from correction_service.common import CorrectionContext
from correction_service.data import OcrData, CorrectRequest, OcrEnData, OcrCnData, \
    OcrFormulaData, BookResponse, AnswerItemData
from base_common import Context, ProcessError as PE, LoggerFactory, \
    Constants, FormulaUtil, ImageUtil, RedisRequestor, BoxUtil

log = LoggerFactory.get_logger('OcrService')
one_piece_print_ocr_size = 8
one_piece_ch_ocr_size = 6
one_piece_en_ocr_size = 4
one_piece_formula_ocr_size = 8
def is_same_row(box1, box2, tolerance=-3):
    y1_min = min(point[1] for point in box1)
    y1_max = max(point[1] for point in box1)
    y2_min = min(point[1] for point in box2)
    y2_max = max(point[1] for point in box2)

    return (y1_max + tolerance >= y2_min) and (y2_max + tolerance >= y1_min)
def sort_box(boxes_list):
    rows = []
    visited = [False] * len(boxes_list)

    for i in range(len(boxes_list)):
        if visited[i]:
            continue
        current_row = [boxes_list[i]]
        visited[i] = True
        for j in range(i + 1, len(boxes_list)):
            if not visited[j] and is_same_row(boxes_list[i], boxes_list[j]):
                current_row.append(boxes_list[j])
                visited[j] = True
        rows.append(current_row)

    rows.reverse()

    # 对每一行的框根据横坐标排序
    sorted_boxes = []
    for row in rows:
        sorted_row = sorted(row, key=lambda box: min(point[0] for point in box))
        sorted_boxes.extend(sorted_row)

    return sorted_boxes

def filter_boxes(box_list):
    def aspect_ratio(box):
        x1, y1 = box[0]
        x2, y2 = box[1]
        x3, y3 = box[2]
        x4, y4 = box[3]
        min_x = min([x1, x2, x3, x4])
        max_x = max([x1, x2, x3, x4])
        min_y = min([y1, y2, y3, y4])
        max_y = max([y1, y2, y3, y4])
        width = max_x - min_x
        height = max_y - min_y

        return height / width if height > width else width / height

    # 计算长宽比，并与框一起排序
    box_with_ratio = [(i, box, aspect_ratio(box)) for i, box in enumerate(box_list)]
    box_with_ratio.sort(key=lambda x: x[2], reverse=True)

    # 选择前30个框，并保持顺序
    top_30_boxes_with_index = sorted(box_with_ratio[:30], key=lambda x: x[0])
    top_30_boxes = [box for _, box, _ in top_30_boxes_with_index]
    boxes_list = top_30_boxes
    return boxes_list

def get_maxsize(imgs, pad_size=None):
    if pad_size is None:
        pad_size = [0, 0, 0, 0]
    top, left, bottom, right = pad_size
    widths = []
    heights = []
    for img in imgs:
        # update by dewen
        if isinstance(img, list):
            for item in img:
                h, w, _ = item.shape
                old_size = (w + left + right, h + top + bottom)
                new_size, _ = FormulaUtil.get_new_size(old_size, Constants.BUCKETS)
                widths.append(new_size[0])
                heights.append(new_size[1])
        else:
            h, w, _ = img.shape
            old_size = (w + left + right, h + top + bottom)
            new_size, _ = FormulaUtil.get_new_size(old_size, Constants.BUCKETS)
            widths.append(new_size[0])
            heights.append(new_size[1])
        # update by dewen
        #h, w, _ = img.shape
        #old_size = (w + left + right, h + top + bottom)
        #new_size, _ = FormulaUtil.get_new_size(old_size, Constants.BUCKETS)
        #widths.append(new_size[0])
        #heights.append(new_size[1])
    for (w_b, h_b) in Constants.BUCKETS:
        if w_b >= max(widths) and h_b >= max(heights):
            return [w_b, h_b]
    return [max(widths), max(heights)]

async def exec_ocr_cn_async(req_data: CorrectRequest, ocr_data: OcrData, item_info):
    # 中文文本识别
    t0 = time.time()
    inputs_text = ocr_data.get_inputs_text()
    names_text = ocr_data.get_names_text()

    batch_imgs = []
    empty_indexs = []
    for index in range(len(inputs_text)):
        d = inputs_text[index]
        if d is not None and d[0] is not None:
            try:
                base64_str = ImageUtil.numpy_2_base64(d[0])
                batch_imgs.append(base64_str)
            except:
                if index < len(names_text):
                    log.warn(f"中文识别图片异常，已使用空白图片处理 key：{names_text[index]}")
                empty_indexs.append(index)
                batch_imgs.append(Constants.EMPTY_IMG)
    if len(batch_imgs) == 0:
        return [], time.time() - t0

    batch_labels = [''] * len(batch_imgs)
    if item_info is not None:
        for item in item_info:
            try:
                if 'answerInfo' not in item:
                    continue
                #log.info(f"item {item}")
                answer_infos = item['answerInfo']
                for index, answer_info in enumerate(answer_infos):  # 判断，单选，多选
                    pname = str(item['itemId'])
                    if item['itemType'] == 14: #多选
                        asname = answer_info['blockName'] + '_' + str(0)
                        answer = answer_info['answer'][0]['value']
                        answer = answer.replace(',','@@@')
                        if asname in names_text:
                            batch_labels[names_text.index(asname)] = answer
                    
                    elif 'blockName' not in answer_info:
                        asname = pname + '_' + str(index)
                        answer = '@@@'.join(answer_info['answer'])
                        if asname in names_text:
                            batch_labels[names_text.index(asname)] = answer

                    elif answer_info['blockType'] == 0:  # 单空单答案
                        asname = answer_info['blockName'] + '_' + str(0)
                        answer = answer_info['answer'][0]['value']
                        if asname in names_text:
                            batch_labels[names_text.index(asname)] = answer
                            
                    elif answer_info['blockType'] == 3 and answer_info.get('relation', None) == 0:  # 单空多答案“或”
                        asname = answer_info['blockName'] + '_' + str(0)
                        answers = answer_info['answer']
                        answer_list = []
                        for ans in answers:
                            answer_list.append(ans['value'])
                        answer_str = '\t'.join(answer_list)
                        if asname in names_text:
                            batch_labels[names_text.index(asname)] = answer_str

                    elif answer_info['blockType'] == 2:  # 多空多答案
                        an_final = []
                        for ii in range(len(answer_info['answer'])):
                            an_final.append(answer_info['answer'][ii]['value'])
                        an_final_str = '\t'.join(an_final)

                        for ii in range(len(answer_info['answer'])):
                            asname = answer_info['blockName'] + '_' + str(ii)
                            if asname in names_text:
                                batch_labels[names_text.index(asname)] = an_final_str
                    else:  # 应用题，连线题等
                        continue
            except Exception as e:
                log.error(f"Oops!!! --error ocr_ch_get_answer {traceback.format_exc()}")
    #log.info(f"batch_labels {batch_labels}")
    data_len = len(batch_imgs)
    start_index = 0
    futures = []
    while start_index < data_len:
        end_index = min(data_len, start_index + one_piece_ch_ocr_size)
        ocr_cn_data = OcrCnData(req_data.get_record_id())
        ocr_cn_data.set_batch_labels(batch_labels[start_index:end_index])
        ocr_cn_data.set_batch_imgs(batch_imgs[start_index:end_index])
        futures.append(RedisRequestor.ai_ocr_hand_write_cn(ocr_cn_data.to_json(), req_data.get_record_id()))
        start_index += one_piece_ch_ocr_size

    resps = await asyncio.gather(*futures)
    result = []
    for resp in resps:
        if resp.is_success():
            result.extend(resp.get_response())
        else:
            Context.report_error(req_data.get_mission_id(), PE.CN_HANDWRITE_OCR)
    if len(empty_indexs) > 0:
        for index in empty_indexs:
            if index < len(result):
                result[index] = ''
    return result, time.time() - t0

async def exec_ocr_en_async(req_data: CorrectRequest, ocr_data: OcrData, item_info):
    # 英文文本识别
    t0 = time.time()
    inputs_en = ocr_data.get_inputs_en()
    inputs_en_ids = ocr_data.get_inputs_en_ids()
    batch_imgs = []
    empty_indexs = []
    for index in range(len(inputs_en)):
        try:
            d = inputs_en[index]
            if d is not None:
                base64_str = ImageUtil.numpy_2_base64(d)
                batch_imgs.append(base64_str)
        except:
            if index < len(inputs_en):
                log.warn(f"英文识别图片异常，已使用空白图片处理 key：{inputs_en_ids[index]}")
            empty_indexs.append(index)
            batch_imgs.append(Constants.EMPTY_IMG)

    if len(batch_imgs) != 0:
        data_len = len(batch_imgs)
        start_index = 0
        futures = []
        if item_info is not None:
            try:
                en_answer = {}
                for item in item_info:
                    if 'answerInfo' not in item:
                        continue
                    answer_infos = item['answerInfo']
                    for answer_info in answer_infos:
                        if 'blockName' not in answer_info or 'answer' not in answer_info:
                            continue
                        pname = answer_info['blockName']
                        ct = 0
                        for answer in answer_info['answer']:
                            if isinstance(answer, list):
                                continue
                            asname = pname + '_' + str(ct)
                            ct += 1
                            if 'value' not in answer:
                                continue
                            en_answer[asname] = answer['value']
            except Exception as e:
                log.error(f"Oops!!! --error ocr_en_get_answer {e}")

        while start_index < data_len:
            end_index = min(data_len, start_index + one_piece_en_ocr_size)
            ocr_en_data = OcrEnData(req_data.get_record_id(), batch_imgs[start_index:end_index])
            ocr_en_data.set_inputs_en_ids(inputs_en_ids[start_index:end_index])
            ocr_en_data.set_answer(en_answer)
            futures.append(RedisRequestor.ai_ocr_hand_write_en(ocr_en_data.to_json(), req_data.get_record_id()))
            start_index += one_piece_en_ocr_size
        resps = await asyncio.gather(*futures)
        result = []
        for resp in resps:
            if resp.is_success():
                result.extend(resp.get_response())
            else:
                Context.report_error(req_data.get_mission_id(), PE.EN_HANDWRITE_OCR)
        if len(empty_indexs) > 0:
            for index in empty_indexs:
                if index < len(result):
                    result[index] = ''
        return result, time.time() - t0
    return [], time.time() - t0

async def exec_ocr_formula_async(req_data: CorrectRequest, ocr_data: OcrData):
    # 公式文本识别
    t0 = time.time()
    names_formula = ocr_data.get_names_formula()
    inputs_formula = ocr_data.get_inputs_formula()
    bsf = one_piece_formula_ocr_size
    steps = len(names_formula) // bsf if len(names_formula) % bsf == 0 else len(
        names_formula) // bsf + 1
    futures = []
    empty_indexs = []
    for i in range(steps):
        batch_inputs = inputs_formula[i * bsf:(i + 1) * bsf]
        max_size = get_maxsize(batch_inputs)
        batch_imgs = []
        for index in range(len(batch_inputs)):
            d = batch_inputs[index]
            try:
                base64_str = ImageUtil.numpy_2_base64(d)
                batch_imgs.append(base64_str)
            except:
                log.warn(f"公式识别图片异常，已使用空白图片处理")
                empty_indexs.append(index + i)
                batch_imgs.append(Constants.EMPTY_IMG)

        ocr_formula_data = OcrFormulaData(record_id=req_data.get_record_id(),
                                          batch_images=batch_imgs,
                                          max_size=max_size)
        futures.append(RedisRequestor.ai_ocr_hand_write_formula(ocr_formula_data.to_json(), req_data.get_record_id()))

    resps = await asyncio.gather(*futures)
    pred_chars_formula = []
    for model_response in resps:
        if model_response.is_success():
            resp = model_response.get_response()
            pred_chars_formula += resp
        else:
            Context.report_error(req_data.get_mission_id(), PE.FORMULA_HANDWRITE_OCR)
    if len(empty_indexs) != 0:
        for index in empty_indexs:
            if index < len(pred_chars_formula):
                pred_chars_formula[index] = ''
    return pred_chars_formula, time.time() - t0

async def exec_ocr_async(req_data: CorrectRequest, ocr_data: OcrData, item_info) -> dict:
    futures = [
        exec_ocr_cn_async(req_data, ocr_data, item_info),
        exec_ocr_en_async(req_data, ocr_data, item_info),
        exec_ocr_formula_async(req_data, ocr_data)
    ]
    results = await asyncio.gather(*futures)
    ocr_cn_resp, tc = results[0]
    ocr_en_resp, te = results[1]
    ocr_formula_resp, tf = results[2]
    mission_id = req_data.get_mission_id()
    if tc > 1e-4:
        # log.info(f"手写中文识别结果：{ocr_cn_resp}")
        Context.report_cost(mission_id, (MissionAlisa.CH_HW_SERVICE, tc))
    if te > 1e-4:
        # log.info(f"手写英文识别结果：{ocr_en_resp}")
        Context.report_cost(mission_id, (MissionAlisa.EN_HW_SERVICE, te))
    if tf > 1e-4:
        # log.info(f"手写公式识别结果：{ocr_formula_resp}")
        Context.report_cost(mission_id, (MissionAlisa.FORMULA_HW_SERVICE, tf))
    return ocr_data.to_predicts(ocr_cn_resp, ocr_en_resp, ocr_formula_resp)

class OcrService(BaseService):
    def __init__(self):
        super().__init__()
        self.tokenizer = Tokenizer()
        self._print_urls = {}
        self.redis_service = RedisManager.get_mission_data_redis()
        self.mission_queue_redis = RedisManager.get_mission_queue_redis()

    def update_batch_size(self):
        global one_piece_print_ocr_size, one_piece_ch_ocr_size, one_piece_en_ocr_size, one_piece_formula_ocr_size
        try:
            config = AutoSetting.get_config()
            one_piece_print_ocr_size = config['one_piece_print_ocr_size']
            one_piece_ch_ocr_size = config['one_piece_ch_ocr_size']
            one_piece_en_ocr_size = config['one_piece_en_ocr_size']
            one_piece_formula_ocr_size = config['one_piece_formula_ocr_size']
        except:
            one_piece_print_ocr_size = 8
            one_piece_ch_ocr_size = 6
            one_piece_en_ocr_size = 4
            one_piece_formula_ocr_size = 8

    def do_hand_write_ocr(self, req_data: CorrectRequest, answer_item: AnswerItemData, book_data: BookResponse, resp_detect, reg_coords):
        data_images, rec_empty = self._prepare_hand_write_data(req_data, answer_item, resp_detect, reg_coords)
        self.update_batch_size()

        # 调用识别模型，其中包括公式识别，中文识别，英文识别三种识别模型。
        ocr_data = self._build_ocr_data(data_images)
        predicts = asyncio.run(exec_ocr_async(req_data, ocr_data, book_data.get_page_data().get_item_info()))
        total_recs = dict(predicts, **rec_empty)
        correction_data = CorrectionContext.get_correct_data(req_data.get_mission_id())
        reg_class = correction_data.get_reg_class()
        if reg_class is not None and predicts is not None:
            for key, val in reg_class.items():
                if key in predicts:
                    value = predicts.get(key, None)
                    if value is not None and 'frac' in value:
                        reg_class[key] = 1
                        
        items_type = answer_item.get_items_type()
        rec_ks = correction_data.get_rec_ks()
        for item_block_id, item_block_rec_ret in rec_ks.items():
            local_item_id = item_block_id.split("_")[0]
            local_item_type = items_type[local_item_id]
            if local_item_type == 28 or local_item_type == 27:
                total_recs[item_block_id] = item_block_rec_ret

        if not Context.is_product():
            log.info(f"(mission_id: {req_data.get_mission_id()}) 所有手写识别结果：{json.dumps(total_recs, ensure_ascii=False)}")
        correction_data.set_total_recs(total_recs)

    def _prepare_hand_write_data(self, req_data: CorrectRequest, answer_item: AnswerItemData, resp_detect, reg_coords):
        t0 = time.time()
        # types字段 0:单行文本; 1:分数; -1:空(连线题标准答案与实际均无连线); 10:连线题作答正确; 11:连线题作答错误; 12:连线题作答半对;
        reg_class = resp_detect['types']
        rec_ks = resp_detect.get('recs', None)
        answer_ks = resp_detect.get('answers', None)
        stem_ks = resp_detect.get('stems', None)
        answered = 0 if (set(reg_class.values()) == {-1} or len(reg_class) == 0) else 1

        # 根据答案信息覆盖识别答案的类型，1为公式，2为英语
        data_imgs = answer_item.get_data_images()
        items_type = answer_item.get_items_type() # update by dewen
        for k, v in data_imgs.items():
            if v[1] == 'latex' or items_type[k.split('_')[0]] == 18: # update by dewen
            #if v[1] == 'latex':
                for k1, v1 in reg_class.items():
                    if k in k1:
                        reg_class[k1] = 0 # 1 
            elif v[1] == 'english':
                for k1, v1 in reg_class.items():
                    if k in k1:
                        reg_class[k1] = 2

        rec_empty = {k: '' for k, _ in reg_coords.items() if not reg_coords[k]}
        data_images = {}
        # 根据检测的坐标收集图片，并排除掉连线题，作图题区域。
        items_type = answer_item.get_items_type()
        for k, v in reg_coords.items():
            item_id = k.split("_")[0]
            item_type = items_type.get(item_id, None)
            if item_type is None or item_type == -1:  # 未开启批改题型
                continue

            if reg_coords[k] and k not in stem_ks.keys():
                if k in data_imgs.keys():
                    if data_imgs[k][1] not in ['lianxian', 'zuotu']:
                        tmp_img = ImageUtil.cut_img(answer_item.get_im_reg(), reg_coords[k])
                        data_images[k] = [tmp_img, data_imgs[k][1], reg_class[k]]  # reg class : 识别类型
                else:
                    assert len(k.split('_')) == 4
                    k_ = '_'.join(k.split('_')[:-1])
                    if data_imgs[k_][1] not in ['lianxian', 'zuotu']:
                        tmp_img = ImageUtil.cut_img(answer_item.get_im_reg(), reg_coords[k])
                        data_images[k] = [tmp_img, data_imgs[k_][1], reg_class[k]]

        reg_coords_ = answer_item.get_reg_coords()
        for k, v in reg_coords.items():  # Not any user input area return coord
            if len(v) == 0 and k in reg_coords_:
                reg_coords[k] = reg_coords_[k]
        for k, v in reg_coords_.items():
            if len(v) != 0 and k not in reg_coords:
                reg_coords[k] = reg_coords_[k]
        correction_data = CorrectionContext.get_correct_data(req_data.get_mission_id())
        correction_data.set_answer_ks(answer_ks)
        correction_data.set_stem_ks(stem_ks)
        correction_data.set_rec_ks(rec_ks)
        correction_data.set_reg_class(reg_class)
        correction_data.set_reg_coords(reg_coords)
        correction_data.set_reg_coords_(reg_coords_)
        correction_data.set_answered(answered)
        Context.report_cost(req_data.get_record_id(), ('手写检测准备', time.time() - t0))
        return data_images, rec_empty

    def exec_predict_ocr(self, req_data, img_user, img_key=None, img_type=None):
        t0 = time.time()
        mission_id = req_data.get_mission_id()
        if img_key is None:
            img_key, img_type = self.set_image(img_user)
        data_json = {'img_key': img_key, 'img_type': img_type}
        model_response = RedisRequestor.ai_print_detect(data_json, req_data.get_record_id())
        if not model_response.is_success():
            Context.report_error(mission_id, PE.PRINTE_OCR_DET)
            log.error(f'(mission_id: {mission_id}) 印刷体检测模型调用失败')
            return None
        boxes_list = model_response.get_json_response()
        if len(boxes_list) == 0:
            Context.report_error(mission_id, PE.PRINTE_OCR_DET)
            log.error(f'(mission_id: {mission_id}) 印刷体检测模型调用失败')
            return None
        boxes_list = sort_box(boxes_list)
        boxes_list = filter_boxes(boxes_list)
        Context.report_cost(mission_id, (MissionAlisa.PRINT_DET_SERVICE, time.time() - t0))
        return self._do_print_ocr(req_data, img_key, img_type, boxes_list)

    def _do_print_ocr(self, req_data, img_key, img_type, boxes_list):
        try:
            t0 = time.time()
            mission_id = req_data.get_mission_id()
            #log.info(f"(mission_id: {mission_id}) 印刷体识别模型调用开始")
            results = asyncio.run(self._do_print_ocr_rec(boxes_list, img_key, img_type, req_data.get_record_id()))
            seg_text_ori = self.tokenizer.tokenize(results)
            seg_text = []
            for t in seg_text_ori:
                seg_text.extend(BoxUtil.custom_analyzer(t))
            seg_text = [s for s in seg_text if s != ' ']
            Context.report_cost(mission_id, (MissionAlisa.PRINT_REC_SERVICE, time.time() - t0))
            return seg_text
        except:
            log.error(f'(mission_id: {mission_id}) 印刷体识别模型调用失败 {traceback.format_exc()}')
            Context.report_error(mission_id, PE.PRINTE_OCR_REC)
            return None

    async def _do_print_ocr_rec(self, data_list, img_key, img_type, record_id):
        futures = []
        data_len = len(data_list)
        start_index = 0

        while start_index < data_len:
            end_index = min(data_len, start_index + one_piece_print_ocr_size)
            req_data = {
                'rec_box': data_list[start_index:end_index],
                'img_key': img_key,
                'img_type': img_type
            }
            futures.append(RedisRequestor.ai_print_rec(req_data, record_id))
            start_index += one_piece_print_ocr_size

        resps = await asyncio.gather(*futures)
        final_results = []
        for model_response in resps:
            items = model_response.get_json_response()['result']
            final_results.append(''.join(items))

        return ''.join(final_results)

    def _build_ocr_data(self, data_images) -> OcrData:
        ocr_data = OcrData()
        for k, v in data_images.items():
            if v[2] == 2:
                ocr_data.append_names_en(k)
                ocr_data.append_inputs_en(v[0])
                ocr_data.append_inputs_en_ids(k)
                
            else:
                if isinstance(v[0], list):
                    for i in range(len(v[0])):
                        ocr_data.append_names_text(k + '_' + str(i))
                        ocr_data.append_inputs_text([v[0][i]])
                else:
                    ocr_data.append_names_text(k)
                    ocr_data.append_inputs_text(v[:-1])

        return ocr_data
