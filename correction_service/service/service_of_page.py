# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/8 10:47
# @Description  : service_of_search_page.py
import os
import time
import base64

import cv2
import numpy
import traceback

from correction_service.data import PageData
from correction_service.data import CorrectRequest
from correction_service.data import SearchPageResponse

from base_common import FileUtil, Constants, LoggerFactory

log = LoggerFactory.get_logger('PageService')

class PageService:

    def correct_page_data(self, req_data: CorrectRequest, sch_page: SearchPageResponse, data_book) -> PageData:
        """
        根据搜页信息从本地文件中读取书籍的json信息。
        参数:
        :param req_data: 包含搜索请求所需信息的数据对象。
        :param sch_page: 搜页请求的结果集
        :param data_book: 本地书籍信息。
        返回:
        :return: 返回书籍对象@ApiPageData，如果请求失败，则包含错误信息。
        """
        t0 = time.time()
        #log.info("收集书籍信息流程启动")
        # 获取搜索页面的响应数据
        resp = sch_page.get_response()
        res_page_id = resp.get('pageId')
        page_data = self._build_page_data(data_book, res_page_id)
        if not page_data.is_success():
            t0 = time.time() - t0
            log.warn(f"收集书籍信息流程结束，加载本地书籍失败. {t0:.4f} sec")
            return page_data

        # 解码图片数据
        # img_data = base64.b64decode(resp['image'])
        # nparr = numpy.fromstring(img_data, numpy.uint8)
        # img_page = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        page_data.set_img_page(resp['image'])
        try:
            FileUtil.write_file('./record_align_data.txt',
                                f'bookId: {page_data.get_book_json()["bookId"]} pageId: {res_page_id}\t' + req_data.get_image_url() + '\n',
                                'a')
        except:
            pass

        return page_data

    def _build_page_data(self, data_book, res_page_id: str) -> PageData:
        try:
            # 默认列索引值
            col_index = "default"
            if '_' in res_page_id:
                # 如果页面ID包含'_', 表明有分栏信息
                page_id, col_index = res_page_id.split('_')
                page = self._find_page(data_book, page_id)
                page_info = page['items']
                item_info = [item for item in page_info if item['column'] == int(col_index)]
                col_info = self._find_col(page, res_page_id)
                crop_x_min, crop_y_min, crop_x_max, crop_y_max = col_info['coord']
                page_data = PageData(page_path=page['localPagePath'], page_id=page_id, page_info=page_info,
                                     item_info=item_info, col_info=col_info, crop_info=col_info['coord'],
                                     book_json=data_book, crop_x_min=crop_x_min, crop_y_min=crop_y_min,
                                     crop_x_max=crop_x_max, crop_y_max=crop_y_max, page_url=page.get("pageUrl", None))
            else:
                page_id = res_page_id
                page = self._find_page(data_book, page_id)
                page_info = page['items']
                page_data = PageData(page_path=page['localPagePath'], page_id=page_id, page_info=page_info,
                                     item_info=page_info, book_json=data_book, page_url=page.get("pageUrl", None))

            page_data.set_col_index(col_index)
            page_data.set_success(True)
            return page_data
        except BaseException:
            log.error(f"Oops!!! load column info from bookdatas failed, due to {traceback.format_exc()}")
            return PageData()

    def _find_page(self, data_book, page_id):
        for page in data_book['bookItems']:
            if page['pageId'] == int(page_id):
                return page
        return None

    def _find_col(self, page, res_page_id):
        for col in page['pageColums']['colInfo']:
            if col['colId'] == res_page_id:
                return col
        return None

    def _correct_page_path(self, req_data: CorrectRequest, page_path: str, base_path: str) -> str:
        before_path = page_path
        new_part = "new" if req_data.is_new_correct() else "old"
        if f"/mnt/bookdatas_v1_{new_part}/" in page_path:
            page_path = page_path.replace(f"/mnt/bookdatas_v1_{new_part}/{req_data.get_book_id()}", base_path)
        elif f"./prepub/{req_data.get_book_id()}" in page_path:
            modified_path = page_path.replace(f"./prepub/{req_data.get_book_id()}", "", 1)
            first_slash_index = modified_path.find("/")
            if first_slash_index != -1:
                page_path = base_path + modified_path[first_slash_index + 1:]
        elif f"./daily/{req_data.get_book_id()}" in page_path:
            modified_path = page_path.replace(f"./daily/{req_data.get_book_id()}", "", 1)
            first_slash_index = modified_path.find("/")
            if first_slash_index != -1:
                page_path = base_path + modified_path[first_slash_index + 1:]

        if not req_data.is_product() or not os.path.exists(page_path):
            n_page_path = f'{Constants.MNT_PATH}/{page_path}'
            if os.path.exists(n_page_path):
                page_path = n_page_path

        page_path = page_path.replace('//', '/')
        if before_path != page_path:
            log.warn(f"页面路径从：{before_path} 修正为：{page_path}")
        return page_path
