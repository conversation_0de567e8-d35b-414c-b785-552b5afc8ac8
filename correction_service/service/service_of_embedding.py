# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/8 11:39 
# @Description  : service_of_embedding.py
import cv2
import numpy

from base_common.service.service_of_base import BaseService
from base_common import Context, ProcessError as PE, LoggerFactory, RedisRequestor, MissionMode

log = LoggerFactory.get_logger('EmbeddingService')
class EmbeddingService(BaseService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.EMBEDDING_MISSION2
        self.init_redis()

    def do_feature_detect(self, mission_id, img_user, dim=512):
        src_img = cv2.resize(img_user, (dim, dim), interpolation=cv2.INTER_AREA)
        img_key, img_type = self.set_image(src_img)
        data_json = {'imgs': [{'img_key': img_key, 'img_type': img_type}]}
        model_response = RedisRequestor.ai_embedding2(data_json, Context.get_record_id(mission_id))
        if model_response.is_success():            
            model_response.set_response(numpy.array(model_response.get_json_response()))
        else:
            model_response.set_response([])
            Context.report_error(mission_id, PE.FEATURE_DETECT)
        embeding = numpy.reshape(model_response.get_response(), (-1, 1))
        return embeding