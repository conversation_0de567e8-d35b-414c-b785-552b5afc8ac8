# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/8 10:47 
# @Description  : service_of_search_page.py
import json
import os
import re
from base_common.auto_setting import AutoSetting
from base_common.mission_mode import MissionAlisa

os.environ["CUDA_VISIBLE_DEVICES"] = "-1"
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
import time
import yaml
import numpy as np
t_s = time.time()
print(f"\033[32mTensorflow正在初始化...\033[0m")
import tensorflow as tf
t_t = time.time() - t_s
print(f"\033[32mTensorflow已初始化，耗时：{t_t:.4f} sec\033[0m")

from PIL import Image
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from .service_of_ocr import OcrService
from .service_of_embedding import EmbeddingService

from correction_service.data import CorrectRequest
from correction_service.data import SearchPageRequest
from correction_service.data import SearchPageResponse

from base_common.util.util_of_detection_cpu import ImageProcessorCpu
from base_common.service.service_of_direction import DirectionService
from base_common.service.service_of_detection import DetectionService
from base_common import Context, GPUUtil, FileUtil, LoggerFactory, Constants, BoxUtil

from text2vec.utils.tokenizer import Tokenizer

GPUUtil.set_memory_growth(tf, True)
log = LoggerFactory.get_logger('SearchPageService')

def word2index_with_pad(texts, word_dict, length):
    wv = [word_dict[wd] + 1 for wd in texts]
    wv_ = wv + [0 for _ in range(length - len(wv))]
    return wv_

class SearchPageService:
    last_modified_time = None
    book_data = None
    
    def __init__(self):
        self._detection_ser = None
        self._embedding_ser = None
        self._ocr_ser = None
        self._direction_ser = None
        self.exculde_threshold = 0.3
        self.total_score_threshold = 0.45
        self.tokenizer = Tokenizer()

    def autowired(self, detection_ser: DetectionService, embedding_ser: EmbeddingService,
                  ocr_ser: OcrService, direction_ser: DirectionService):
        self._detection_ser = detection_ser
        self._embedding_ser = embedding_ser
        self._ocr_ser = ocr_ser
        self._direction_ser = direction_ser

    # 检查是否有两个特征的 top1 key 相同
    def find_common_top1_key(self, key1, key2, key3):
        if key1 == key2:
            return key1
        if key1 == key3:
            return key1
        return None

    # 判断ocr结果非数字占比
    def print_if_non_chinese_percentage_high(self, string):
        # 统计非中文字符和非英文字符的数量
        non_chinese_count = sum(
            1 for c in string if not re.match(r'[\u4e00-\u9fff]', c) and not re.match(r'[a-zA-Z]', c))

        # 计算字符串总长度
        total_length = len(string)

        # 计算非中文字符占比
        non_chinese_percentage = (non_chinese_count / total_length) * 100 if total_length > 0 else 0

        return non_chinese_percentage

    def filter_boxes(self, box_list):

        # 计算长宽比
        def aspect_ratio(box):
            x1, y1 = box[0]
            x2, y2 = box[1]
            x3, y3 = box[2]
            x4, y4 = box[3]
            min_x = min([x1, x2, x3, x4])
            max_x = max([x1, x2, x3, x4])
            min_y = min([y1, y2, y3, y4])
            max_y = max([y1, y2, y3, y4])
            width = max_x - min_x
            height = max_y - min_y
            # 返回长宽比
            return height / width if height > width else width / height

        # 计算长宽比，并与框一起排序
        box_with_ratio = [(i, boxs['box'], aspect_ratio(boxs['box'])) for i, boxs in enumerate(box_list)]

        # 排序，按长宽比排序，从大到小
        box_with_ratio.sort(key=lambda x: x[2], reverse=True)

        # 选择前30个框
        top_20_boxes_with_index = box_with_ratio[:30]

        # 保持原顺序
        top_20_boxes_with_index.sort(key=lambda x: x[0])

        # 提取前20个框的数据
        top_20_boxes = [box for _, box, _ in top_20_boxes_with_index]

        # 过滤框，并收集框中的文字
        box_filter_list = []
        for boxs in box_list:
            box = boxs['box']
            if box in top_20_boxes:  # 判断框是否在前20名之中
                box_filter_list.extend(self.tokenizer.tokenize(boxs['text']))

        return box_filter_list

    def update_exculde_threshold(self):
        try:
            config = AutoSetting.get_config()
            self.exculde_threshold = config['exculde_threshold']
            self.total_score_threshold = config['total_score_threshold']
        except:
            self.exculde_threshold = 0.3
            self.total_score_threshold = 0.45

    def do_search(self, req_data: CorrectRequest, sch_pg_req: SearchPageRequest) -> SearchPageResponse:
        """
        请求搜索页面数据。根据提供的请求数据，向API发起请求以获取搜索页面的相关信息。
        参数:
        :param req_data: 当前任务请求体。
        :param sch_pg_req: 包含搜索请求所需信息的数据对象。
            'bookId':书籍id，
            'imageUrl':图像url，
            'coord':用户设置的文档坐标，现在一般是[0,0,1,1],
            'isOriginImg': 是否是原图批改，原图批改则不执行抠图操作，
            'justPage':是否是整页批改，一般是，
            'direction'：是否执行文档方向识别操作，默认是
            'data_book': 本地书籍信息
        返回:
        :return: 返回搜索页面数据的对象@ApiSearchPageData，如果请求失败，则包含错误信息。
        """
        if not Context.is_product():
            log.info(f"(mission_id: {req_data.get_mission_id()})搜页流程启动, 请求参数：{sch_pg_req.to_json()}")
        t_start = time.time()
        sch_page = SearchPageResponse.error()
        coord = sch_pg_req.get_coord()
        record_id = sch_pg_req.get_record_id()
        mission_id = sch_pg_req.get_mission_id()
        base_path = sch_pg_req.get_base_path()
        data_book = sch_pg_req.get_book_json()
        if not coord or max(coord) > 1:
            # 如果请求出错，返回错误的搜索页面数据对象
            log.error('coord has some problem')
            searched_info = {'error_info': 'coord has some problem'}
            if not Context.is_product():
                log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
            return sch_page

        img_user = sch_pg_req.get_user_image()
        tmp_img = Image.fromarray(img_user)
        w, h = tmp_img.size
        size_all = [w, h]
        coord_raw = [int(np.prod(p)) for p in zip([w, h] * 2, coord)]
        img_key = None
        if sch_pg_req.is_origin_image() is None or sch_pg_req.is_origin_image() is False:
            t0 = time.time()
            img_key, img_type, img_user = self._detection_ser.do_detection(mission_id, img_user)
            Context.report_cost(mission_id, (MissionAlisa.DETECTION_SERVICE, time.time() - t0))
            if img_user is None:
                log.warn(f'(mission_id: {mission_id}) user image not valid {sch_pg_req.get_image_url()} judge by detection model.')
                searched_info = {'pageId': -1}
                if not Context.is_product():
                    log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
                return sch_page.set_success(True).set_response(searched_info)

            ratio = ImageProcessorCpu.is_pure_black_or_white(img_user)
            if ratio >= 0.62:
                log.warn(f'(mission_id: {mission_id}) user image not valid {sch_pg_req.get_image_url()} judge by is_pure_black_or_white.')
                searched_info = {'pageId': -1}
                if not Context.is_product():
                    log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
                return sch_page.set_success(True).set_response(searched_info)
            height, width, _ = img_user.shape
            # 计算图像的宽高比
            aspect_ratio = min(height, width) / max(height, width)
            # 初始化标志为False
            has_cut_type_six = False
            # 读取cutType
            for page in data_book['bookItems']:
                # 如果cutType为6，则将标志设置为True, 跳出循环
                # 单页单栏(两边线) 1; 双页单栏(四边线) 2; 单页双栏(旧)(2020-6 禁用)(三边线) 3; 双页双栏(未使用) 4;
                # 双页三栏(旧)(2020-6 禁用)(四边线) 5; 双页三栏(新)(六边线) 6; 单页双栏(新)(四边线) 7;
                if page['cutType'] == 6:
                    has_cut_type_six = True
                    break

            # 根据标志设置threshold_ratio的值
            threshold_ratio = 0.25 if has_cut_type_six else 0.45
            # 根据宽高比拦截图像
            if aspect_ratio <= threshold_ratio:
                log.warn(f'(mission_id: {mission_id}) Width-to-height {aspect_ratio} ratio is too low {threshold_ratio}, {sch_pg_req.get_image_url()}')
                searched_info = {'pageId': -1}
                if not Context.is_product():
                    log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
                return sch_page.set_success(True).set_response(searched_info)

        #if sch_pg_req.is_direction() is None or sch_pg_req.is_direction() is True:
        # 调用文档方向分类模型进行方向预测
        t0 = time.time()
        img_key, img_type, img_user, angle = self._direction_ser.do_direction(mission_id, img_user, img_key, img_type)
        if img_user is None:
            log.warn(f'(mission_id: {mission_id}) user image not valid {sch_pg_req.get_image_url()} judge by direction model.')
            searched_info = {'pageId': -1}
            if not Context.is_product():
                log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
            return sch_page.set_success(True).set_response(searched_info)
        if not Context.is_product():
            log.info(f"(mission_id: {mission_id}) 方向预测模型调用结束 度数：{angle}")
        Context.report_cost(mission_id, (MissionAlisa.DIRECTION_SERVICE, time.time() - t0))

        if angle == 1:
            size_all = [h, w]
            coord_raw = [coord_raw[1], w - coord_raw[2], coord_raw[3], w - coord_raw[0]]
        elif angle == 2:
            size_all = [h, w]
            coord_raw = [h - coord_raw[3], coord_raw[0], h - coord_raw[1], coord_raw[2]]
        elif angle == 3:
            coord_raw = [w - coord_raw[2], h - coord_raw[3], w - coord_raw[0], h - coord_raw[1]]

        book_id = sch_pg_req.get_book_id()
        image_url = sch_pg_req.get_image_url()
        if not hasattr(img_user, 'shape'):
            img_user = np.array(img_user)

        h, w, _ = img_user.shape
        if "jsons" not in os.listdir(base_path):
            log.error(f"(mission_id: {mission_id}) book not exists in {base_path}")
            searched_info = {'image': image_url, 'message': 'this book not exists'}
            if not Context.is_product():
                log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
            return sch_page.set_success(True).set_response(searched_info)

        data_features = np.load(os.path.join(base_path, 'features', f'{book_id}.npz'))
        features = data_features['features']
        booknames = data_features['names']
        t0 = time.time()
        embeding = self._embedding_ser.do_feature_detect(mission_id, img_user, dim=512)
        Context.report_cost(mission_id, (MissionAlisa.EMBEDDING2_SERVICE, time.time() - t0))
        scores_f = features.dot(embeding)
        scores_ft = {booknames[i]: scores_f[i][0] for i in range(len(booknames))}

        # 新增图像阈值功能
        scores_ft_sorted = sorted(scores_ft.items(), key=lambda x: x[1], reverse=True)[:2]
        scores_ft_rank1 = round(scores_ft_sorted[0][1], 2)
        scores_ft_rank2 = round(scores_ft_sorted[1][1], 2)
        if not Context.is_product():
            log.info(f'(mission_id: {mission_id}) 图像特征分数: {scores_ft_sorted[:10]}')
        # img_user = Image.fromarray(img_user)
        self.update_exculde_threshold()
        if scores_ft_rank1 < self.exculde_threshold:
            # 其他书或模糊或拍一半 或拍得有问题
            searched_info = {'pageId': -2}
            if not Context.is_product():
                log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
            return sch_page.set_success(True).set_response(searched_info)

        if scores_ft_rank1 - scores_ft_rank2 > 0.35:
            serchcol_id = scores_ft_sorted[0][0]
            if not Context.is_product():
                log.info(f'(mission_id: {mission_id}) 书籍编号：{book_id} 页码编号：{serchcol_id}')
                log.info(f'(mission_id: {mission_id}) rank_diff-{scores_ft_rank1 - scores_ft_rank2}')
            if sch_pg_req.is_just_page():
                #img_crop = ImageUtil.numpy_2_base64(img_user)
                if not Context.is_product():
                    searched_info = {"recordId": record_id, "bookId": book_id,
                                     "pageId": serchcol_id, "justedImg": None}
                    log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
                Context.report_cost(mission_id, ("搜页", time.time() - t_start))
                return sch_page.set_success(True).set_response({'pageId': serchcol_id, 'img_key': img_key, 'img_type': img_type,
                        'image': img_user, 'coord': coord_raw, 'size_all': size_all, 'message': 0})
        else:
            # pagetexts_ = {str(page['pageId']): page['pageText'] for page in data_book['bookItems']}
            for page in data_book['bookItems']:
                #     获取当前页的 boxText 内容
                box_list = page['boxText']
                # 过滤框
                page['filteredText'] = self.filter_boxes(box_list)

            pagetexts_ = {str(page['pageId']): page['filteredText'] for page in data_book['bookItems']}

            colinfos = []
            # 排除切割类型为1，3，7的分栏数据
            for page in data_book['bookItems']:
                if page['cutType'] not in [1, 3, 7]:
                    colinfos += page['pageColums']['colInfo']

            seg_text = self._ocr_ser.exec_predict_ocr(req_data, img_user, img_key, img_type)
            if seg_text is None:
                searched_info = {'pageId': -1}
                if not Context.is_product():
                    log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
                return sch_page.set_success(True).set_response(searched_info)
            t0 = time.time()
            #log.info(f"(seg_text: {seg_text})")
            percent = self.print_if_non_chinese_percentage_high(''.join(seg_text))
            #log.info(f"(percent: {percent})")

            # save json for test
            found, thresholds, weights_score = self._get_book_info(book_id, [0.8, 0.9, 0.2])
            if not found:
                if percent <= 70:
                    weights_score = [0.8, 0.9, 0.2]
                else:
                    weights_score = [0.2, 0.0, 0.1]
            if not Context.is_product():
                log.info(f"(mission_id: {mission_id}) thresholds as {thresholds}")
                log.info(f"(mission_id: {mission_id}) weights score as {weights_score}")

            # 计算当前页文本与该书录入数据文本的tfidf值及编辑距离分数；
            # tfidf值更注重词频，编辑距离更注重文本顺序。
            coltexts_ = {cols['colId']: cols['coloumText'] for cols in colinfos}

            totaltexts_ori = dict(pagetexts_, **coltexts_)
            # todo 初始化一个新的字典
            totaltexts_ = {}
            # 遍历原始字典
            for key, values in totaltexts_ori.items():
                # 初始化一个空列表用于存储分析结果
                analyzed_values = []
                # 遍历当前键的值（列表）
                for value in values:
                    # 将处理后的结果添加到新列表中
                    analyzed_values.extend(BoxUtil.custom_analyzer(value))
                # 将结果存入新字典
                totaltexts_[key] = analyzed_values
            texts_join = [' '.join(seg_text)] + [' '.join(v) for _, v in totaltexts_.items()]
            vectorizer = TfidfVectorizer(analyzer=BoxUtil.custom_analyzer)

            docs_tfidf = vectorizer.fit_transform(texts_join)
            word_dict = vectorizer.vocabulary_
            cosine_similarities = cosine_similarity(docs_tfidf[0], docs_tfidf[1:])[0]
            scores_tfidf = {k: v for k, v in zip(list(totaltexts_.keys()), cosine_similarities.tolist())}
            cosine_similarities_bool = cosine_similarities > 0.2
            selected = np.array(list(totaltexts_.keys()))[cosine_similarities_bool].tolist()
            score_selected = {}
            if selected:
                length_max = max(
                    [len(seg_text)] + [len(v) for k, v in totaltexts_.items() if k in selected])

                wv_quare = word2index_with_pad(seg_text, word_dict, length_max)
                total_vecs = {k: word2index_with_pad(v, word_dict, length_max) for k, v in
                              totaltexts_.items() if k in selected}
                vecs_ts = tf.constant(list(total_vecs.values()))
                quare_ts = tf.tile([wv_quare], [len(total_vecs), 1])
                sp_vecs_ts = tf.sparse.from_dense(vecs_ts)
                sp_quare_ts = tf.sparse.from_dense(quare_ts)
                edit_dis = (1 - tf.edit_distance(sp_vecs_ts, sp_quare_ts,
                                                 normalize=False) / length_max).numpy().tolist()
                score_selected = {k: v for k, v in zip(list(total_vecs.keys()), edit_dis)}

            score_noselected = {k: 0 for k, _ in totaltexts_.items() if k not in selected}
            scores_edit = dict(score_noselected, **score_selected)
            scores = {k: (weights_score[0] * scores_ft[k] + weights_score[1] * scores_tfidf[k] +
                          weights_score[2] * scores_edit[k]) / sum(weights_score) for k in booknames if
                      k in totaltexts_.keys()}
            scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            scores = [elem for elem in scores if elem[1] >= thresholds]
            if not Context.is_product():
                log.info(f'(mission_id: {mission_id}) tf-idf score: {sorted(scores_tfidf.items(), key=lambda x: x[1], reverse=True)[:10]}')
                log.info(f'(mission_id: {mission_id}) edit_dis score: {sorted(scores_edit.items(), key=lambda x: x[1], reverse=True)[:10]}')
                log.info(f'(mission_id: {mission_id}) 综合分数: {scores[:10]}')
                log.info(f'(mission_id: {mission_id}) 阈值: {thresholds}')
            if thresholds == self.total_score_threshold:
                serch_id = self.find_common_top1_key(scores_ft_sorted[0][0],
                                                       sorted(scores_tfidf.items(), key=lambda x: x[1], reverse=True)[:10][0][0],
                                                       sorted(scores_edit.items(), key=lambda x: x[1], reverse=True)[:10][0][0])
    
                if serch_id is not None and sch_pg_req.is_just_page():
                    #img_crop = ImageUtil.numpy_2_base64(img_user)
                    Context.report_cost(mission_id, ("计算综合得分", time.time() - t0))
                    Context.report_cost(mission_id, ("搜页", time.time() - t_start))
                    return sch_page.set_success(True).set_response({'pageId': serch_id, 'image': img_user, 'img_key': img_key,'img_type': img_type,
                                                                'coord': coord_raw, 'size_all': size_all, 'message': 0})

            if len(scores) > 0:
                serchcol_id = scores[0][0]
                if not Context.is_product():
                    log.info(f'(mission_id: {mission_id}) 书籍编号：{book_id} 页码编号：{serchcol_id}')
                if sch_pg_req.is_just_page():
                    #img_crop = ImageUtil.numpy_2_base64(img_user)
                    if not Context.is_product():
                        searched_info = {"recordId": record_id, "bookId": book_id,
                                         "pageId": serchcol_id, "justedImg": None}
                        log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
                    Context.report_cost(mission_id, ("搜页", time.time() - t_start))
                    return sch_page.set_success(True).set_response({'pageId': serchcol_id, 'image': img_user, 'img_key': img_key, 'img_type': img_type,
                                                             'coord': coord_raw, 'size_all': size_all, 'message': 0})
            else:
                log.warn(f'(mission_id: {mission_id}) image: {image_url}, recordId:{record_id}, message: Matching page not found')
                t_end = time.time()
                Context.report_cost(mission_id, ("搜页", t_end - t_start))
                searched_info = {'image': image_url, 'message': 'Matching page not found'}
                if not Context.is_product():
                    log.info(f"(mission_id: {mission_id}) 搜页流程结束，响应结果：{json.dumps(searched_info)}")
                return sch_page.set_success(True).set_response(searched_info)
				
    def _get_book_info(self, book_id, default_weight_score):
        _book_data = SearchPageService.get_yaml_config()
        if not _book_data:
            return False, self.total_score_threshold, default_weight_score

        for item in _book_data:
            if item['bookid'] == book_id:
                return (True, item.get('thresholds', self.total_score_threshold),
                        item.get('default_weight_score', default_weight_score))
        return False, self.total_score_threshold, default_weight_score
        
    @classmethod
    def get_yaml_config(cls):
        modified_time = os.path.getmtime(Constants.SEARCH_WEIGHT_YAML)
        if cls.last_modified_time != modified_time:
            log.info(f'yaml文件更新了...')
            cls.last_modified_time = modified_time
            content = FileUtil.read_file(Constants.SEARCH_WEIGHT_YAML, 'r')
            try:
                data = yaml.safe_load(content)
                cls.book_data = data
            except yaml.YAMLError:
                return None
        return cls.book_data