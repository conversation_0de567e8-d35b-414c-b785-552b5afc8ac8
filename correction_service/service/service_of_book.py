# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/5 14:30
import os
import json
import time
import traceback
from .service_of_answer import AnswerService
from .service_of_page import PageService
from .service_of_search_page import SearchPageService
from correction_service.common import CorrectionContext
from correction_service.data import BookResponse, CorrectRequest, SearchPageRequest

from base_common import Context, FileUtil, Constants, LoggerFactory, OkHttpUtil, \
    CorrectionError as CE, ProcessError as PE, DictUtil, ErrorCode as EC
log = LoggerFactory.get_logger('BookService')

class BookService:
    def __init__(self):
        self._page_ser = None
        self._answer_ser = None
        self._search_page_ser = None

    def autowired(self, page_ser: PageService, answer_ser: AnswerService, search_page_ser: SearchPageService):
        self._page_ser = page_ser
        self._answer_ser = answer_ser
        self._search_page_ser = search_page_ser

    def do_search(self, req_data: CorrectRequest) -> BookResponse:
        """
        处理书籍信息，包括:
            1.找书，从请求体中获取书籍的相关信息，如下载或本地获取； 返回书籍所在的目录
            2.搜页，通过用户给定图像在书籍中搜索具体页信息
            3.加载书籍信息， 如搜索成功则加载对应的数据
        参数: --
        :param req_data: 校正请求数据对象，包含请求的具体参数。
        返回: --
        :return: 返回一个BookResponse对象，包含校正结果或错误信息。
        """
        # 找书
        t0 = time.time()
        # 获取书籍的基础路径
        base_path = self._get_book_path(req_data)
        # 如果没有找到书籍路径，则返回错误响应
        if not base_path or not os.path.exists(base_path):
            Context.report_error(req_data.get_mission_id(), PE.FIND_BOOK)
            return None, BookResponse(st=t0, message=CE.ACCESS_BOOK_NOT_FOUND, error_code=EC.BOOK_NOT_FOUND_ERROR, base_path=base_path)

        features_path = os.path.join(base_path, "features")
        jsons_path = os.path.join(base_path, "jsons")
        xfeatures2d_path = os.path.join(base_path, "xfeatures2d")
        if not os.path.exists(features_path) or not os.path.exists(jsons_path) or not os.path.exists(xfeatures2d_path):
            if Context.is_product():
                OkHttpUtil.notify_feishu(f"书籍数据异常，请检查书籍数据是否完整，路径为：{base_path}")
            log.error(f"(mission_id: {req_data.get_mission_id()}) 书籍数据异常，请检查书籍数据是否完整，路径为：{base_path}")
            Context.report_error(req_data.get_mission_id(), PE.FIND_BOOK)
            return None, BookResponse(st=t0, message=CE.ACCESS_BOOK_NOT_FOUND, error_code=EC.BOOK_NOT_FOUND_ERROR,
                                base_path=base_path)

        base_path = base_path.replace("//", "/")
        # 加载书籍的 JSON 数据，注意:这一步是从搜页业务中抽取的，因此搜页业务中无需再次加载书籍数据
        content = FileUtil.read_file(os.path.join(base_path, f"jsons/{req_data.get_book_id()}.json"))
        book_json = json.loads(content)
        # 找书结束

        # 搜页
        t0 = time.time()
        try:
            search_page_req = SearchPageRequest.fromReq(req_data)
            search_page_req.set_base_path(base_path)
            search_page_req.set_user_image(req_data.get_user_image())
            search_page_req.set_book_json(book_json)
            search_page_resp = self._search_page_ser.do_search(req_data, search_page_req)
        except:
            log.error(f"(mission_id: {req_data.get_mission_id()}) Oops!!! search page exception, stack as {traceback.format_exc()}")
            Context.report_error(req_data.get_mission_id(), PE.SEARCH_PAGE)
            return book_json, BookResponse(st=t0, message=CE.ACCESS_SEARCH_PAGE_API_FAILED,
                                error_code=EC.SEARCH_PAGE_ERROR, base_path=base_path)

        # 如果搜索页面响应不成功，则报告错误并返回错误响应
        if not search_page_resp.is_success():
            Context.report_error(req_data.get_mission_id(), PE.SEARCH_PAGE)
            return book_json, BookResponse(st=t0, message=CE.ACCESS_SEARCH_PAGE_API_FAILED, error_code=EC.SEARCH_PAGE_ERROR,
                                base_path=base_path, search_data=search_page_resp)
        # 从搜索页面响应中获取响应数据
        resp = search_page_resp.get_response()
        search_page = resp.get('pageId', None)
        # 如果没有搜索页面ID，则报告错误并返回错误响应
        if not search_page:
            Context.report_error(req_data.get_mission_id(), PE.SEARCH_PAGE)
            log.error(f"(mission_id: {req_data.get_mission_id()}) message: {str(CE.SEARCH_PAGE_FAILED)}, response as {search_page}")
            return book_json, BookResponse(st=t0, message=CE.SEARCH_PAGE_FAILED, error_code=EC.SEARCH_PAGE_ERROR,
                                base_path=base_path, search_data=search_page_resp)
        # 搜页结束
        # 检查搜索页面ID的有效性
        if search_page == -1:
            Context.report_error(req_data.get_mission_id(), PE.SEARCH_PAGE)
            log.error(f"(mission_id: {req_data.get_mission_id()}) message: {CE.INVALID_USER_IMAGE}, url as {req_data.get_image_url()}")
            return book_json, BookResponse(st=t0, message=CE.INVALID_USER_IMAGE, error_code=EC.USER_IMAGE_ERROR,
                                base_path=base_path, search_data=search_page_resp)
        elif search_page == -2:
            Context.report_error(req_data.get_mission_id(), PE.SEARCH_PAGE)
            if req_data.get_biz_type() == 3:
                return book_json, BookResponse(st=t0, message=CE.ACCESS_SEARCH_PAGE_API_FAILED, error_code=EC.SEARCH_PAGE_ERROR,
                                    base_path=base_path, search_data=search_page_resp)
            else:
                return book_json, BookResponse(st=t0, message=CE.SEARCH_PAGE_FAILED, error_code=EC.SEARCH_USER_IMAGE_ERROR,
                                    base_path=base_path, search_data=search_page_resp)
        return book_json, BookResponse(st=t0, success=True, base_path=base_path, search_data=search_page_resp)

    def generate_book_data(self, req_data: CorrectRequest) -> BookResponse:
        """
        处理书籍信息，包括:
            1.找书，从请求体中获取书籍的相关信息，如下载或本地获取； 返回书籍所在的目录
            2.搜页，通过用户给定图像在书籍中搜索具体页信息
            3.加载书籍信息， 如搜索成功则加载对应的数据
        参数: --
        :param req_data: 校正请求数据对象，包含请求的具体参数。
        返回: --
        :return: 返回一个BookResponse对象，包含校正结果或错误信息。
        """
        # 找书
        t0 = time.time()
        book_json, book_resp = self.do_search(req_data)
        if not book_resp.is_success():
            return book_resp
        base_path = book_resp.get_base_path()
        search_page_resp = book_resp.get_search_data()
        # 加载书籍信息
        page_data = self._page_ser.correct_page_data(req_data, search_page_resp, book_json)
        if not page_data.is_success():
            Context.report_error(req_data.get_mission_id(), PE.LOAD_BOOK)
            return BookResponse(st=t0, message=CE.LOAD_COLUMN_INFO_FAILED, error_code=EC.CORRECTION_ERROR,
                                base_path=base_path, search_data=search_page_resp, page_data=page_data)
        # 如果所有操作都成功，则返回成功的书籍响应
        return BookResponse(st=t0, success=True, base_path=base_path, search_data=search_page_resp, page_data=page_data)

    def fix_item_id(self, mission_id, item_info, page_id, item_id):
        # fix： 智能口算不能返回正确的itemId，
        # 导致H5页面无法查看本题的全部答案。 by yanhui 2024-6-6
        correction_data = CorrectionContext.get_correct_data(mission_id)
        if item_info[0]['answerInfo'] is not None:
            fix_item_info = [item_info[0]]
            fix_item_info[0]['itemId'] = int(item_id)
            fix_answer_info = []
            for answer_item in item_info[0]['answerInfo']:
                fix_answer_info.append({
                    'blockName': answer_item['blockName'].replace(page_id, item_id),
                    'answer': answer_item['answer'],
                    'blockType': answer_item['blockType']
                })
            correction_data.set_item_info(fix_item_info)
        stem_ks = DictUtil.replace_dict_key(correction_data.get_stem_ks(), page_id, item_id)
        reg_coords = DictUtil.replace_dict_key(correction_data.get_reg_coords(), page_id, item_id)
        reg_class = DictUtil.replace_dict_key(correction_data.get_reg_class(), page_id, item_id)
        ref_coords = DictUtil.replace_dict_key(correction_data.get_ref_coords(), page_id, item_id)
        answer_ks = DictUtil.replace_dict_key(correction_data.get_answer_ks(), page_id, item_id)
        rec_ks = DictUtil.replace_dict_key(correction_data.get_rec_ks(), page_id, item_id)

        correction_data.set_stem_ks(stem_ks)
        correction_data.set_reg_coords(reg_coords)
        correction_data.set_reg_class(reg_class)
        correction_data.set_ref_coords(ref_coords)
        correction_data.set_answer_ks(answer_ks)
        correction_data.set_rec_ks(rec_ks)

    def _get_book_path(self, req_data: CorrectRequest) -> str:
        _path = req_data.get_path()
        _encrypt_path = req_data.get_encrypt_path()
        if _path is None and _encrypt_path is None:
            log.warn("path and encryptPath is None we check book in dick.")
            return None

        is_encrypt = _path is None
        base_path_ = self._get_path(_encrypt_path if is_encrypt else _path, is_encrypt)
        if base_path_ is None:
            log.warn("encryptPath not in dick.")
            return None

        if not base_path_.startswith("https://"):
            return base_path_ + "/"
        return None

    def _get_path(self, json_obj, is_encrypt=False):
        if isinstance(json_obj, str):
            json_obj = json.loads(json_obj)
        nas_path = json_obj.get('nasPath', None)
        if is_encrypt:
            nas_path = self._decrypt_path(nas_path)

        if nas_path and os.path.exists(nas_path):
            return nas_path
        return None

    def _decrypt_path(self, encrypt_path):
        if encrypt_path is None:
            return None
        decoded = []
        for char in encrypt_path:
            single_decode = Constants.DECODE_TABLE[char] if char in Constants.DECODE_TABLE else char
            decoded.append(single_decode)
        return ''.join(decoded)
