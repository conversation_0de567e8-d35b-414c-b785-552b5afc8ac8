# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/5 16:23
import copyreg
import cv2
import time

from base_common.service.service_of_base import BaseService
from correction_service.data import PageData, AnswerItemData
from correction_service.data import CorrectRequest
from correction_service.data import BookResponse, AnswerResponse
from correction_service.util import TopicUtil

from base_common import Context, TopicType as TT, TopicMode as TM, ErrorCode as EC, \
    CorrectionError as CE, ProcessError as PE, LoggerFactory, BoxUtil
from correction_service.service.service_of_surf import SurfService

log = LoggerFactory.get_logger('AnswerService')
def _pickle_keypoint(keypoint):  # : cv2.KeyPoint
    return cv2.KeyPoint, (
        keypoint.pt[0],
        keypoint.pt[1],
        keypoint.size,
        keypoint.angle,
        keypoint.response,
        keypoint.octave,
        keypoint.class_id,
    )

copyreg.pickle(cv2.KeyPoint().__class__, _pickle_keypoint)
class AnswerService(BaseService):
    def __init__(self):
        super().__init__()
        self.surf_service = SurfService()
       
    def do_collect(self, req_data: CorrectRequest, book_data: BookResponse) -> AnswerResponse:
        """
        收集答案
        :param req_data: 请求体
        :param book_data: 书籍信息
        :return: 答案信息
        """
        page_data = book_data.get_page_data()
        # 是否全为智能口算
        item_info = page_data.get_item_info()
        ai_oral_calc_count = 0
        for item in item_info:
            if item.get('itemType', None) == TT.INTELLIGENT_ORAL_CALC:
                ai_oral_calc_count += 1
        if not Context.is_product():
            log.info(f"(mission_id: {req_data.get_mission_id()}) 智能口算题目数量：{ai_oral_calc_count}")
        all_ai_oral_calc = ai_oral_calc_count == 1 and ai_oral_calc_count == len(item_info)
        
        t0 = time.time()
        book_json = page_data.get_book_json()
        w = None
        h = None
        pdf_path = None
        try:
            for page in book_json['bookItems']:
                if str(page['pageId']) == page_data.get_page_id():
                    w = page.get('w', None)
                    h = page.get('h', None)
                    if "localPagePath" in page:
                        pdf_path = page['localPagePath']
                        img = cv2.imread(pdf_path)
                    if w is None or h is None:
                        h, w, _ = img.shape
                    break
        except:
            pass
        if w is None or h is None:
            Context.report_error(req_data.get_mission_id(), PE.ALIGN_IAMGE)
            log.error(f"(mission_id: {req_data.get_mission_id()}) json中没有找到当前页的宽高， bookId: {book_json['bookId']} pageId: {page_data.get_page_id()}")
            return AnswerResponse(st=t0, message=CE.INVALID_ALIGN_PAGE, error_code=EC.ALIGN_PAGE_ERROR)
        aligned_page = self.surf_service.do_surf(book_data, req_data, pdf_path, w, h)

        if not aligned_page:
            Context.report_error(req_data.get_mission_id(), PE.ALIGN_IAMGE)
            log.error(f"(mission_id: {req_data.get_mission_id()}) message: {str(CE.INVALID_ALIGN_PAGE)}, "
                      f"pageId: {page_data.get_page_id()}, "
                      f"column: {page_data.get_col_index()}, "
                      f"pageUrl: {page_data.get_page_url()}")
            return AnswerResponse(st=t0, message=CE.INVALID_ALIGN_PAGE, error_code=EC.ALIGN_PAGE_ERROR)
        img_align, crop_coord, surf_mat, img_key, img_type = aligned_page

        try:
            align_info = [img_align.shape]
        except:
            pass
        if img_align is None:
            Context.report_error(req_data.get_mission_id(), PE.ALIGN_IAMGE)
            log.error(f"(mission_id: {req_data.get_mission_id()}) message: {str(CE.INVALID_ALIGN_PAGE)}, "
                      f"pageId: {page_data.get_page_id()}, "
                      f"column: {page_data.get_col_index()}, "
                      f"pageUrl: {page_data.get_page_url()}")
            return AnswerResponse(st=t0, message=CE.INVALID_ALIGN_PAGE, error_code=EC.ALIGN_PAGE_ERROR)
        resp = self._do_collect(book_data, img_align, crop_coord, all_ai_oral_calc, img_key, img_type)
        resp.set_align_info(align_info)
        resp.set_surf_mat(surf_mat)
        return resp

    def _do_collect(self, book_data: BookResponse, img_align, crop_coord, all_ai_oral_calc, img_key, img_type) -> AnswerResponse:
        #im_reg = cv2.cvtColor(img_align, cv2.COLOR_BGR2RGB)
        #img_back = im_reg.copy()
        im_reg = img_align.copy()
        h, w, __ignored = im_reg.shape
        answer_item = AnswerItemData(w=w, h=h, img_align=img_align, im_reg=im_reg, img_key=img_key, img_type=img_type)
        page_data = book_data.get_page_data()
        search_data = book_data.get_search_data()
        resp = search_data.get_response()
        search_page = resp.get('pageId')

        # 按题目进行题目信息获取，每题里面是按答题区域也就是region进行记录
        #if not all_ai_oral_calc:
        self._collect_item_data(answer_item, page_data, crop_coord, search_page, all_ai_oral_calc)

        answer_item.set_crop_coord(crop_coord)
        resp_data = AnswerResponse.ok()
        resp_data.set_answer_item(answer_item)
        resp_data.set_all_ai_oral_calc(all_ai_oral_calc)
        return resp_data

    def _collect_item_data(self, answer_item: AnswerItemData, page_data: PageData, crop_coord, search_page, all_ai_oral_calc):
        item_info = page_data.get_item_info()
        for item_idx, item in enumerate(item_info):
            item_type = item.get('itemType', None)
            item_answers = item.get('answerInfo', [])
            item_id = item['itemId']
            item_id_str = str(item_id)
            item_coord = item['coord']

            item_w = item_coord[2] - item_coord[0]
            item_h = item_coord[3] - item_coord[1]
            ref = self._calc_ref(page_data, search_page, crop_coord, item_coord)
            if all_ai_oral_calc:
                ref = [0, 0, answer_item.get_w() - 1, answer_item.get_h() - 1]
            answer_item.get_ref_coords()[item_id_str] = ref
            answer_item.get_items_type()[item_id_str] = item_type

            # 9 连线题， 21 作图题
            if item_type in [TT.CONNECT_LINE, TT.DRAW_IMG, TT.OFF_COMPUTE]:
                answer_item.get_items_answer()[item_id_str] = item_answers

            # 按region进行答题区域坐标，名称等信息收集
            if item_type != TT.INTELLIGENT_ORAL_CALC:
                self._collect_item_info_not_ai(answer_item, item_id, item_type, item_answers, ref, item_w, item_h)

    def _calc_ref(self, page_data, search_page, crop_coord, item_coord):
        """
        根据给定的页面数据、搜索页、裁剪坐标和项目坐标来计算参考值。
        :param page_data: 页面数据对象，具有get_crop_x_min和get_crop_y_min方法
        :param search_page: 搜索页，用于某种条件判断
        :param crop_coord: 裁剪坐标，一个包含4个整数的列表
        :param item_coord: 项目坐标，一个包含4个整数的列表
        :return: 计算得到的参考值列表
        """
        crop_x_min = page_data.get_crop_x_min()
        crop_y_min = page_data.get_crop_y_min()
        if '_' in search_page:
            if not crop_coord:
                return [item_coord[0] - crop_x_min,
                       item_coord[1] - crop_y_min,
                       item_coord[2] - crop_x_min,
                       item_coord[3] - crop_y_min]
            else:
                return [item_coord[0] - crop_x_min - crop_coord[0],
                       item_coord[1] - crop_y_min - crop_coord[1],
                       item_coord[2] - crop_x_min - crop_coord[0],
                       item_coord[3] - crop_y_min - crop_coord[1]]
        else:
            if not crop_coord:
                return item_coord
            else:
                return [item_coord[0] - crop_coord[0], item_coord[1] - crop_coord[1],
                       item_coord[2] - crop_coord[0], item_coord[3] - crop_coord[1]]

    def _calc_sr(self, ref, w, h):
        it_x1, it_y1, it_x2, it_y2 = ref
        molecule = (min(it_x2, w - 1) - max(it_x1, 0)) * (min(it_y2, h - 1) - max(it_y1, 0))
        denominator = (it_x2 - it_x1) * (it_y2 - it_y1)
        sr = molecule / denominator
        return sr > 0.5

    def _collect_item_info_not_ai(self, answer_item: AnswerItemData, item_id,
                                  item_type, item_answers, ref, item_w, item_h):
        w = answer_item.get_w()
        h = answer_item.get_h()
        for i, region in enumerate(item_answers):
            region_coord = BoxUtil.box_to_list(region['area'])
            region_answer = region['answer']
            block_name = region.get('blockName', None)
            isCombine = region.get('isCombine', False)

            if not block_name:
                region_type = 1
                reg_name = f'{item_id}_{i}'
                region.update({'blockName': reg_name, 'blockType': region_type})
                if region_type == 0 or len(region_coord) == 0:
                    if self._calc_sr(ref, w, h):
                        # TODO 将这里的魔法值2替换为常量
                        answer_item.get_item_res()[reg_name] = 2
                        answer_item.get_reg_names().append(reg_name)
                else:
                    answer_item.get_reg_names().append(reg_name)
                    region_coord_ = [int(r * p) for r, p in zip(region_coord[0], [item_w, item_h] * 2)]
                    abs_coord = [sum(p) for p in zip(ref[:2] * 2, region_coord_)]
                    x_min, y_min, x_max, y_max = abs_coord
                    if x_min > w or y_min > h or x_max < 0 or y_max < 0:
                        answer_item.get_item_res()[reg_name] = 4
                    else:
                        # mode为该空的模式类型，主要是便于后续识别模型的调控
                        if item_type in [TT.SINGLE_CHOICE, TT.MULTI_CHOICE]:
                            mode = TM.CHOICE
                        elif item_type == TT.JUDGE_YES_NO:
                            mode = TM.JUDGMENT
                        elif isCombine:
                            mode = TM.NORMAL
                        else:
                            mode = TopicUtil.get_mode_name(region_answer, item_type)
                        answer_item.get_data_images()[reg_name] = [abs_coord, mode]
                        answer_item.get_reg_coords()[reg_name] = abs_coord
            else:
                for j in range(len(region_coord)):
                    reg_name = f'{block_name}_{j}'
                    if item_type == TT.VERTICAL_CALC or item_type == TT.VERTICAL_CALC_WITH_CHECK:
                        reg_name = f'{block_name}'
                    # TODO 将这里的魔法值2替换为常量
                    answer_item.get_item_res()[reg_name] = 2
                    answer_item.get_reg_names().append(reg_name)

                    if len(region_coord[j]) != 0:
                        region_coord_ = [int(r * p) for r, p in zip(region_coord[j], [item_w, item_h] * 2)]
                        abs_coord = [sum(p) for p in zip(ref[:2] * 2, region_coord_)]
                        x_min, y_min, x_max, y_max = abs_coord
                        if x_min > w or y_min > h or x_max < 0 or y_max < 0:
                            # TODO 将这里的魔法值4替换为常量
                            answer_item.get_item_res()[reg_name] = 4
                        else:
                            if item_type in [TT.SINGLE_CHOICE, TT.MULTI_CHOICE]:
                                mode = TM.CHOICE
                            elif item_type == TT.JUDGE_YES_NO:
                                mode = TM.JUDGMENT
                            elif item_type == TT.CONNECT_LINE:
                                mode = TM.LIANXIAN
                            elif item_type == TT.DRAW_IMG:
                                mode = TM.ZUOTU
                            elif item_type == TT.VERTICAL_CALC or item_type == TT.VERTICAL_CALC_WITH_CHECK:
                                mode = TM.STANDING_FORM_CHECK
                                answer_item.set_standing_form_check(True)
                                answer_item.get_verify_cal_dict()[reg_name] = region.get('verifyCal', 0)
                                answer_item.get_items_answer()[reg_name] = region['answer'][0]['value']
                            elif isCombine:
                                mode = TM.NORMAL
                            else:
                                mode = TopicUtil.get_mode_name([region_answer], item_type)
                            answer_item.get_data_images()[reg_name] = [abs_coord, mode]
                            answer_item.get_reg_coords()[reg_name] = abs_coord
