# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> yanhui
# @Date         ：2024/6/4 11:11
import os
import json
import time
import requests
import datetime
import traceback
from urllib.parse import unquote

from base_common.service.service_of_base import BaseService
from base_common.mission_mode import <PERSON><PERSON><PERSON><PERSON>
from base_common.service.service_of_redis import RedisManager
from correction_service.data import AnswerItemData, PageData
from correction_service.data import CorrectRequest, AnswerDetectRequest
from correction_service.data import CorrectResponse, BookResponse
from correction_service.util import TopicUtil
from correction_service.common import CorrectionContext

from base_common import Context, FileUtil, OssUtil, TopicType as TT, ImageUtil, ErrorCode as EC, \
    CorrectionError as CE, BizCode as BC, ProcessError as PE, OkHttpUtil, Constants, LoggerFactory

from .service_of_ocr import OcrService
from .service_of_book import BookService
from .service_of_answer import AnswerService
from .service_of_answer_detect import AnswerDetectService
cdn_internal_host_map: dict = {}
log = LoggerFactory.get_logger('CorrectionService')

class CorrectionService(BaseService):
    def __init__(self):
        super().__init__()
        self._book_ser = None
        self._ans_ser = None
        self._ocr_ser = None
        self._ans_det_ser = None
        self.redis_service = RedisManager.get_mission_data_redis()
        self.mission_queue_redis = RedisManager.get_mission_queue_redis()

    def autowired(self, book_ser: BookService, ans_ser: AnswerService,
                  ocr_ser: OcrService, ans_det_ser: AnswerDetectService):
        self._book_ser = book_ser
        self._ans_ser = ans_ser
        self._ocr_ser = ocr_ser
        self._ans_det_ser = ans_det_ser

    def do_search(self, req_data: CorrectRequest) -> CorrectResponse:
        """
                批改业务
                :param req_data: 请求体
                :return: @CorrectResponse 批改结果
                """
        if not req_data.get_coord() or max(req_data.get_coord()) > 1:
            return CorrectResponse(message=1, origin_image_url=req_data.get_image_url())

        _, book_data = self._book_ser.do_search(req_data)
        if not book_data.is_success():
            return CorrectResponse(message=str(book_data.get_message()), error_code=book_data.get_error_code(),
                                   origin_image_url=req_data.get_image_url())
        sch_page = book_data.get_search_data()
        resp = sch_page.get_response()
        if resp is None:
            return CorrectResponse(message=str(book_data.get_message()), error_code=book_data.get_error_code(),
                                   origin_image_url=req_data.get_image_url())
        return CorrectResponse(success=True, response={
            'success': True,
            'data': {
                "bookId": req_data.get_book_id(),
                "pageId": resp.get('pageId', None),
                "recordId": req_data.get_record_id(),
                'success': True
            }
        })
    def do_correction(self, req_data: CorrectRequest) -> CorrectResponse:
        """
        批改业务
        :param req_data: 请求体
        :return: @CorrectResponse 批改结果
        """
        if not req_data.get_coord() or max(req_data.get_coord()) > 1:
            return CorrectResponse(message=1, origin_image_url=req_data.get_image_url())
        
        book_data = self._book_ser.generate_book_data(req_data)
        if not book_data.is_success():
            return CorrectResponse(message=str(book_data.get_message()), error_code=book_data.get_error_code(),
                                   origin_image_url=req_data.get_image_url())

        response = self._do_correction(req_data, book_data)
        if response.is_success():
            return self.do_finally_correction(req_data, response.get_response())
        return response

    def fetch_judge(self, req_data: CorrectRequest):
        """
        请求批改
        :param req_data: 请求体
        :return: 批改结果
        """
        mission_id = req_data.get_mission_id()
        correction_data = CorrectionContext.get_correct_data(mission_id)
        judge_url = Context.get_judge_url(req_data.get_user_id(), req_data.get_env())
        new_req = correction_data.transform_to_new_data(req_data.get_biz_type())
        new_req.setBookId(req_data.get_book_id())
        json_data = new_req.toDict()
        datas_judge = {
            'judgeData': json.dumps(json_data, ensure_ascii=False),
            'bookId': req_data.get_book_id(),
            'userId': req_data.get_user_id(),
            'recordId': req_data.get_record_id()
        }

        print_data = {
            'judgeData': json_data,
            'bookId': req_data.get_book_id(),
            'userId': req_data.get_user_id(),
            'recordId': req_data.get_record_id()
        }
        if req_data.get_user_id() is not None:  # frontend access algo directly
            datas_judge.update({'bizType': int(BC.WITH_PDF_PIGAI.value)})
            print_data.update({'bizType': int(BC.WITH_PDF_PIGAI.value)})

        resp = OkHttpUtil.post_request(judge_url, str_data=datas_judge, timeout=3)
        if not Context.is_product():
            log.info(f"(mission_id: {mission_id}) 批改接口请求 {json.dumps(print_data, ensure_ascii=False)}")
            log.info(f"(mission_id: {mission_id}) 批改接口响应 {resp.to_string()}")
        return resp

    def do_finally_correction(self, req_data: CorrectRequest, algo_resp: dict) -> CorrectResponse:
        """
        处理批改结果
        :param req_data: 请求体
        :param algo_resp: 算法返回结果
        :return: @CorrectResponse 批改结果
        """
        if req_data.get_user_id() is not None:
            if 'errorCode' in algo_resp:
                error_judge_url = Context.get_error_judge_url(req_data.get_env())
                error_req = {
                    'recordId': req_data.get_record_id(),
                    'userId': req_data.get_user_id(),
                    'bookId': req_data.get_book_id(),
                    'bizType': req_data.get_biz_type(),
                    'errorInfo': json.dumps(algo_resp, ensure_ascii=False)
                }
                log.error(f"(mission_id: {req_data.get_mission_id()}) error_req as {error_req}, error_url as {error_judge_url}")
                resp = requests.post(url=error_judge_url, data=error_req, timeout=3)
                if resp.status_code != 200:
                    log.error(f"(mission_id: {req_data.get_mission_id()}) 服务端响应错误 {resp.status_code}")
                    return CorrectResponse(success=True, response={'success': True, 'data': {}})
                server_side_resp = resp.json()
                return CorrectResponse(success=True, response=server_side_resp)
        return CorrectResponse(success=True, response=algo_resp)

    def chk_img_url(self, record_id, image_url, env):
        """
        处理用户图片，如果是base64，则先上传oss再生成url，
        这里将保存零时图片，供后续业务使用，在批改结束后删除它
        :param record_id: 记录编号
        :param image_url: 用户图像url/base64
        :param env: 环境
        :return: 图片url，图片临时路径，是否处理成功
        """
        orig_image_path = f'{Constants.TEMP_PATH}/{record_id}_{int(time.time() * 1000)}.jpg'
        #t0 = time.time()
        if image_url.startswith("http"):
            success = ImageUtil.fetch_user_image(image_url, orig_image_path)
            #t0 = time.time() - t0
            #log.info(f"用户图片下载{'成功' if success else '失败'}，临时目录：{orig_image_path} {'%.4f' % t0} sec")
            return image_url, orig_image_path, success
        #log.info(f"开始解析用户图片并上传OSS")
        img_content = unquote(image_url)
        img_content = img_content[img_content.find(';base64,') + len(';base64,'):]
        ImageUtil.base64_to_file(img_content, orig_image_path)
        #log.info(f"文件保存成功，临时路径：{orig_image_path}")
        formatted_today = datetime.date.today().strftime('%Y%m%d')
        image_url = OssUtil.upload(orig_image_path, f'piccorrect/{env}/{formatted_today}/userimages/{orig_image_filename}')
        #t0 = time.time() - t0
        #log.info(f"用户图片为上传OSS成功，地址：{image_url} {t0:.4f} sec")
        return image_url, orig_image_path, True

    def do_oral_calc(self, req_data: CorrectRequest, align_info: list) -> CorrectResponse:
        """
        智能口算
        :param req_data: 请求参数
        :param align_info: 对齐后的图片集合
        :return: 批改结果
        """
        t0 = time.time()
        image_user = req_data.get_user_image()
        if len(align_info) != 0:
            h, w, _ = align_info[0]
        else:
            h, w, _ = image_user.shape

        if req_data.is_correction():
            page_id = '99999999'
            ref_coords = {page_id: [0, 0, w - 1, h - 1]}
            items_type = {page_id: 25}
            img_key, img_type = self.set_image(image_user)
            answer_item = AnswerItemData(w=w, h=h, ref_coords=ref_coords, items_type=items_type, img_key=img_key, img_type=img_type)
            answer_req_data = AnswerDetectRequest(url_photo=req_data.get_image_url(),
                                items=answer_item.get_ref_coords(),
                                items_type=answer_item.get_items_type(),
                                items_answer=answer_item.get_items_answer(),
                                boxs=answer_item.get_reg_coords(),
                                photo_crop=answer_item.get_crop_coord(),
                                temp_crop=[],
                                verify_cal=answer_item.get_verify_cal_dict())

            ad_resp = self._ans_det_ser.internal_answer_detect(req_data, answer_req_data, image_user, img_key, img_type)
            if not ad_resp.is_success():
                Context.report_error(req_data.get_mission_id(), PE.ANSWER_DETECT)
                return CorrectResponse(message=str(CE.ACCESS_ANSWER_DETECT_API_FAILED), error_code=EC.CORRECTION_ERROR,
                                       origin_image_url=req_data.get_image_url(), page_id=page_id)
            self._do_oral_calc(req_data, answer_item, ad_resp.to_dict(), page_id)

            response = self._fetch_judge(req_data, page_id)
            resp_json = response.get_response()
            if not response.is_success():
                return resp_json
            results_dict = resp_json.get('data', None)
            if results_dict is not None:
                if not isinstance(results_dict, dict):
                    results_dict = json.loads(results_dict)
            else:
                log.error(f'(mission_id: {req_data.get_mission_id()}) '
                          f'--No handwritten answer detected (30001)'
                          f' {json.dumps(results_dict, ensure_ascii=False)}')
                return CorrectResponse(message=str(CE.NO_HANDWRITTEN_ANSWER_DETECTED), error_code=30001,
                                       origin_image_url=req_data.get_image_url(), page_id=page_id)

            exist_answer = 0
            try:
                as_list = results_dict['correctResult']
                for single_a in as_list:
                    if single_a["itemResult"]:
                        exist_answer = 1
                        break
                results_dict.pop('pageId', None)
                for q in range(len(results_dict['correctResult'])):
                    results_dict['correctResult'][q].pop('itemId', None)

            except Exception:
                log.error(f"(mission_id: {req_data.get_mission_id()}) Oops!!! at exist_answer {traceback.format_exc()}")

            if exist_answer == 0:
                log.error(f'(mission_id: {req_data.get_mission_id()}) '
                          f'--No handwritten answer detected (30001) '
                          f'{json.dumps(results_dict, ensure_ascii=False)}')
                return CorrectResponse(message=str(CE.NO_HANDWRITTEN_ANSWER_DETECTED), error_code=30001,
                                       origin_image_url=req_data.get_image_url(), page_id=page_id)
            self.__print_item_result(req_data.get_mission_id(), results_dict)
            results_dict['bizType'] = req_data.get_biz_type()
            results_dict['recordId'] = req_data.get_record_id()
            Context.report_cost(req_data.get_mission_id(), ('推送批改结果', time.time() - t0))
            return CorrectResponse(success=True, response=results_dict)

    def _do_oral_calc(self, req_data: CorrectRequest, answer_item: AnswerItemData, resp_detect, page_id):
        reg_coords = resp_detect['boxs']
        TopicUtil.adjust_coords(reg_coords)
        # types字段 0:单行文本; 1:分数; -1:空(连线题标准答案与实际均无连线); -2:答案需要连线，实际没有连线 10:连线题作答正确; 11:连线题作答错误; 12:连线题作答半对;
        reg_class = resp_detect['types']
        # 口算识别结果
        rec_ks = resp_detect.get('recs', None)
        # 智能口算题干
        stem_ks = resp_detect.get('stems', None)
        # 智能口算答案
        answer_ks = resp_detect.get('answers', None)
        answered = 0 if (set(reg_class.values()) == {-1} or len(reg_class) == 0) else 1

        data_imgs = answer_item.get_data_images()
        data_images = {}
        # 根据答案信息覆盖识别答案的类型，1为公式，2为英语
        items_type = answer_item.get_items_type()
        for k, v in reg_coords.items():
            item_id = k.split("_")[0]
            if items_type[item_id] == -1 or items_type[item_id] is None:  # 未开启批改题型
                continue
            if reg_coords[k] and k not in stem_ks.keys():
                if k in data_imgs.keys():
                    if data_imgs[k][1] not in ['lianxian', 'zuotu']:
                        tmp_img = ImageUtil.cut_img(answer_item.get_im_reg(), reg_coords[k])
                        data_images[k] = [tmp_img, data_imgs[k][1], reg_class[k]]  # reg class : 识别类型
                else:
                    assert len(k.split('_')) == 4
                    k_ = '_'.join(k.split('_')[:-1])
                    if data_imgs[k_][1] not in ['lianxian', 'zuotu']:
                        tmp_img = ImageUtil.cut_img(answer_item.get_im_reg(), reg_coords[k])
                        data_images[k] = [tmp_img, data_imgs[k_][1], reg_class[k]]

        correction_data = CorrectionContext.get_correct_data(req_data.get_mission_id())
        correction_data.set_answer_item_info(answer_item)
        # 如果全是智能口算，组装结构，便于与通用批改兼容
        correction_data.set_item_info([{'itemId': int(page_id), 'itemType': TT.INTELLIGENT_ORAL_CALC,
                                        'answerInfo': [
                                            {'blockName': k, 'answer': answer_ks.get(k, None), 'blockType': 0}
                                            for k, v in stem_ks.items()]}])
        correction_data.set_answer_ks(answer_ks)
        correction_data.set_stem_ks(stem_ks)
        correction_data.set_rec_ks(rec_ks)
        correction_data.set_correction(req_data.is_correction())
        correction_data.set_reg_coords(reg_coords)
        correction_data.set_reg_class(reg_class)
        correction_data.set_total_recs({})
        correction_data.set_item_res({})
        correction_data.set_reg_names([])
        correction_data.set_reg_coords_({})
        correction_data.set_crop_coord([])
        correction_data.set_page_id(page_id)
        correction_data.set_align_url(req_data.get_image_url())
        correction_data.set_origin_image_url(req_data.get_image_url())
        correction_data.set_ks_bools(True)
        correction_data.set_answered(answered)
        correction_data.set_w(answer_item.get_w())
        correction_data.set_h(answer_item.get_h())

    def _do_correction(self, req_data: CorrectRequest, book_data: BookResponse) -> CorrectResponse:
        answer_detect_resp = self._ans_det_ser.do_answer_detect(req_data, book_data)
        resp = answer_detect_resp.get_response()
        if not answer_detect_resp.is_success():
            return resp

        answer_data = resp
        answer_item = answer_data.get_answer_item()
        page_data = book_data.get_page_data()
        page_id = page_data.get_page_id()
        item_info = page_data.get_item_info()

        correction_data = CorrectionContext.get_correct_data(req_data.get_mission_id())
        correction_data.set_page_id(page_id)
        correction_data.set_item_info(item_info)
        correction_data.set_ks_bools(answer_data.is_all_ai_oral_calc())
        correction_data.set_answer_item_info(answer_item)

        fixed_item_id = None
        if answer_data.is_all_ai_oral_calc():
            item_ids = [item['itemId'] for item in item_info]
            if len(item_ids) == 1:
                fixed_item_id = item_ids[0]
            # 如果全是智能口算，组装结构，便于与通用批改兼容
            stem_ks = correction_data.get_stem_ks()
            answer_ks = correction_data.get_answer_ks()
            answer_info_list = [
                {
                    'blockName': k,
                    'answer': answer_ks.get(k, None),
                    'blockType': 0
                }
                for k, v in stem_ks.items()
            ]
            item_info = [{
                'itemId': int(page_id),
                'itemType': TT.INTELLIGENT_ORAL_CALC,
                'answerInfo': answer_info_list
            }]
            correction_data.set_item_info(item_info)

        return self._do_judge(req_data, page_data, fixed_item_id)

    def _fetch_judge(self, req_data: CorrectRequest, page_id):
        response = self.fetch_judge(req_data)
        if not response.is_success():
            Context.report_error(req_data.get_mission_id(), PE.CORRECT)
            log.error(f"(mission_id: {req_data.get_mission_id()}) message: {str(CE.ACCESS_ANSWER_JUDGE_API_FAILED)}, resp as {response.to_string()}")
            return CorrectResponse(message=str(CE.ACCESS_ANSWER_JUDGE_API_FAILED), error_code=EC.CORRECTION_ERROR,
                                   origin_image_url=req_data.get_image_url(), page_id=page_id)
        resp = response.get_json_response()
        if 'data' not in resp:
            Context.report_error(req_data.get_mission_id(), PE.CORRECT)
            log.error(f"(mission_id: {req_data.get_mission_id()}) "
                      f"resp: {json.dumps(resp)}")
            log.error(f"(mission_id: {req_data.get_mission_id()}) "
                      f"message: {str(CE.ANSWER_JUDGE_API_LOGIC_ERROR)}; "
                      f"recordId:{req_data.get_record_id()}, "
                      f"originImageUrl: {req_data.get_image_url()}, "
                      f"bookid: {req_data.get_book_id()}, "
                      f"pageId: {page_id}; "
                      f"resp as {response.to_string()}")
            if Context.is_product():
                today = datetime.date.today().strftime('%Y-%m-%d')
                self.redis_service.increment(f'{today}:error_service:500002')
            return CorrectResponse(message=str(CE.ACCESS_ANSWER_JUDGE_API_FAILED), error_code=EC.CORRECTION_ERROR,
                                   origin_image_url=req_data.get_image_url(), page_id=page_id)
        return CorrectResponse(success=True, response=resp)

    def _do_judge(self, req_data: CorrectRequest, page_data: PageData, fixed_item_id=None) -> CorrectResponse:
        log.debug(f"开始请求批改")
        t0 = time.time()
        page_id = page_data.get_page_id()
        # fix： 智能口算不能返回正确的itemId，
        # 导致H5页面无法查看本题的全部答案。 by yanhui 2024-6-6
        if fixed_item_id is not None:
            self._book_ser.fix_item_id(req_data.get_mission_id(), page_data.get_item_info(),
                                       page_id, str(fixed_item_id))

        response = self._fetch_judge(req_data, page_id)
        resp_json = response.get_response()
        log.debug(f"完成批改请求")
        if not response.is_success():
            return resp_json

        results_dict = resp_json['data']
        if isinstance(results_dict, str):
            results_dict = json.loads(resp_json['data'])

        if not Context.is_product():
            self.__print_item_result(req_data.get_mission_id(), results_dict)
        results_dict['bizType'] = req_data.get_biz_type()
        results_dict['recordId'] = req_data.get_record_id()
        Context.report_cost(req_data.get_mission_id(), (MissionAlisa.JUDGE, time.time() - t0))
        return CorrectResponse(success=True, response=results_dict)

    def __print_item_result(self, mission_id, results_dict):
        try:
            item_results = []
            correct_result = results_dict['correctResult']
            for index in range(len(correct_result)):
                item_result = correct_result[index]['itemResult']
                item_results.append(item_result)
            log.info(f"(mission_id: {mission_id}) 批改题目结果: {json.dumps(item_results, ensure_ascii=False)}")
        except:
            pass
