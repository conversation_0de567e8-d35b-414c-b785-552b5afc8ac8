# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/14 8:45 
# @Description  : logo.py

GREEN = "\033[32m"
BLUE = "\033[34m"
MAGENTA = "\033[35m"
CYAN = "\033[36m"
RESET = "\033[0m"

def print_colored_correction():
    # ANSI 转义序列定义
    __version__ = "v2.0.5"
    _LOGO_ = """
    ****************************************************************************   
    *    ______                                       __     _                 *
    *   / ____/  ____    _____   _____  ___   _____  / /_   (_)  ____    ____  *
    *  / /      / __ \  / ___/  / ___/ / _ \ / ___/ / __/  / /  / __ \  / __ \ *
    * / /___   / /_/ / / /     / /    /  __// /__  / /_   / /  / /_/ / / / / / *
    * \____/   \____/ /_/     /_/     \___/ \___/  \__/  /_/   \____/ /_/ /_/  *
    ****************************************************************************"""

    print(f"{MAGENTA}{_LOGO_}{RESET}")
    print(f"    :: {CYAN}拍照批改{RESET} ::            {__version__}\n")
    print(f"{GREEN}系统正在初始化...{RESET}")
# 在任何代码执行之前调用彩色打印函数
print_colored_correction()
