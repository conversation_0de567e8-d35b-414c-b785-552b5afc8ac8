# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/28 20:18 
# @Description  : correction_context.py
from correction_service.data import CorrectionData

class PrivateCorrectionContext:
    def __init__(self):
        self.__correct_data_dict = dict()
    def pop_correct_data(self, mission_id):
        self.__correct_data_dict.pop(mission_id, None)

    def get_correct_data(self, mission_id):
        correction_data = self.__correct_data_dict.get(mission_id, None)
        if correction_data is None:
            correction_data = CorrectionData()
            self.__correct_data_dict[mission_id] = correction_data
        return correction_data

class CorrectionContext:
    context = None
    @classmethod
    def setup(cls):
        cls.context = PrivateCorrectionContext()

    @classmethod
    def get_correct_data(cls, mission_id) -> CorrectionData:
        return cls.context.get_correct_data(mission_id)

    @classmethod
    def pop_correct_data(cls, mission_id):
        cls.context.pop_correct_data(mission_id)