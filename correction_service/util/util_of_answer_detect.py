# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 8:29 
# @Description  : util_of_answer_detect.py
import os
import re # update by dewen
from fractions import Fraction
from PIL import Image, ImageDraw
import numpy as np
from scipy.optimize import linear_sum_assignment

import difflib
import json
from base_common import LoggerFactory, BoxUtil

log = LoggerFactory.get_logger('AnswerDetectUtil')
class AnswerDetectUtil:
    @staticmethod
    def environ_answers_by_answer(answer_stem, pred):
        if answer_stem.find('……') != -1:
            return pred
        try:
            if abs(eval(answer_stem.find('=')[0]) - float(answer_stem.find('=')[-1])) < 1e-6:
                return pred
            answer = answer_stem.split('=')[-1]
            if answer.find('.') == -1:
                return str(int(float(pred)))
            else:
                num_f = len(answer.split('.')[-1])
                if num_f == 1:
                    return '%.1f' % float(pred)
                if num_f == 2:
                    return '%.2f' % float(pred)
                if num_f == 3:
                    return '%.3f' % float(pred)
                if num_f == 4:
                    return '%.4f' % float(pred)
                else:
                    return pred
        except:
            return pred
    @staticmethod
    def string_similar(s1, s2):
        return difflib.SequenceMatcher(lambda x: x == ' ', s1, s2).quick_ratio()
        
    @staticmethod    
    def edit_distance_optimized(s1: str, s2: str) -> int:
        m, n = len(s1), len(s2)
        # 使用一维数组优化空间
        dp = list(range(n + 1))  # 初始化为第一行（i=0的情况）
        
        for i in range(1, m + 1):
            prev = dp[0]  # 保存左上角的值（dp[i-1][j-1]）
            dp[0] = i     # 更新第一列（j=0的情况）
            for j in range(1, n + 1):
                temp = dp[j]  # 保存当前行前一个位置的值（dp[i][j-1]）
                if s1[i - 1] == s2[j - 1]:
                    dp[j] = prev
                else:
                    dp[j] = min(prev + 1, dp[j] + 1, temp + 1)
                prev = temp  # 更新左上角的值为当前j的旧值（即下一轮j+1的左上角）
        if dp[n] / min(m,n) >= 0.4:
            return 1e3
        else:
            return dp[n]
        
    @staticmethod
    def blur_shushi_item(reg_item, std_item, reg=''):
        if reg_item.find('=') == -1 or std_item.find('=') == -1:
            if len(reg) == 0:
                return reg_item, std_item
            else:
                return reg_item, std_item, reg

        reg_0, reg_1 = reg_item.split('=')[0:2]
        std_0, std_1 = std_item.split('=')[0:2]
        similar = min(AnswerDetectUtil.edit_distance_optimized(reg_0, std_0),AnswerDetectUtil.edit_distance_optimized(std_0,reg_0))
        if len(reg) == 0:
            if similar <= 2:
                return std_0 + '=' + reg_1, std_item
            else:
                return reg_item, std_item
        else:
            if similar <= 2:
                return std_0 + '=' + reg_1, std_item, reg.replace(reg_0, std_0)
            else:
                return reg_item, std_item, reg
                

    
    @staticmethod
    def change_forume_short(yansuan_item):
        p0 = yansuan_item
        if p0.find('+') != -1:
            p0_0, p0_1 = p0.split('+')

            if p0_0.find('*') != -1:
                p0_0_0, p0_0_1 = p0_0.split('*')

                return p0_0_1 + '*' + p0_0_0 + '+' + p0_1
            else:
                return p0_1 + '+' + p0_0
        if p0.find('*') != -1:
            p0_0, p0_1 = p0.split('*')
            return p0_1 + '*' + p0_0
        return yansuan_item
    @staticmethod
    def calc_iou(bbox1, bbox2):
        if not isinstance(bbox1, np.ndarray):
            bbox1 = np.array(bbox1)
        if not isinstance(bbox2, np.ndarray):
            bbox2 = np.array(bbox2)
        xmin1, ymin1, xmax1, ymax1, = np.split(bbox1, 4, axis=-1)
        xmin2, ymin2, xmax2, ymax2, = np.split(bbox2, 4, axis=-1)

        area1 = (xmax1 - xmin1) * (ymax1 - ymin1)
        area2 = (xmax2 - xmin2) * (ymax2 - ymin2)

        ymin = np.maximum(ymin1, np.squeeze(ymin2, axis=-1))
        xmin = np.maximum(xmin1, np.squeeze(xmin2, axis=-1))
        ymax = np.minimum(ymax1, np.squeeze(ymax2, axis=-1))
        xmax = np.minimum(xmax1, np.squeeze(xmax2, axis=-1))

        h = np.maximum(ymax - ymin, 0)
        w = np.maximum(xmax - xmin, 0)
        intersect = h * w

        iou_1 = intersect / area1
        iou_2 = intersect / area2.reshape(1, -1)
        ious_ = np.stack((iou_1, iou_2), 0)
        ious = ious_.max(0)
        return ious
    @staticmethod
    def get_target_boxs(boxs_full_pages, bb, lenth=4, iou_thr=0.2,return_iou=False):
        # boxs_full_pages：所有手写字的box区域；bb:题目区域
        target_boxs = []
        ious_return = []
        if boxs_full_pages is None or len(boxs_full_pages) == 0:
            if return_iou:
                return [],[]
            else:
                return []
        for box in boxs_full_pages:
            if isinstance(box[0], list):
                box_ = np.array(box)
                t_box = [box_[:, 0].min(), box_[:, 1].min(), box_[:, 0].max(), box_[:, 1].max()]
                iou = AnswerDetectUtil.calc_iou([t_box], [bb])
                if iou > iou_thr:
                    if lenth == 8:
                        target_boxs.append([
                            box[0][0], box[0][1], box[1][0], box[1][1],
                            box[2][0], box[2][1], box[3][0], box[3][1]
                        ])
                    else:
                        target_boxs.append(box)
                    ious_return.append(iou)

            else:
                iou = AnswerDetectUtil.calc_iou([box], [bb])
                if iou > iou_thr:
                    target_boxs.append(box)
                    ious_return.append(iou)
                    continue
        if return_iou:
            return target_boxs,ious_return
        else:
            return target_boxs
            
    @staticmethod
    def choose_n_1_box(boxs, bb):
        if len(boxs) == 1:
            return boxs[0]
        ious = AnswerDetectUtil.calc_iou(boxs, [bb])
        return boxs[np.argmax(ious[:, 0])]
    @staticmethod
    def get_yansuan_item(gt):
        if gt.find('+') != -1:
            item1 = gt.split('+')[0]
            item2 = gt.split('+')[-1].split('=')[0]
            item3 = gt.split('+')[-1].split('=')[1]
            return [item2 + '+' + item1 + '=' + item3 ,item3+'-'+item2+'='+item1,item3+'-'+item1+'='+item2]
            # return [item3+'-'+item2+'='+item1,item3+'-'+item1+'='+item2]
        if gt.find('-') != -1:
            item1 = gt.split('-')[0]
            item2 = gt.split('-')[-1].split('=')[0]
            item3 = gt.split('-')[-1].split('=')[1]
            return [item3 + '+' + item2 + '=' + item1, item2 + '+' + item3 + '=' + item1, item1 + '-' + item3 + '=' + item2]
        if gt.find('*') != -1:
            item1 = gt.split('*')[0]
            item2 = gt.split('*')[-1].split('=')[0]
            item3 = gt.split('*')[-1].split('=')[1]
            return [item2 + '*' + item1 + '=' + item3]
            # return [item3+'/'+item2+'='+item1,item3+'/'+item1+'='+item2]
        if gt.find('/') != -1:
            item1 = gt.split('/')[0]
            item2 = gt.split('/')[-1].split('=')[0]
            item3 = gt.split('/')[-1].split('=')[1]
            if gt.find('……') != -1:
                shang, yu = item3.split('……')
                return [shang + '*' + item2 + '+' + yu + '=' + item1, item2 + '*' + shang + '+' + yu + '=' + item1]
            else:
                return [item3 + '*' + item2 + '=' + item1, item2 + '*' + item3 + '=' + item1]
        return None
    @staticmethod
    def calc_dist(boxs, preds):
        if not isinstance(boxs, np.ndarray):
            boxs = np.array(boxs)
        if not isinstance(preds, np.ndarray):
            preds = np.array(preds)

        flag_x1 = ((boxs[:, 0].reshape(len(boxs), 1) - preds[:, 0])) * (boxs[:, 2].reshape(len(boxs), 1) - preds[:, 0])
        dist_x1 = np.stack([np.abs(boxs[:, 0].reshape(len(boxs), 1) - preds[:, 0]),
                            np.abs(boxs[:, 2].reshape(len(boxs), 1) - preds[:, 0])]).min(0)
        flag_x1_ = np.ones_like(flag_x1)
        flag_x1_[np.where(flag_x1 <= 0)] = 0
        dist_x1 *= flag_x1_

        flag_x2 = ((boxs[:, 0].reshape(len(boxs), 1) - preds[:, 2])) * (boxs[:, 2].reshape(len(boxs), 1) - preds[:, 2])
        dist_x2 = np.stack([np.abs(boxs[:, 0].reshape(len(boxs), 1) - preds[:, 2]),
                            np.abs(boxs[:, 2].reshape(len(boxs), 1) - preds[:, 2])]).min(0)
        flag_x2_ = np.ones_like(flag_x2)
        flag_x2_[np.where(flag_x2 <= 0)] = 0
        dist_x2 *= flag_x2_

        dist_x = np.stack([dist_x1, dist_x2]).min(0)

        flag_y1 = ((boxs[:, 1].reshape(len(boxs), 1) - preds[:, 1])) * (boxs[:, 3].reshape(len(boxs), 1) - preds[:, 1])
        dist_y1 = np.stack([np.abs(boxs[:, 1].reshape(len(boxs), 1) - preds[:, 1]),
                            np.abs(boxs[:, 3].reshape(len(boxs), 1) - preds[:, 1])]).min(0)
        flag_y1_ = np.ones_like(flag_y1)
        flag_y1_[np.where(flag_y1 <= 0)] = 0
        dist_y1 *= flag_y1_

        flag_y2 = ((boxs[:, 1].reshape(len(boxs), 1) - preds[:, 3])) * (boxs[:, 3].reshape(len(boxs), 1) - preds[:, 3])
        dist_y2 = np.stack([np.abs(boxs[:, 1].reshape(len(boxs), 1) - preds[:, 3]),
                            np.abs(boxs[:, 3].reshape(len(boxs), 1) - preds[:, 3])]).min(0)
        flag_y2_ = np.ones_like(flag_y2)
        flag_y2_[np.where(flag_y2 <= 0)] = 0
        dist_y2 *= flag_y2_

        dist_y = np.stack([dist_y1, dist_y2]).min(0)

        dists = np.sqrt(dist_x ** 2 + dist_y ** 2)

        return dists
    @staticmethod
    def cal_dists_mat(boxs, preds):
        if not isinstance(boxs, np.ndarray):
            boxs = np.array(boxs)
        if not isinstance(preds, np.ndarray):
            preds = np.array(preds)
        dists_m = np.ones((len(boxs), len(preds))) * 1e6
        for m in range(len(boxs)):
            for n in range(len(preds)):
                dist_0 = (((boxs[m][0] - preds[n][0]) ** 2 + (boxs[m][1] - preds[n][1]) ** 2) ** 0.5)
                dist_1 = (((boxs[m][2] - preds[n][2]) ** 2 + (boxs[m][1] - preds[n][1]) ** 2) ** 0.5)
                dist_2 = (((boxs[m][2] - preds[n][2]) ** 2 + (boxs[m][3] - preds[n][3]) ** 2) ** 0.5)
                dist_3 = (((boxs[m][0] - preds[n][0]) ** 2 + (boxs[m][3] - preds[n][3]) ** 2) ** 0.5)
                dist_4 = ((((boxs[m][0] + boxs[m][2]) / 2 - (preds[n][0] + preds[n][2]) / 2) ** 2 + (
                        (boxs[m][1] + boxs[m][3]) / 2 - (preds[n][1] + preds[n][3]) / 2) ** 2) ** 0.5)
                dists_m[m][n] = min(dist_0, dist_1, dist_2, dist_3, dist_4)
        return dists_m
    # 对检测到的结果根据情况进行合并或分离
    @staticmethod
    def concate_or_split_boxs(boxs, preds, preds_label, iou_thr=0.3, angle_thr=1.):
        ious = AnswerDetectUtil.calc_iou(preds, boxs)
        ious_t = ious.transpose(1, 0)
        preds_tmp = []
        labels_tmp = []
        # 处理一个预测框包揽了两个gt的情况
        # 进行分离
        # 分离条件：如果一个检测框包含了两个gt，那么根据gt的位置将检测框切开
        for i, iou in enumerate(ious):
            a = np.where(iou > iou_thr)[0]
            if len(a) <= 1:
                preds_tmp.append(preds[i])
                labels_tmp.append(preds_label[i])
                continue

            else:
                boxs_ = [boxs[a[j]] for j in range(len(a))]
                centers_ = [((boxs_[j][0] + boxs_[j][2]) / 2, (boxs_[j][1] + boxs_[j][3]) / 2) for j in range(len(a))]
                gap_x = abs(centers_[0][0] - centers_[1][0])
                gap_y = abs(centers_[0][1] - centers_[1][1])
                if gap_x > gap_y and gap_y < 0.2 * (boxs_[0][3] - boxs_[0][1]):
                    if (boxs_[0][2] - boxs_[0][0]) < (boxs_[0][3] - boxs_[0][1]):
                        preds_tmp.append(preds[i])
                        labels_tmp.append(preds_label[i])
                        continue
                
                    orders_ = np.argsort(np.array(centers_)[:, 0])
                    pred = preds[i]
                    record = pred[0]
                    for k in range(len(a) - 1):
                        split_x = int(boxs_[orders_[k + 1]][0] - boxs_[orders_[k]][2]) // 2 + boxs_[orders_[k]][2]
                        preds_tmp.append([record, pred[1], split_x, pred[3]])
                        labels_tmp.append(preds_label[i])
                        record = split_x

                    preds_tmp.append([record, pred[1], pred[2], pred[3]])
                    labels_tmp.append(preds_label[i])
                elif gap_x < 0.2 * (boxs_[0][2] - boxs_[0][0]):
                    if (boxs_[0][2] - boxs_[0][0]) > (boxs_[0][3] - boxs_[0][1]):
                        preds_tmp.append(preds[i])
                        labels_tmp.append(preds_label[i])
                        continue
                        
                    orders_ = np.argsort(np.array(centers_)[:, 1])
                    pred = preds[i]
                    record = pred[1]
                    for k in range(len(a) - 1):
                        split_y = int(boxs_[orders_[k + 1]][1] - boxs_[orders_[k]][3]) // 2 + boxs_[orders_[k]][3]
                        preds_tmp.append([pred[0], record, pred[2], split_y])
                        labels_tmp.append(preds_label[i])
                        record = split_y

                    preds_tmp.append([pred[0], record, pred[2], pred[3]])
                    labels_tmp.append(preds_label[i])
                else:
                    preds_tmp.append(preds[i])
                    labels_tmp.append(preds_label[i])

        #         if len(a) == 2:
        #             pred = preds[i]
        #             box_0 = boxs[a[0]]
        #             box_1 = boxs[a[1]]
        #             if box_0[3]<box_1[1] or box_1[3]<box_0[1]:
        #                 preds_tmp.append(preds[i])
        #                 labels_tmp.append(preds_label[i])
        #                 continue

        #             center_0 = ((box_0[0]+box_0[2])/2,(box_0[1]+box_0[3])/2)
        #             center_1 = ((box_1[0]+box_1[2])/2,(box_1[1]+box_1[3])/2)
        #             if abs((center_0[1]-center_1[1])/(center_0[0]-center_1[0]+1e-6)) > angle_thr:
        #                 preds_tmp.append(preds[i])
        #                 labels_tmp.append(preds_label[i])
        #                 continue

        #             if box_0[0]>=box_1[2]:
        #                 split_x = int(box_0[0]-box_1[2])//2 + box_1[2]
        #             else:
        #                 split_x = int(box_1[0]-box_0[2])//2 + box_0[2]

        #             preds_tmp.append([pred[0],pred[1],split_x,pred[3]])
        #             labels_tmp.append(preds_label[i])
        #             preds_tmp.append([split_x,pred[1],pred[2],pred[3]])
        #             labels_tmp.append(preds_label[i])
        #         else:
        #             preds_tmp.append(preds[i])
        #             labels_tmp.append(preds_label[i])

        preds = preds_tmp
        labels = labels_tmp
        remove_ind = []
        labels_add = []
        preds_add = []
        ious = AnswerDetectUtil.calc_iou(boxs, preds)

        # 处理一个gt包含两个预测框的情况
        # 进行合并
        # 合并条件：如果两个检测框被一个gt包围，那么就将这两个检测框合并成一个
        for i, iou in enumerate(ious):
            a = np.where(iou > 0.5)[0]

            if len(a) <= 1:
                continue

            else:
                preds_ = [preds[a[j]] for j in range(len(a))]
                labels_ = [labels[a[j]] for j in range(len(a))]

                if len(set(labels_)) != 1:
                    continue

                for k in range(len(a)):
                    remove_ind.append(a[k])
                labels_add.append(labels_[0])
                preds_add.append([min(np.array(preds_)[:, 0]), min(np.array(preds_)[:, 1]), max(np.array(preds_)[:, 2]),
                                  max(np.array(preds_)[:, 3])])

        #         if len(a) == 2:
        #             pred_0 = preds[a[0]]
        #             pred_1 = preds[a[1]]

        #             if pred_0[3]<pred_1[1] or pred_1[3]<pred_0[1]:
        #                 continue
        #             center_0 = ((pred_0[0]+pred_0[2])/2,(pred_0[1]+pred_0[3])/2)
        #             center_1 = ((pred_1[0]+pred_1[2])/2,(pred_1[1]+pred_1[3])/2)
        #             if abs((center_0[1]-center_1[1])/(center_0[0]-center_1[0]+1e-6)) > angle_thr:
        #                 continue

        #             if labels[a[0]] == labels[a[1]]:
        #                 remove_ind.append(a[0])
        #                 remove_ind.append(a[1])
        #                 labels_add.append(labels[a[0]])
        #                 preds_add.append([min(pred_0[0],pred_1[0]),min(pred_0[1],pred_1[1]),max(pred_0[2],pred_1[2]),max(pred_0[3],pred_1[3])])
        preds_final = []
        labels_final = []
        for ind in range(len(preds)):
            if not ind in remove_ind:
                preds_final.append(preds[ind])
                labels_final.append(labels[ind])
        preds_final += preds_add
        labels_final += labels_add
        return boxs, preds_final, labels_final
    @staticmethod
    def match_res(boxs, preds, preds_label):
        n_boxs = len(boxs)
        n_preds = len(preds)
        flag_boxs = np.ones(n_boxs)
        flag_preds = np.ones(n_preds)
        res_boxs = [[]] * n_boxs
        res_labels = [-1] * n_boxs
        preds_label = np.array(preds_label)
        ious = AnswerDetectUtil.calc_iou(boxs, preds)
        while True:
            max_iou = ious.max()
            if max_iou <= 0.:
                break
            inds = ious.argmax()
            ind_boxs = inds // n_preds
            ind_preds = inds % n_preds
            if len(res_boxs[ind_boxs]) != 0:
                ious[ind_boxs, ind_preds] = -10.
                continue
            flag_boxs[ind_boxs] = 0
            flag_preds[ind_preds] = 0
            res_boxs[ind_boxs] = preds[ind_preds].tolist()
            res_labels[ind_boxs] = preds_label[ind_preds].tolist()
            ious[ind_boxs, :] = -10.
            ious[:, ind_preds] = -10.

        if flag_boxs.sum() == 0 or flag_preds.sum() == 0:
            return res_boxs, res_labels

            # 剩余未匹配的再次匹配
        rest_boxs = boxs[np.where(flag_boxs == 1)]
        rest_boxs_indices = np.where(flag_boxs == 1)[0]

        rest_preds = preds[np.where(flag_preds == 1)]
        rest_preds_indices = np.where(flag_preds == 1)[0]
        rest_labels = preds_label[np.where(flag_preds == 1)]

        mean_h = (preds[:, 3] - preds[:, 1]).mean()
        dist_thr = min(20, mean_h * 2.)

        dist_res_boxs, dist_res_labels = AnswerDetectUtil.match_res_dist(rest_boxs, rest_preds, rest_labels, dist=dist_thr)
        for m, ind in enumerate(rest_boxs_indices):
            if dist_res_labels[m] == -1:
                continue
            res_boxs[ind] = dist_res_boxs[m]
            res_labels[ind] = dist_res_labels[m]
        return res_boxs, res_labels

    @staticmethod
    def match_res_dist(boxs, preds, preds_labels, dist=50):
        # print('boxs,preds: ',boxs,preds,preds_labels)
        n_boxs = len(boxs)
        n_preds = len(preds)
        flag_boxs = np.ones(n_boxs)
        flag_preds = np.ones(n_preds)
        res_boxs = [[]] * n_boxs
        res_labels = [-1] * n_boxs
        dists = AnswerDetectUtil.calc_dist(boxs, preds)
        #     print('dists:................. ',dists)
        while True:
            min_dist = dists.min()
            if min_dist > dist:
                break

            inds = dists.argmin()
            ind_boxs = inds // n_preds
            ind_preds = inds % n_preds

            if len(res_boxs[ind_boxs]) != 0:
                dists[ind_boxs, ind_preds] = 10000.
                continue
            flag_boxs[ind_boxs] = 0
            flag_preds[ind_preds] = 0

            res_boxs[ind_boxs] = preds[ind_preds].tolist()
            res_labels[ind_boxs] = preds_labels[ind_preds].tolist()
            dists[ind_boxs, :] = 1000000.
            dists[:, ind_preds] = 1000000.
            if flag_boxs.sum() == 0:
                #             print(res_boxs)
                return res_boxs, res_labels
        #     print(res_boxs)
        return res_boxs, res_labels

    @staticmethod
    def match_res_hungrain(boxs, preds, preds_label):
        dists_m = AnswerDetectUtil.cal_dists_mat(boxs, preds)
        #     print(boxs.shape,preds.shape,dists_m.shape)
        row_ind, col_ind = linear_sum_assignment(dists_m)

        mean_h = (boxs[:, 3] - boxs[:, 1]).mean()
        dist_thr = mean_h * 1.5

        res_boxs = [[]] * len(boxs)
        res_labels = [-1] * len(boxs)
        preds_matched_inds = []
        for n in range(len(row_ind)):
            ind_box = row_ind[n]
            ind_pred = col_ind[n]

            if dists_m[ind_box][ind_pred] < dist_thr:
                res_boxs[ind_box] = preds[ind_pred].tolist()
                res_labels[ind_box] = preds_label.tolist()[ind_pred]
                preds_matched_inds.append(ind_pred)

        flag = False
        for res_box in res_boxs:
            if len(res_box) == 0:
                flag = True
                break
        if flag:
            res_preds_box = []
            res_preds_labels = []
            for i in range(len(preds)):
                if i not in preds_matched_inds:
                    res_preds_box.append(preds[i])
                    res_preds_labels.append(preds_label[i])
            if len(res_preds_box) != 0:

                res_boxs_nms, res_labels_nms = AnswerDetectUtil.match_res(boxs, np.array(res_preds_box, dtype=np.int32),
                                                         np.array(res_preds_labels, dtype=np.int32))
                for k, res_box in enumerate(res_boxs):
                    if len(res_box) == 0 and len(res_boxs_nms[k]) != 0:
                        res_boxs[k] = res_boxs_nms[k]
                        res_labels[k] = res_labels_nms[k]

        return res_boxs, res_labels
    @staticmethod
    def adjust_box_ratio(box):
        if len(box) == 0:
            return box
        #     print(box)
        w = box[2] - box[0]
        h = box[3] - box[1]
        if w == 0:
            return box
        if h / w > 2:
            w_new = h / 2
            pad = int((w_new - w) / 2)
            box[0] -= max(pad, 4)
            box[2] += max(pad, 4)

            box[3] += 4
            return box
        else:
            box[0] -= 4
            box[2] += 4

            box[3] += 4
            return box
    @staticmethod
    def remove_overlap(boxs, res_boxs, res_types):
        if len(res_boxs) <= 1:
            return res_boxs, res_types
        res_box_list = []
        res_id_list = []
        #     print(res_boxs)
        for key in res_boxs.keys():
            if len(key.split('_')) > 3 or len(res_boxs[key]) == 0:
                continue

            if isinstance(res_boxs[key][0], int):
                pass
            else:
                continue

            if key in boxs.keys() and len(res_boxs[key]) == 4:
                res_box_list.append(res_boxs[key])
                res_id_list.append(key)
        if len(res_box_list) <= 1:
            return res_boxs, res_types
        ious = AnswerDetectUtil.calc_iou(res_box_list, res_box_list)
        for i in range(len(res_box_list)):
            ious[i][i] = 0.
            for j in range(len(res_box_list)):
                if ious[i][j] > 0.8:
                    id_i = res_id_list[i]
                    id_j = res_id_list[j]
                    if id_i.split('_')[0] == id_j.split('_')[0]:
                        ious[i][j] = 0.
                        ious[j][i] = 0.
                        continue
                    gt_i = boxs[id_i]
                    gt_j = boxs[id_j]

                    pre_i = res_boxs[id_i]
                    pre_j = res_boxs[id_j]
                    
                    if len(pre_i) == 0 or len(pre_j) == 0:
                        ious[i][j] = 0.
                        ious[j][i] = 0.
                        continue
                        
                    iou_gti_prei = AnswerDetectUtil.calc_iou([gt_i], [pre_i])[0][0]
                    iou_gtj_prej = AnswerDetectUtil.calc_iou([gt_j], [pre_j])[0][0]

                    if iou_gti_prei > iou_gtj_prej:
                        ious[i][j] = 0.
                        ious[j][i] = 0.
                        res_types[id_j] = -1
                        res_boxs[id_j] = []
                    elif iou_gtj_prej > iou_gti_prei:
                        ious[i][j] = 0.
                        ious[j][i] = 0.
                        res_types[id_i] = -1
                        res_boxs[id_i] = []
                    else:
                        area_i = (pre_i[2] - pre_i[0]) * (pre_i[3] - pre_i[1])
                        area_j = (pre_j[2] - pre_j[0]) * (pre_j[3] - pre_j[1])
                        if area_i >= area_j:
                            ious[i][j] = 0.
                            ious[j][i] = 0.
                            res_types[id_j] = -1
                            res_boxs[id_j] = []
                        else:
                            ious[i][j] = 0.
                            ious[j][i] = 0.
                            res_types[id_i] = -1
                            res_boxs[id_i] = []

        return res_boxs, res_types
    @staticmethod
    def vis_result(img, answers_json, items_json, res_json, res_type, inf_num, save_dir):
        try:
            draw = ImageDraw.Draw(img)

            # 画标准答案区域box
            for k in answers_json.keys():
                answer_box = answers_json[k]
                if len(answer_box) == 0:
                    continue
                else:
                    draw.rectangle([int(answer_box[0]), int(answer_box[1]), int(answer_box[2]), int(answer_box[3])],
                                   outline=(0, 255, 0))

                    # 画题目区域box
            for k in items_json.keys():
                item_box = items_json[k]
                if len(item_box) == 0:
                    continue
                else:
                    try:
                        draw.rectangle([int(item_box[0]), int(item_box[1]), int(item_box[2]), int(item_box[3])],
                                       outline=(0, 0, 255))
                    except:
                        print('wrong item box indices')
                        continue

            # 画预测手写答案
            for k in res_json.keys():
                res_box = res_json[k]

                pred_left_top = []
                if len(res_box) == 0:
                    continue
                elif isinstance(res_box[0], list):  #########
                    draw.polygon([(int(res_box[0][0]), int(res_box[0][1])), (int(res_box[1][0]), int(res_box[1][1])),
                                  (int(res_box[2][0]), int(res_box[2][1])), (int(res_box[3][0]), int(res_box[3][1]))],
                                 outline=(255, 0, 0))
                    draw.text((int(res_box[0][0]), int(res_box[0][1])), str(res_type[k]), align="left")
                    pred_left_top = [(int(res_box[0][0]), int(res_box[0][1]))]
                else:
                    draw.rectangle([int(res_box[0]), int(res_box[1]), int(res_box[2]), int(res_box[3])],
                                   outline=(255, 0, 0))
                    #             print(res_type)
                    draw.text((int(res_box[0]), int(res_box[1])), str(res_type[k]), align="left")
                    pred_left_top = [int(res_box[0]), int(res_box[1])]
                try:
                    standard_left_top = [int(answers_json[k][0]), int(answers_json[k][1])]
                    #             print(pred_left_top,standard_left_top)
                    draw.line((pred_left_top[0], pred_left_top[1], standard_left_top[0], standard_left_top[1]), 'red')
                except:
                    continue

            del draw
            img.save(os.path.join(save_dir, '{}.jpg'.format(inf_num)))
        except:
            pass
    @staticmethod
    def align_boxs(boxs):
        if len(boxs) == 0:
            return []
        box_numpy = np.array(boxs)
        if len(box_numpy.shape) == 3:
            box_numpy = box_numpy.reshape(-1, 8)
        boxs_ys = box_numpy[:, 0::2]
        boxs_y_mean = boxs_ys.mean(1)
        indices = np.argsort(boxs_y_mean)
        boxs_final = []
        for ind in indices:
            bb = box_numpy.tolist()[ind]
            boxs_final.append([[bb[0], bb[1]], [bb[2], bb[3]], [bb[4], bb[5]], [bb[6], bb[7]]])
        return boxs_final
    @staticmethod
    def extract_paint(answer_list, item_box, padded_box):
        '''
        answer_list:[{
            "answer":[
                [
                    {
                        "type":"text",
                        "value":"circle"
                    }
                ]
            ],
            "blockName":"6171_0",
            "blockType":1,
            "region":[ 基于当前题目框的坐标与当前题目框长宽的比值
                [
                    0.4472,
                    0.1177,
                    0.5964,
                    0.2448
                ]
            ]
        },...]

        转换成：

        answers:[{'box_id':str,'boxs':list,'ans_type':'str'},...]
        '''
        w = item_box[2] - item_box[0]
        h = item_box[3] - item_box[1]

        answers = []
        for ans_ori in answer_list:
            ans_f = {
                'box_id': ans_ori['blockName'],
                'ans_type': ans_ori['answer'][0]['value']
            }
            region = BoxUtil.box_to_list(ans_ori['area'])[0]

            b0 = int(region[0] * w) + (item_box[0] - padded_box[0])
            b1 = int(region[1] * h) + (item_box[1] - padded_box[1])
            b2 = int(region[2] * w) + (item_box[0] - padded_box[0])
            b3 = int(region[3] * h) + (item_box[1] - padded_box[1])

            ans_f['boxs'] = [b0, b1, b2, b3]
            answers.append(ans_f)

            #print([b0, b1, b2, b3])
        return answers

    @staticmethod
    def align_boxs_2p(boxs):
        if len(boxs) == 0:
            return []
        box_numpy = np.array(boxs)
        if len(box_numpy.shape) == 3:
            box_numpy = box_numpy.reshape(-1, 8)
        boxs_ys = box_numpy[:, 0::2]
        boxs_y_mean = boxs_ys.mean(1)
        indices = np.argsort(boxs_y_mean)
        boxs_final = []
        for ind in indices:
            bb = box_numpy.tolist()[ind]
            boxs_final.append([[bb[0], bb[1]], [bb[2], bb[1]], [bb[2], bb[3]], [bb[0], bb[3]]])
        return boxs_final
    @staticmethod
    def add_outside_answer(boxs_list_4p, bb_r, tuoshi_flag=False):
        # bb_r有两种格式，要进行兼容（n,4,2）（n,8）
        if len(boxs_list_4p) == 0 or len(bb_r) == 0:
            return []
        y_max = 0
        bottom_index = 0
        for kk, bb in enumerate(bb_r):
            y_place = (bb[2][1] + bb[3][1]) / 2
            if y_place > y_max:
                y_max = y_place
                bottom_index = kk
        bottom_box = bb_r[bottom_index]

        heights = []
        for b in bb_r:
            heights.append(b[3][1] - b[0][1])
        hm = np.array(heights).mean()

        i = 0
        add_flag = True
        while i < 5:
            if not add_flag:
                break
            add_flag = False
            for bl4 in boxs_list_4p:
                if abs(bl4[0][0] - bottom_box[0][0]) < 2 and abs(bl4[0][1] - bottom_box[0][1]) < 2:
                    continue
                if bl4[0][0] > bottom_box[1][0] or bl4[1][0] < bottom_box[0][0]:
                    continue

                if bl4[0][1] > bottom_box[0][1] and bl4[0][1] - bottom_box[3][1] < 0.75 * hm:
                    if tuoshi_flag and (bottom_box[1][0] - bottom_box[0][0]) < (
                            bl4[1][0] - bl4[0][0]) * 0.5:  # 拖式计算下一行比上一行长，来进行一定的限制
                        continue
                    i += 1
                    add_flag = True
                    bottom_box = bl4
                    bb_r.append(bottom_box)
                    break
        return bb_r
        
    @staticmethod
    def compare_fraction_and_decimal(fraction: str, decimal:str):
        def parse_fraction(fraction_str: str) -> Fraction:
            # 匹配 LaTeX 分数格式（支持负号）
            latex_match = re.match(r"^(-?)\\frac\{(\d+)\}\{(\d+)\}$", fraction_str)
            if latex_match:
                sign = "-" if latex_match.group(1) else ""
                numerator = int(latex_match.group(2))
                denominator = int(latex_match.group(3))
                if denominator == 0:
                    raise ZeroDivisionError
                return Fraction(f"{sign}{numerator}/{denominator}")
    
            # 常规分数格式（如 -3/4）
            if "/" in fraction_str:
                return Fraction(fraction_str)
    
            # 整数格式（如 -5）
            return Fraction(int(fraction_str))

        try:
            # 解析分数和十进制数
            frac = parse_fraction(fraction)
            dec = Fraction(decimal) if "." in decimal else Fraction(int(decimal))
            return frac == dec
        except (ValueError, ZeroDivisionError):
            return False
     
    @staticmethod    
    def compare_latex_fraction_decimal(fraction_str, decimal_str):
        try:
            if '\\frac' in fraction_str:
                match = re.match(r'\\frac\{(\d+)\}\{(\d+)\}', fraction_str)
                if not match:
                    return False
                numerator = int(match.group(1))
                denominator = int(match.group(2))
            else:
                parts = fraction_str.split('/')
                if len(parts) != 2:
                    return False
                numerator = int(parts[0])
                denominator = int(parts[1])
    
            fraction_value = numerator / denominator
    
            decimal_value = float(decimal_str)
    
            # 比较两者是否相等(使用小误差范围处理浮点数精度问题)
            epsilon = 1e-10  # 允许的误差范围
            if abs(fraction_value - decimal_value) < epsilon:
                return True
            else:
                return False
    
        except ValueError as e:
            return False
        except ZeroDivisionError:
            return False
    
    @staticmethod
    def convert_frac_to_fraction(expr: str):
        match_with_whole = re.match(r'=(\d+)\\frac\{(\d+)\}\{(\d+)\}', expr)
        if match_with_whole:
            whole = int(match_with_whole.group(1))
            numerator = int(match_with_whole.group(2))
            denominator = int(match_with_whole.group(3))
            return "=" + "\\frac" + "{" + str(whole * denominator + numerator) + "}" + "{" + str(denominator) + "}"

        match_simple = re.match(r'=\\frac\{(\d+)\}\{(\d+)\}', expr)
        if match_simple:
            return "=" + "\\frac" + "{" + str(match_simple.group(1)) + "}" + "{" + str(match_simple.group(2)) + "}"

        var_match = re.match(r'^([a-zA-Z]+)=', expr)
        if var_match:
            var_name = var_match.group(1)
            math_part = expr[len(var_name) + 1:]
            latex_match = re.match(r'(\d+)\\frac\{(\d+)\}\{(\d+)\}', math_part)
            if latex_match:
                whole = int(latex_match.group(1))
                num = int(latex_match.group(2))
                den = int(latex_match.group(3))
                return str(var_name) + "=" + "\\frac" + "{" + str(whole * den + num) + "}" + "{" + str(den) + "}"
            else:
                return expr

        else:
            return expr
                
    @staticmethod
    def compare_math_expressions(expr1, expr2):
        def parse_expression(expr):
            # 处理带负号的混合数（如 -1\frac{2}{3}）
            mixed_frac = re.match(r'^(-?\d+)\\frac\{(\d+)\}\{(\d+)\}$', expr)
            if mixed_frac:
                whole_str, num_str, den_str = mixed_frac.groups()
                whole = int(whole_str)
                num = int(num_str)
                den = int(den_str)
                
                if den == 0:
                    raise ValueError("分母不能为零")
                
                # 计算逻辑：符号作用于整个数
                abs_whole = abs(whole)
                sign = -1 if whole < 0 else 1
                numerator = abs_whole * den + num
                return Fraction(sign * numerator, den)
    
            # 处理带负号的标准分数（如 -\frac{5}{6}）
            std_frac = re.match(r'^(-?)\\frac\{(\d+)\}\{(\d+)\}$', expr)
            if std_frac:
                sign_str, num_str, den_str = std_frac.groups()
                sign = -1 if sign_str else 1
                num = int(num_str)
                den = int(den_str)
                
                if den == 0:
                    raise ValueError("分母不能为零")
                return Fraction(sign * num, den)
    
            # 处理带负号的整数（如 -5）
            integer_match = re.match(r'^(-?\d+)$', expr)
            if integer_match:
                return Fraction(int(integer_match.group(1)), 1)
    
            raise ValueError(f"无法解析的表达式: {expr}")
    
        try:
            return parse_expression(expr1) == parse_expression(expr2)
        except (ValueError, ZeroDivisionError):
            return False
    
    @staticmethod
    def is_fraction_reduced(fraction_str):
        import math
        """
        判断分数是否已约分
        例如：\frac{2}{4} -> 未约分；\frac{3}{10} -> 已约分
        """
        # 正则匹配提取分子分母
        match = re.match(r"\\frac\{(\d+)\}\{(\d+)\}", fraction_str)
        if match:
            numerator = int(match.group(1))   # 分子
            denominator = int(match.group(2)) # 分母
            
            # 检查分母为零
            if denominator == 0:
                raise ValueError("分母不能为零")
            
            # 计算最大公约数（GCD）
            gcd = math.gcd(numerator, denominator)
            
            # 判断是否已约分（GCD=1表示已约分）
            return gcd == 1