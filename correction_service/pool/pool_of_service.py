# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> yanhui
# @Date         ：2024/6/4 17:14

import threading
from queue import Queue

from correction_service.service.service_of_ocr import OcrService
from correction_service.service.service_of_page import PageService
from correction_service.service.service_of_book import BookService
from correction_service.service.service_of_answer import AnswerService
from correction_service.service.service_of_embedding import EmbeddingService
from correction_service.service.service_of_correction import CorrectionService
from correction_service.service.service_of_search_page import SearchPageService
from correction_service.service.service_of_answer_detect import AnswerDetectService
from correction_service.service.service_of_internal_answer_detect import IAnswerDetectService

from base_common import LoggerFactory
from base_common.service.service_of_direction import DirectionService
from base_common.service.service_of_detection import DetectionService
log = LoggerFactory.get_logger('ServicePool')

class _PrivateServicePool:
    def __init__(self):
        self.__service_pool: Queue = None
        self.__pool_lock = threading.Lock()

    def __create_service(self):
        page_service = PageService()
        book_service = BookService()
        answer_service = AnswerService()
        ocr_service = OcrService()
        detection_service = DetectionService()
        direction_service = DirectionService()
        embedding_service = EmbeddingService()
        search_page_service = SearchPageService()
        correction_service = CorrectionService()
        answer_detect_service = AnswerDetectService()
        internal_answer_detect_service = IAnswerDetectService()

        answer_detect_service.autowired(answer_service, ocr_service, internal_answer_detect_service)
        search_page_service.autowired(detection_service, embedding_service, ocr_service, direction_service)
        book_service.autowired(page_service, answer_service, search_page_service)
        correction_service.autowired(book_service, answer_service, ocr_service, answer_detect_service)
        return correction_service

    def initialize(self, max_size):
        self.__service_pool = Queue(max_size)
        for i in range(max_size):
            self.__service_pool.put(self.__create_service())

    def get_service(self):
        with self.__pool_lock:
            if not self.__service_pool.empty():
                return False, self.__service_pool.get()

        log.warn("****************************************************")
        log.warn(f"服务器资源不足，启用临时服务")
        log.warn("****************************************************")
        return True, self.__create_service()

    def release_service(self, service):
        if not self.__service_pool.full():
            self.__service_pool.put(service)

class ServicePool:
    @classmethod
    def initialize(cls, max_size=2):
        #log.info("服务对象池正在初始化...")
        cls.__service_pool = _PrivateServicePool()
        cls.__service_pool.initialize(max_size)
        #log.info("服务对象池已初始化")

    @classmethod
    def get_service(cls) -> CorrectionService:
        return cls.__service_pool.get_service()

    @classmethod
    def release_service(cls, service: CorrectionService):
        cls.__service_pool.release_service(service)
