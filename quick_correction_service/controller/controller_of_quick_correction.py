# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# <AUTHOR> hanlin
# @Date         ：2024/07/16 11:54
# @Description  : __init__.py
import json
import traceback
import uuid
import asyncio
import tornado.web

from base_common import ThreadExecutor, LoggerFactory, Context
from quick_correction_service.pool import ServicePool

log = LoggerFactory.get_logger('QuickCorrectionController')

class QuickCorrectionController(tornado.web.RequestHandler):
    thread_executor = None  # 用于存储线程池，每个子进程创建一个新的线程池
    @classmethod
    def initialize_executor(cls, threads_number):
        if cls.thread_executor is None:
            cls.thread_executor = ThreadExecutor(threads_number)

    async def post(self):
        release, service = ServicePool.get_service()
        try:
            mission_id = str(uuid.uuid4()).replace('-', '').upper()
            req_data = json.loads(self.request.body)
            req_data.update({'mission_id': mission_id})
            Context.correct_start(mission_id, req_data.get('create_date', None))

            log.info(f"(mission_id: {mission_id})(record_id:{req_data['recordId']}) 任务开始 批改请求参数: {json.dumps(req_data)}")
            loop = asyncio.get_event_loop()
            resp = await loop.run_in_executor(QuickCorrectionController.thread_executor,
                                              service.do_correction, req_data)
            record, record1 = Context.correct_end(mission_id, resp)
            if record:
                log.info(record)
            if record1:
                log.info(record1)
            if resp.get('img_path', None):
                resp.pop('img_path')
            self.write(json.dumps(resp, ensure_ascii=False))
        except:
            log.error(f"批改错误{traceback.format_exc()}")
        finally:
            if release:
                del service
            else:
                ServicePool.release_service(service)

