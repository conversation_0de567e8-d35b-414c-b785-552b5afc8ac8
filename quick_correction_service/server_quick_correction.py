# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# <AUTHOR> hanlin
# @Date         ：2024/07/16 15:10
# @Description  : service_quick_correction.py
import tornado.web

from controller import QuickCorrectionController
from base_common import ServerWrapper, Context, RedisRequestor, OssUtil, Constants, LoggerFactory
from quick_correction_service.pool import ServicePool

log = LoggerFactory.get_logger('QuickCorrectionController')
def make_app():
    handlers = [(r'/auto_correct', QuickCorrectionController)]
    QuickCorrectionController.initialize_executor(4)
    return tornado.web.Application(handlers)

def setup():
    Context.setup(True)
    OssUtil.load_oss_config()
    Constants.setup(Context.is_product())
    ServicePool.initialize(Constants.QUICK_CORRECTION_MISSION_NUMBER)
    RedisRequestor.init_producers(False)

if __name__ == '__main__':
    server_warapper = ServerWrapper(log, '智能批改主程序', 8876)
    log.info(f"智能批改主程序监听http://0.0.0.0:8876/auto_correct")
    server_warapper.start_server(make_app, setup)
