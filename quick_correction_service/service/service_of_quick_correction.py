import asyncio
import json
import time
import traceback

import cv2

from base_common import (
    AutoCorrectErrorNum,
    Constants,
    Context,
    FileUtil,
    ImageUtil,
    LoggerFactory,
    OssUtil,
)
from base_common.mission_mode import <PERSON><PERSON>lisa
from base_common.service.service_of_detection import DetectionService
from base_common.service.service_of_direction import DirectionService
from base_common.util.util_of_detection_cpu import ImageProcessorCpu

from .service_of_answer_detect import QuickAnswerDetectService

log = LoggerFactory.get_logger("QuickCorrectionService")


class QuickCorrectionService:
    def __init__(self):
        self._answer_detect_ser = QuickAnswerDetectService()
        self._detection_ser = DetectionService()
        self._direction_ser = DirectionService()

    def _crop_and_rotate(self, mission_id, user_image):
        t0 = time.time()
        img_key, img_type, img_user = self._detection_ser.do_detection(
            mission_id, user_image
        )
        if img_user is None:
            return -1, None, None, None
        # img_user = cv2.cvtColor(img_user, cv2.COLOR_BGR2RGB)
        Context.report_cost(
            mission_id, (MissionAlisa.DETECTION_SERVICE, time.time() - t0)
        )
        t0 = time.time()
        ratio = ImageProcessorCpu.is_pure_black_or_white(img_user)
        if ratio >= 0.59:
            return -2, None, None, None
        height, width, _ = img_user.shape
        # 计算图像的宽高比
        aspect_ratio = min(height, width) / max(height, width)
        threshold_ratio = 0.25
        # 根据宽高比拦截图像
        if aspect_ratio <= threshold_ratio:
            return -3, None, None, None
        img_key, img_type, img_user, _ = self._direction_ser.do_direction(
            mission_id, img_user, img_key, img_type
        )
        Context.report_cost(
            mission_id, (MissionAlisa.DIRECTION_SERVICE, time.time() - t0)
        )
        return 0, img_key, img_user, img_type

    def do_correction(self, data_json):
        try:
            mission_id = data_json["mission_id"]
            log.info(f"(mission_id: {mission_id}) 开始批改 {json.dumps(data_json)}")
            record_id = data_json["recordId"]
            img_url = data_json["originImageUrl"]
            env = data_json["env"]

            orig_image_path = f'{Constants.TEMP_PATH}/{record_id}_{int(time.time() * 1000)}.jpg'
            success = ImageUtil.fetch_user_image(img_url, orig_image_path)
            if not success:
                log.error(f"(mission_id: {mission_id}) download {img_url} failed.")
                return {
                    "message": str(AutoCorrectErrorNum.NO_PDF_INVALID_USER_IMAGE),
                    "originImageUrl": data_json["originImageUrl"],
                    "errorCode": 30021,
                }
            user_image = cv2.imread(orig_image_path, cv2.IMREAD_COLOR)
            # user_image = numpy.array(img)
            ret_code, img_key, img_user, img_type = self._crop_and_rotate(
                mission_id, user_image
            )
            if ret_code == -1:
                log.error(f"user image not valid {img_url} judge by detection model.")
                return {
                    "message": str(AutoCorrectErrorNum.NO_PDF_INVALID_USER_IMAGE),
                    "originImageUrl": data_json["originImageUrl"],
                    "errorCode": 30021,
                }
            elif ret_code == -2:
                log.error(
                    f"(mission_id: {mission_id}) user image not valid {img_url} judge by is_pure_black_or_white."
                )
                return {
                    "message": str(AutoCorrectErrorNum.NO_PDF_INVALID_USER_IMAGE),
                    "originImageUrl": data_json["originImageUrl"],
                    "errorCode": 30021,
                }
            elif ret_code == -3:
                log.error(
                    f"(mission_id: {mission_id}) Width-to-height ratio is too low, {img_url}"
                )
                return {
                    "message": str(AutoCorrectErrorNum.NO_PDF_INVALID_USER_IMAGE),
                    "originImageUrl": data_json["originImageUrl"],
                    "errorCode": 30021,
                }

            r_name = "crop_and_rotate_tmp_%s_%d" % (record_id, time.time())
            savepath = f"{Constants.TEMP_PATH}/{r_name}.jpg"
            cv2.imwrite(savepath, img_user)
            alignurl = OssUtil.upload(
                savepath, f"piccorrect/{env}/alignimages/{r_name}.jpg"
            )
            FileUtil.remove_file(savepath)
            if Constants.SAVE_VIS:
                ImageUtil.merge_and_save(
                    f"{Constants.QUICK_CORRECTION_PATH}/{record_id}_{str(time.time())}.jpg",
                    user_image,
                    img_user,
                )
            log.info(f"(mission_id: {mission_id}) 校正后图像url：{alignurl}")
            try:
                ans_req_data = {
                    "recordId": record_id,
                    "originImageUrl": data_json["originImageUrl"],
                    "alignUrl": alignurl,
                    "mission_id": mission_id,
                }
                log.info(
                    f"(mission_id: {mission_id}) 答案检查请求：{json.dumps(ans_req_data)}"
                )
                resp_dict = asyncio.run(
                    self._answer_detect_ser.do_correction(
                        ans_req_data, img_user, img_key, img_type
                    )
                )

                if resp_dict is None:
                    log.error(
                        f"message: {str(AutoCorrectErrorNum.ACCESS_AUTO_CORRECTION_ALGO_API_FAILED)}, resp as None"
                    )
                    return {
                        "message": str(
                            AutoCorrectErrorNum.ACCESS_AUTO_CORRECTION_ALGO_API_FAILED
                        ),
                        "originImageUrl": data_json["originImageUrl"],
                        "errorCode": 30012,
                    }

                log.info(
                    f"(mission_id: {mission_id}) 答案检查响应： {json.dumps(resp_dict, ensure_ascii=False)}"
                )
                if "items" in resp_dict.keys() and 0 == len(resp_dict["items"]):
                    return {
                        "message": str(AutoCorrectErrorNum.NO_ANY_ANSWER_ITEM_DETECT),
                        "originImageUrl": data_json["originImageUrl"],
                        "errorCode": 30001,
                    }
                return resp_dict
            except:
                log.error(
                    f"message: {AutoCorrectErrorNum.ACCESS_AUTO_CORRECTION_ALGO_API_OTHER_EXCEPTION}, due to {traceback.format_exc()}"
                )
                return {
                    "message": str(
                        AutoCorrectErrorNum.ACCESS_AUTO_CORRECTION_ALGO_API_OTHER_EXCEPTION
                    ),
                    "originImageUrl": data_json["originImageUrl"],
                    "errorCode": 30011,
                }
        finally:
            FileUtil.remove_file(orig_image_path)
