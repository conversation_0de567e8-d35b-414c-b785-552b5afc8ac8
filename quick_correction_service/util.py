import os
import random

import cv2
import numpy as np
from asteval import Interpreter

from quick_calc_rec_service.model.util import Draw<PERSON>hine<PERSON>, replace_percent

aeval = Interpreter()


def calc_iou(bbox1, bbox2):
    if not isinstance(bbox1, np.ndarray):
        bbox1 = np.array(bbox1)
    if not isinstance(bbox2, np.ndarray):
        bbox2 = np.array(bbox2)
    (
        xmin1,
        ymin1,
        xmax1,
        ymax1,
    ) = np.split(bbox1, 4, axis=-1)
    (
        xmin2,
        ymin2,
        xmax2,
        ymax2,
    ) = np.split(bbox2, 4, axis=-1)

    area1 = (xmax1 - xmin1) * (ymax1 - ymin1)
    area2 = (xmax2 - xmin2) * (ymax2 - ymin2)

    ymin = np.maximum(ymin1, np.squeeze(ymin2, axis=-1))
    xmin = np.maximum(xmin1, np.squeeze(xmin2, axis=-1))
    ymax = np.minimum(ymax1, np.squeeze(ymax2, axis=-1))
    xmax = np.minimum(xmax1, np.squeeze(xmax2, axis=-1))

    h = np.maximum(ymax - ymin, 0)
    w = np.maximum(xmax - xmin, 0)
    intersect = h * w

    iou_1 = intersect / area1
    iou_2 = intersect / area2.reshape(1, -1)
    ious_ = np.stack((iou_1, iou_2), 0)
    ious = ious_.max(0)
    return ious


def judge_answer_blur(answer, pred):
    if (
        pred.find("+") != -1
        or pred.find("-") != -1
        or pred.find("*") != -1
        or pred.find("/") != -1
    ):
        return False

    if isinstance(pred, str):
        pred = pred.replace("\\%", "*0.01")
    if isinstance(answer, str) and answer.find("frac") != -1:
        try:
            fraud, top, bottom = answer.split("{")
            if len(fraud) > 5:
                answer = float(fraud[:-5]) + float(top[:-1]) / float(bottom[:-1])
            else:
                answer = float(top[:-1]) / float(bottom[:-1])
        except:
            pass
    if isinstance(pred, str) and pred.find("frac") != -1:
        try:
            fraud, top, bottom = pred.split("{")
            if len(fraud) > 5:
                pred = float(fraud[:-5]) + float(top[:-1]) / float(bottom[:-1])
            else:
                pred = float(top[:-1]) / float(bottom[:-1])
        except:
            pass

    if isinstance(answer, int) or isinstance(answer, float):
        if isinstance(pred, int) or isinstance(pred, float):
            return abs(answer - pred) < 1e-6
        else:  # isinstance(pred,str):
            try:
                return abs(answer - aeval.eval(pred)) < 1e-6
            except:
                return False
    else:  # isinstance(answer,str)
        if isinstance(pred, int) or isinstance(pred, float):
            # 处理百分数字符串，兼容'20%'等情况
            if isinstance(answer, str) and "%" in answer:
                answer = replace_percent(answer)
            return abs(eval(answer) - pred) < 1e-6
        else:  # isinstance(pred,str):
            try:
                if answer == pred:
                    return True
                return abs(eval(answer) - aeval.eval(pred)) < 1e-6
            except:
                return False


def tmp_draw_box(img, new_item_boxs, ans_box_thrink):
    img_ = img.copy()
    for bb in new_item_boxs:
        img_ = cv2.rectangle(img_, (bb[0], bb[1]), (bb[2], bb[3]), (255, 0, 0), 1)
    img_ = cv2.rectangle(
        img_,
        (int(ans_box_thrink[0]), int(ans_box_thrink[1])),
        (int(ans_box_thrink[2]), int(ans_box_thrink[3])),
        (0, 0, 255),
        1,
    )
    cv2.imwrite("{}.jpg".format(random.randint(0, 100)), img_)


def get_handwrite(detect_strs, detect_boxs, box, item_boxs, img):
    # detect_strs 当前页面识别出的文本行
    # detect_boxs 当前页面识别出的文本行对应的box
    # box 目标拖式计算的框
    # item_boxs 所有检测出的口算题的框
    himg, wimg, _ = img.shape
    if len(detect_boxs) == 0:
        return [], []

    new_detect_boxs = []  # detect_boxs多边形转换成外接正矩形
    for ib in detect_boxs:
        ib = np.array(ib)
        ixmin = ib[:, 0].min()
        ixmax = ib[:, 0].max()
        iymin = ib[:, 1].min()
        iymax = ib[:, 1].max()
        new_detect_boxs.append([ixmin, iymin, ixmax, iymax])

    box = np.array(box)  # box多边形转换成外接正矩形
    """
    xmin = box[:,0].min()
    xmax = box[:,0].max()
    ymin = box[:,1].min()
    ymax = box[:,1].max()
    h = box[3][1] - box[0][1] #box的高度
    """
    xmin = box[0]
    xmax = box[2]
    ymin = box[1]
    ymax = box[3]
    h = box[3] - box[1]
    """
    new_item_boxs = []#口算题的框换成外接正矩形
    for ib in item_boxs:
        ib = np.array(ib)
        ixmin = ib[:,0].min()
        ixmax = ib[:,0].max()
        iymin = ib[:,1].min()
        iymax = ib[:,1].max()
        new_item_boxs.append([ixmin,iymin,ixmax,iymax])
    """
    new_item_boxs = np.array(item_boxs)

    ans_box_thrink = [
        max(0, xmin + 0.2 * h),
        max(0, ymin + 0.1 * h),
        min(wimg, xmax - 0.2 * h),
        min(himg, ymax + 1.2 * h),
    ]  # 如果题干附近有其他题干，默认不是拖式计算或者解方程
    ious_ = calc_iou(new_item_boxs, [ans_box_thrink])[:, 0]
    # tmp_draw_box(img,new_item_boxs,ans_box_thrink)

    if (np.array(ious_) > 0.1).sum() >= 2:
        return None, None

    ans_box = [
        max(0, xmin - 0.5 * h),
        max(0, ymax + 2),
        min(wimg, xmax),
        min(himg, ymax + 10 * h),
    ]  # 默认答案长度有10行，答题区域大小尽量取大一些，后面再来后处理限制

    ious_ = calc_iou(new_item_boxs, [ans_box])[:, 0]
    hmax = ymax
    for i_iou in range(len(ious_)):
        if ious_[i_iou] <= 0.1:
            continue
        if ans_box[3] > new_item_boxs[i_iou][1] and ymax + h < new_item_boxs[i_iou][1]:
            ans_box[3] = new_item_boxs[i_iou][1] + h * 0.2

    ious = calc_iou(new_detect_boxs, [ans_box])[:, 0]
    new_target_boxs = []
    new_target_strs = []
    for ii in range(len(ious)):
        if ious[ii] > 0.2:
            new_target_boxs.append(new_detect_boxs[ii])
            new_target_strs.append(detect_strs[ii])

    if len(new_target_boxs) == 0:
        return [], []

    target_boxs = np.array(new_target_boxs, dtype=np.int32)
    argsort = np.argsort(target_boxs[:, 1])

    target_strs = []
    target_boxs = []
    for ar in argsort:
        target_strs.append(new_target_strs[ar])
        target_boxs.append(new_target_boxs[ar])

    # 在这一步的时候限制两行手写字的距离，距离很大的时候表示下面的文本已经不是这道题的作答
    final_target_strs = []
    final_target_boxs = []

    for ii in range(len(target_strs)):
        if ii == 0:
            if target_boxs[ii][1] - ymax > 2 * h:
                return final_target_strs, [0, 0, 0, 0]

            final_target_strs.append(target_strs[ii])
            final_target_boxs.append(target_boxs[ii])
            continue
        h_gap = target_boxs[ii][1] - target_boxs[ii - 1][3]
        if h_gap > 2 * h:
            break
        final_target_strs.append(target_strs[ii])
        final_target_boxs.append(target_boxs[ii])

    final_target_boxs.append([xmin, ymin, xmax, ymax])

    final_target_boxs = np.array(final_target_boxs, dtype=np.int32)
    big_box = [
        final_target_boxs[:, 0].min(),
        final_target_boxs[:, 1].min(),
        final_target_boxs[:, 2].max(),
        final_target_boxs[:, 3].max(),
    ]

    return final_target_strs, big_box


def draw_result(img, img_name, result, save_dir=""):
    h, w, c = img.shape
    for item in result["items"]:
        box = [
            int(item["coord"][0] * w),
            int(item["coord"][1] * h),
            int(item["coord"][2] * w),
            int(item["coord"][3] * h),
        ]
        res = item["result"]
        i_type = item["itemType"]
        rec = item["rec"]
        if res == 0:  # 错误
            img = DrawChinese(
                img,
                str(res) + "  " + str(i_type) + "  " + rec["value"],
                (box[0], box[1]),
            )
            img = cv2.rectangle(img, (box[0], box[1]), (box[2], box[3]), (0, 0, 255), 1)
        else:
            img = DrawChinese(
                img,
                str(res) + "  " + str(i_type),
                (box[0], box[1]),
                fontColor=(0, 255, 0),
            )
            img = cv2.rectangle(img, (box[0], box[1]), (box[2], box[3]), (0, 255, 0), 1)

    cv2.imwrite(os.path.join(save_dir, img_name), img)


def judge_item(pred_str):
    """
    final = {
    'flag':True/False,
    'reg_answers':'',
    'std_answers':'',
    }
    """
    final = {"flag": None, "reg_answers": pred_str, "std_answers": ""}

    if pred_str.find("/") != -1:
        item0 = float(pred_str.split("/")[0])
        item1 = float((pred_str.split("/")[1]).split("S")[0])
        res = float(((pred_str.split("/")[1]).split("S")[1]).split("T")[0])
        if pred_str[-1] == "H":
            mod = 0
        else:
            mod = float(((pred_str.split("/")[1]).split("S")[1]).split("H")[-1])

        item0 = int(item0) if abs(int(item0) - item0) < 1e-6 else item0
        item1 = int(item1) if abs(int(item1) - item1) < 1e-6 else item1
        res = int(res) if abs(int(res) - res) < 1e-6 else res
        mod = int(mod) if abs(int(mod) - mod) < 1e-6 else mod

        if abs((mod + res * item1) - item0) < 1e-5:
            final["flag"] = True
            final["reg_answers"] = (
                str(item0) + "/" + str(item1) + "=" + str(res) + "……" + str(mod)
            )
            final["std_answers"] = (
                str(item0) + "/" + str(item1) + "=" + str(res) + "……" + str(mod)
            )
        else:
            res_ = round(item0 // item1, 2)
            mod_ = round(item0 % item1, 2)

            final["flag"] = False
            final["reg_answers"] = (
                str(item0) + "/" + str(item1) + "=" + str(res) + "……" + str(mod)
            )
            final["std_answers"] = (
                str(item0) + "/" + str(item1) + "=" + str(res_) + "……" + str(mod_)
            )
        return final

    items = pred_str.split("=")
    new_items = []
    for it in items:
        if (it.find("+") != -1 or it.find("-") != -1 or it.find("*") != -1) and it.find(
            "A"
        ) == -1:
            if len(it) > 0:
                new_items.append(it)
    if len(items[-1]) > 0:
        new_items.append(items[-1])
    items = new_items

    if len(items) > 3:
        return final
    if len(items) == 2:
        if abs(eval(items[0]) - eval(items[1])) < 1e-5:
            final["flag"] = True
            final["reg_answers"] = items[0] + "=" + items[1]
            final["std_answers"] = items[0] + "=" + items[1]
        else:
            final["flag"] = False
            final["reg_answers"] = items[0] + "=" + items[1]
            final["std_answers"] = items[0] + "=" + str(round(eval(items[0]), 2))
        return final
    if len(items) == 3:
        if items[1].find("+") != -1:
            title = items[0] + "+" + items[1].split("+")[-1]
            if abs(eval(title) - eval(items[2])) < 1e-5:
                final["flag"] = True
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + items[2]
            else:
                final["flag"] = False
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + str(round(eval(title), 2))
            return final

        if items[1].find("-") != -1:
            title = items[0] + "-" + items[1].split("-")[-1]
            if abs(eval(title) - eval(items[2])) < 1e-5:
                final["flag"] = True
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + items[2]
            else:
                final["flag"] = False
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + str(round(eval(title), 2))
            return final
        if items[1].find("*") != -1:
            if items[0].find("*") != -1:
                title = items[0] + "*" + items[1].split("*")[-1]
            else:
                title = "(" + items[0] + ")" + "*" + items[1].split("*")[-1]
            if abs(eval(title) - eval(items[2])) < 1e-5:
                final["flag"] = True
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + items[2]
            else:
                final["flag"] = False
                final["reg_answers"] = title + "=" + items[2]
                final["std_answers"] = title + "=" + str(round(eval(title), 2))
            return final
    return final
