# -*- coding: utf-8 -*-#
'''主要为拍照批改异步任务的获取及执行'''

import time
import json
import requests
import traceback
import concurrent.futures
import datetime
import tornado.web
import tornado.ioloop
import tornado.httpserver
from base_common import LoggerFactory, Constants

log = LoggerFactory.get_logger('quick_task')
executor = None
stop_flag = False
correction_url = 'http://127.0.0.1:8876/auto_correct'
env = 'product'
camera_correct_oral_cal = 'camera_correct_oral_cal'
if env == 'product':
    group = 'product'
    task_req_url = 'https://itechserver.bookln.cn/taskrecordservices/blockingGetTask.do'
    task_resp_url = 'https://itechserver.bookln.cn/taskrecordservices/taskSuccess.do'
else:
    group = env
    task_req_url = 'https://itechserver.bookln.cn/taskrecordservices/getTask.do'
    task_resp_url = 'https://itechserver.bookln.cn/taskrecordservices/taskSuccess.do'
def worker(num):
    global stop_flag
    while not stop_flag:
        try:
            resp = requests.post(url=task_req_url, params={'bizTypeCode': camera_correct_oral_cal, 'workGroup': group})
            if not resp:
                log.warning('访问批改任务失败！')
            else:
                data_all = resp.json().get('data', [])
                if len(data_all) > 0:
                    data_all = data_all[0]
                    ctime = data_all['gmtCreate']
                    params = json.loads(data_all['bizParams'])
                    record_id = params['recordId']
                    task_create_date = datetime.datetime.fromtimestamp(ctime / 1000.0)
                    log.info(f"(record_id:{record_id}) 异步任务创建于: {task_create_date}, 任务参数 {params}")
                    params.update({'create_date': ctime / 1000.0})
                    origImageUrlName = "originImageUrl"
                    origImageUrlValue = params[origImageUrlName]
                    if (time.time() - ctime / 1000) > 30 and env == 'product':
                        params.update({'errorCode': 30011})
                        params.update({origImageUrlName: origImageUrlValue})
                        message = {'bizTypeCode': camera_correct_oral_cal, 'result': json.dumps(params), 'id': data_all['id']}
                        log.warn(f"(record_id:{record_id}) 任务已经超过30秒，不进行批改。响应30011")
                    else:
                        try:
                            log.info(f"(record_id:{record_id}) 请求批改主程序...")
                            start_time = time.time()
                            resp_pg = requests.post(url=correction_url, data=json.dumps(params), timeout=30)
                            log.info(f"(record_id:{record_id}) 主程序处理完成，耗时： {'%.2f seconds.' % (time.time() - start_time)}")
                            if resp_pg.status_code == 200:
                                message = {'bizTypeCode': camera_correct_oral_cal, 'result': resp_pg.text, 'id': data_all['id']}
                                log.info(f"(record_id:{record_id}) 批改主程序返回码是200, 响应结果为：{resp_pg.text}")
                            else:
                                log.warn(f"(record_id:{record_id}) 批改主程序返回码是{resp_pg.status_code}，响应30011")
                                params.update({'errorCode': 30011})
                                params.update({origImageUrlName: origImageUrlValue})
                                message = {'bizTypeCode': camera_correct_oral_cal, 'result': json.dumps(params),
                                           'id': data_all['id']}
                        except:
                            log.error(f'(record_id:{record_id}) 任务处理异常 \n{traceback.format_exc()}')
                            params.update({'errorCode': 30011})
                            params.update({origImageUrlName: origImageUrlValue})
                            message = {'bizTypeCode': camera_correct_oral_cal, 'result': json.dumps(params), 'id': data_all['id']}
                    task_finish_date = datetime.datetime.fromtimestamp(time.time())
                    log.info(f"(record_id:{record_id}) 异步任务结束于：{task_finish_date}")
                    log.info(f"(record_id:{record_id}) 将批改结果推送到异步任务池,任务结果 {json.dumps(message, ensure_ascii=False)}")
                    resp = requests.post(url=task_resp_url, data=message)
                    log.info(f"(record_id:{record_id}) 推送批改结果结束, 服务器响应：{resp.status_code} {resp.text}")
                else:
                    time.sleep(0.1)
        except BaseException:
            log.error(f'异步任务线程{num}异常，\n{traceback.format_exc()}')
    log.info(f'取任务线程{num}停止.')
    return None

class StopHandler(tornado.web.RequestHandler):
    def get(self):
        global stop_flag
        stop_flag = True
        log.info(f'停止获取异步任务')
        executor.shutdown(wait=True)
        self.write('task stoped.')

    def post(self):
        global stop_flag
        stop_flag = True
        log.info(f'停止获取异步任务')
        executor.shutdown(wait=True)
        self.write('task stoped.')


if __name__ == '__main__':
    log.info(f"server task process start with args env:{env}")
    # 创建一个包含threads_num个线程的线程池
    executor = concurrent.futures.ThreadPoolExecutor(max_workers=Constants.QUICK_CORRECTION_MISSION_NUMBER)
    # 提交任务到线程池
    futures = [executor.submit(worker, i) for i in range(Constants.QUICK_CORRECTION_MISSION_NUMBER)]

    app = tornado.web.Application([(r"/stop", StopHandler)])
    server = tornado.httpserver.HTTPServer(app)
    server.listen(18876)
    log.info(F"Tornado server started on port 18876")

    try:
        tornado.ioloop.IOLoop.current().start()
    except KeyboardInterrupt:
        log.info("Stopping server...")
        stop_flag = True
        executor.shutdown(wait=True)
        log.info("Server stopped and all tasks completed.")
