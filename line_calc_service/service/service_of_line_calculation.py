# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_vertical_calculation.py

import traceback

import cv2
from PIL import Image

from base_common.service.service_of_base import BaseModelService
from line_calc_service.model.line_interface import Line
from base_common import LoggerFactory, Constants, ImageUtil, MissionMode

log = LoggerFactory.get_logger('LineCalculationService')

class LineCalculationService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.LINE_CALC_MISSION
        model_path = f'{Constants.MODEL_WEIGHT_PATH}/line_calc_service/640.pth'
        self.line = Line(model_path, vis_line=Constants.SAVE_VIS)

    def do_post(self, data_json):
        try:
            img_name = data_json['img_name']
            img_photo_cv2 = self.get_image(data_json)
            img_photo = Image.fromarray(cv2.cvtColor(img_photo_cv2, cv2.COLOR_BGR2RGB))
            data_json['image'] = img_photo
            line_result = self.line.run(data_json, img_name)
            return line_result
        except:
            log.error(f"error in judge connection item!, due to {traceback.format_exc()}")
        return {'boxs':{},'types':{}}