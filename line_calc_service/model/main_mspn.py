from tqdm import tqdm
import network
import utils
import os
import shutil
import random
import argparse
import numpy as np
import time
from torch.utils import data
from datasets import Line_datasets
from utils import ext_transforms as et
from utils.loss import EmbeddingLoss
import torchvision.transforms as trans
from network.mspn import MSPN
# from metrics import StreamSegMetrics

import torch
import torch.nn as nn
from utils.visualizer import Visualizer

from PIL import Image
import matplotlib
import matplotlib.pyplot as plt
import cv2

def get_lr(optimizer):
    for param_group in optimizer.param_groups:
        return param_group['lr']
    
def ohem_loss(loss_map,labels):
    pos_mask = labels>0.1
    pos_num = pos_mask.sum()
#     print('pos_num',pos_num)
    pos_loss_sum = (loss_map[pos_mask]).sum()
    
    neg_mask = labels<0.01
    neg_values = loss_map[neg_mask].view(-1)
    neg_num = min(len(neg_values),pos_num.item()*1)
    
    neg_values_des,_ = torch.sort(neg_values, descending=True, dim=-1)
    neg_loss_sum = neg_values_des[:neg_num].sum()

#     neg_loss_sum = neg_values[random.sample(range(len(neg_values)), neg_num)].sum()
    
    return pos_loss_sum/pos_num+neg_loss_sum/neg_num
#     return pos_loss_sum+neg_loss_sum
    

def get_argparser():
    parser = argparse.ArgumentParser()
    # Deeplab Options
    parser.add_argument("--train_txt", type=str, default='/home/<USER>/data/detect/answer_detect/line/train.txt')
    parser.add_argument("--val_txt", type=str, default='/home/<USER>/data/answer_detect/lines/test_lines_yolo4_v0.txt')
    parser.add_argument("--separable_conv", action='store_true', default=False,
                        help="apply separable conv to decoder and aspp")
    parser.add_argument("--output_stride", type=int, default=8, choices=[4,8])

    # Train Options
    parser.add_argument("--test_only", action='store_true', default=False)
    parser.add_argument("--save_interval", type=int, default=10000,
                        help="save segmentation results to \"./results\"")
    parser.add_argument("--total_itrs", type=int, default=30e3,
                        help="epoch number (default: 30k)")
    parser.add_argument("--lr", type=float, default=0.1,
                        help="learning rate (default: 0.1)")
    parser.add_argument("--lr_policy", type=str, default='step', choices=['poly', 'step'],
                        help="learning rate scheduler policy")
    parser.add_argument("--step_size", type=int, default=10000)
    parser.add_argument("--crop_val", action='store_true', default=False,
                        help='crop validation (default: False)')
    parser.add_argument("--batch_size", type=int, default=6,
                        help='batch size (default: 16)')
    parser.add_argument("--val_batch_size", type=int, default=4,
                        help='batch size for validation (default: 4)')
    parser.add_argument("--crop_size", type=int, default=(448,448))#int or (h,w)
    
    parser.add_argument("--ckpt", default='', type=str,
                        help="restore from checkpoint")
    parser.add_argument("--save_ckpt_dir", default='checkpoints_templess_mspn_448_v5', type=str,
                        help="restore from checkpoint")
    parser.add_argument("--continue_training", action='store_true', default=False)

    parser.add_argument("--loss_type", type=str, default='cross_entropy',
                        choices=['cross_entropy', 'focal_loss'], help="loss type (default: False)")
    parser.add_argument("--gpu_id", type=str, default='0',
                        help="GPU ID")
    parser.add_argument("--weight_decay", type=float, default=1e-4,
                        help='weight decay (default: 1e-4)')
    parser.add_argument("--random_seed", type=int, default=1,
                        help="random seed (default: 1)")
    parser.add_argument("--print_interval", type=int, default=10,
                        help="print interval of loss (default: 10)")
    parser.add_argument("--val_interval", type=int, default=100,
                        help="epoch interval for eval (default: 100)")
    parser.add_argument("--download", action='store_true', default=False,
                        help="download datasets")

    return parser


def get_dataloader(opts):
    """ Dataset And Augmentation
    """
    train_transform = et.ExtCompose([
#         et.ExtAspectResize(size=(opts.crop_size,opts.crop_size)),
#         et.ExtResize(size=(opts.crop_size,opts.crop_size)),
        # et.ExtRandomScale((0.5, 2.0)),
        # et.ExtRandomCrop(size=(opts.crop_size, opts.crop_size), pad_if_needed=True),
        et.ExtRotate(),
        et.ExtRandomHorizontalFlip(),
        et.ExtRandomVerticalFlip(),
        et.ExtToTensor(),
        et.ExtNormalize(mean=[0.485, 0.456, 0.406],
                        std=[0.229, 0.224, 0.225]),
    ])

    train_dst = Line_datasets(opts,opts.train_txt,transform=train_transform)
    train_loader = data.DataLoader(train_dst, batch_size=opts.batch_size, shuffle=True, num_workers=opts.batch_size,drop_last=True)
        
    val_transform = et.ExtCompose([
        et.ExtAspectResize(size=opts.crop_size),
        et.ExtToTensor(),
        et.ExtNormalize(mean=[0.485, 0.456, 0.406],
                        std=[0.229, 0.224, 0.225]),
    ])
    val_dst = Line_datasets(opts,opts.val_txt,transform=val_transform)
    val_loader = data.DataLoader(val_dst, batch_size=1, shuffle=False, num_workers=4,drop_last=True)
    print("Train set: %d, Val set: %d" %(len(train_dst), len(val_dst)))
    return train_loader, val_loader


def validate(opts, model, loader, device):
    """Do validation and return specified samples"""

    with torch.no_grad():
        for i, (images, labels) in tqdm(enumerate(loader)):
            
            images = images.to(device, dtype=torch.float32)
            labels = labels.to(device, dtype=torch.long)

            outputs = model(images)
            preds = outputs.detach().max(dim=1)[1].cpu().numpy()
            targets = labels.cpu().numpy()

            metrics.update(targets, preds)
            if ret_samples_ids is not None and i in ret_samples_ids:  # get vis samples
                ret_samples.append(
                    (images[0].detach().cpu().numpy(), targets[0], preds[0]))

            if opts.save_val_results:
                for i in range(len(images)):
                    image = images[i].detach().cpu().numpy()
                    target = targets[i]
                    pred = preds[i]

                    image = (denorm(image) * 255).transpose(1, 2, 0).astype(np.uint8)
                    target = loader.dataset.decode_target(target).astype(np.uint8)
                    pred = loader.dataset.decode_target(pred).astype(np.uint8)

                    Image.fromarray(image).save('results/%d_image.png' % img_id)
                    Image.fromarray(target).save('results/%d_target.png' % img_id)
                    Image.fromarray(pred).save('results/%d_pred.png' % img_id)

                    fig = plt.figure()
                    plt.imshow(image)
                    plt.axis('off')
                    plt.imshow(pred, alpha=0.7)
                    ax = plt.gca()
                    ax.xaxis.set_major_locator(matplotlib.ticker.NullLocator())
                    ax.yaxis.set_major_locator(matplotlib.ticker.NullLocator())
                    plt.savefig('results/%d_overlay.png' % img_id, bbox_inches='tight', pad_inches=0)
                    plt.close()
                    img_id += 1

        score = metrics.get_results()
    return score, ret_samples


def main():
    opts = get_argparser().parse_args()

    os.environ['CUDA_VISIBLE_DEVICES'] = opts.gpu_id
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
#     device = 'cpu'
    print("Device: %s" % device)

    # Setup random seed
    torch.manual_seed(opts.random_seed)
    np.random.seed(opts.random_seed)
    random.seed(opts.random_seed)
    
    train_loader, val_loader = get_dataloader(opts)
    
    # Set up model
    model = MSPN(output_shape=(opts.crop_size[0]//4,opts.crop_size[1]//4))
    
#     print(model)
    if opts.separable_conv and 'plus' in opts.model:
        network.convert_to_separable_conv(model.classifier)
        
    utils.set_bn_momentum(model.stage0, momentum=0.01)
    utils.set_bn_momentum(model.stage1, momentum=0.01)
    
#     input_data = torch.randn((2,3,160,160))
#     output = model(input_data)
#     print(output.size())
    
    vis_dir = './train_vis/'
    if os.path.exists(vis_dir):
        shutil.rmtree(vis_dir)
    os.makedirs(vis_dir)

    # Set up optimizer
    optimizer = torch.optim.SGD(params=[
        {'params': model.parameters(), 'lr': opts.lr},
    ], lr=opts.lr, momentum=0.9, weight_decay=opts.weight_decay)
    #optimizer = torch.optim.SGD(params=model.parameters(), lr=opts.lr, momentum=0.9, weight_decay=opts.weight_decay)
    #torch.optim.lr_scheduler.StepLR(optimizer, step_size=opts.lr_decay_step, gamma=opts.lr_decay_factor)
    if opts.lr_policy=='poly':
        scheduler = utils.PolyLR(optimizer, opts.total_itrs, power=0.9)
    elif opts.lr_policy=='step':
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=opts.step_size, gamma=0.1)

    # Set up criterion
    #criterion = utils.get_loss(opts.loss_type)
    criterion = nn.MSELoss(reduction='none').cuda()
    criterion_emb = EmbeddingLoss()

    def save_ckpt(path):
        """ save current model
        """
        torch.save({
            "cur_itrs": cur_itrs,
            "model_state": model.state_dict(),
            "optimizer_state": optimizer.state_dict(),
            "scheduler_state": scheduler.state_dict(),
            "best_score": best_score,
        }, path)
        print("Model saved as %s" % path)
    
    
    utils.mkdir(opts.save_ckpt_dir)
    # Restore
    best_score = 0.0
    cur_itrs = 0
    cur_epochs = 0
    if opts.ckpt is not None and os.path.isfile(opts.ckpt):
        # https://github.com/VainF/DeepLabV3Plus-Pytorch/issues/8#issuecomment-605601402, @PytaichukBohdan
        checkpoint = torch.load(opts.ckpt, map_location=torch.device('cpu'))
        model.load_state_dict(checkpoint["model_state"],strict=True)
#         model = nn.DataParallel(model)
        model.to(device)
        if opts.continue_training:
            optimizer.load_state_dict(checkpoint["optimizer_state"])
            scheduler.load_state_dict(checkpoint["scheduler_state"])
            cur_itrs = checkpoint["cur_itrs"]
            best_score = checkpoint['best_score']
            print("Training state restored from %s" % opts.ckpt)
        print("Model restored from %s" % opts.ckpt)
        del checkpoint  # free memory
    else:
        print("[!] Retrain")
#         model = nn.DataParallel(model)
        model.to(device)

    #==========   Train Loop   ==========#
    denorm = utils.Denormalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])  # denormalization for ori images

    if opts.test_only:
        model.eval()
        val_score, ret_samples = validate(
            opts=opts, model=model, loader=val_loader, device=device, metrics=metrics, ret_samples_ids=vis_sample_id)
        print(metrics.to_str(val_score))
        return

#     for param in model.backbone.parameters():
#         param.requires_grad = False
#     for param in model.classifier.parameters():
#         param.requires_grad = False

#    for param in model.embedding.parameters():
#        param.requires_grad = False
    
    interval_loss = 0
    interval_loss_map = 0
    interval_loss_emb = 0
    while True: #cur_itrs < opts.total_itrs:
        # =====  Train  =====
        model.train()
        cur_epochs += 1
        num_right = 0
        num_wrong = 1e-6
        for (images, labels,pairs) in train_loader:
#             print(pairs)
            n,c,h,w = images.size()
            vis_flag = random.random() < 0.05
            if vis_flag:
                image = denorm(images[0])
#                 print(image.size())
                image = (image.numpy()*255).astype(np.int32).transpose((1,2,0))
#                 label_vis = cv2.resize(label_vis,(h,w))
                flag = random.randint(0,100)
                for pair in pairs[0]:
                    if pair.sum() == 0:
                        break
                    image = cv2.circle(image,(pair[0],pair[1]),2,(0,0,255),-1)
                    image = cv2.circle(image,(pair[2],pair[3]),2,(0,0,255),-1)
                    cv2.line(image,(pair[0],pair[1]),(pair[2],pair[3]),(0,0,255),1)
                    
                cv2.imwrite(os.path.join(vis_dir,'./{}_ori.jpg'.format(flag)),image)
                
                for g in range(len(labels[0])):
                    label_vis = (labels[0][g].numpy()*255).astype(np.uint8)
                    for pair in pairs[0]:
                        if pair.sum() == 0:
                            break
                        label_vis = cv2.circle(label_vis,(pair[0],pair[1]),2,(0),-1)
                        label_vis = cv2.circle(label_vis,(pair[2],pair[3]),2,(0),-1)
                    cv2.imwrite(os.path.join(vis_dir,'./{}_heatmap_{}.jpg'.format(flag,g)),label_vis)
            
#             print(images.size(),labels.size())
            cur_itrs += 1

            images = images.to(device, dtype=torch.float32)
            labels = labels.to(device, dtype=torch.float32)
            
            optimizer.zero_grad()
            outputs,embeddings = model(images)
            loss_map = None
            loss_emb = None
            
            for i in range(len(outputs)):#stages
                outputs_i = outputs[i]
                embeddings_i = embeddings[i]
                loss_map_i = None
                for j in range(len(outputs_i)):#layers
                    outputs_i_j = outputs_i[j]
                    for k in range(len(outputs_i_j)):#example
                        outputs_i_j_k = outputs_i_j[k].squeeze()
                        outputs_i_j_k = torch.sigmoid(outputs_i_j_k)
                        loss_map_i_j_k = criterion(outputs_i_j_k, labels[k][j])
                        loss_map_i_j_k = ohem_loss(loss_map_i_j_k,labels[k][j])
                        if loss_map_i is None:
                            loss_map_i = loss_map_i_j_k*(1/(4-j))
                        else:
                            loss_map_i += loss_map_i_j_k*(1/(4-j))
#                     time_1 = time.time()        
#                     outputs_i_j = torch.sigmoid(outputs_i_j).squeeze()
#                     loss_map_i_j = criterion(outputs_i_j, labels[:,j,:,:].squeeze())
#                     loss_map_i_j = ohem_loss(loss_map_i_j,labels[:,j,:,:].squeeze())
                    
                    
#                     if loss_map_i is None:
#                         loss_map_i = loss_map_i_j*(1/(4-j))
#                     else:
#                         loss_map_i += loss_map_i_j*(1/(4-j))
                            
                    
                loss_map_i_mean = loss_map_i/len(images)
                
                _,_,h_emb,_ = embeddings_i.size()
                
                scale = h/h_emb
#                 print(images.size(),embeddings_i.size(),pairs.size(),scale)

                
                loss_emb_i,right,wrong = criterion_emb(embeddings_i,pairs,scale)

                num_right += right
                num_wrong += wrong
                
                if i == 0:
                    loss_map = loss_map_i_mean*0.5
                    loss_emb = loss_emb_i*0.5
                else:
                    loss_map += loss_map_i_mean
                    loss_emb += loss_emb_i
                    
                    
            if vis_flag:
                res = (torch.sigmoid(outputs[-1][-1][0][-1]).detach().cpu().numpy()*255).astype(np.uint8)
#                 print(res.shape)
#                 res = (outputs[0].detach().cpu().numpy()*255).astype(np.int32).transpose((1,2,0))
                cv2.imwrite(os.path.join(vis_dir,'./{}_pred.jpg'.format(flag)),res)
#             print(outputs.size())
            
            loss = loss_map*5 + loss_emb
            
            loss.backward()
            optimizer.step()
            
            
            np_loss = loss.detach().cpu().numpy()
            np_loss_map = loss_map.detach().cpu().numpy()
            np_loss_emb = loss_emb.detach().cpu().numpy()
            
            interval_loss += np_loss
            interval_loss_map += np_loss_map
            interval_loss_emb += np_loss_emb

            if (cur_itrs) % 10 == 0:
                interval_loss = interval_loss/10
                lr_now = get_lr(optimizer)
                print("Epoch %d, Itrs %d/%d, Lr=%f, Loss=%f, Loss_map=%f, Loss_emb=%f, Acc=%f" %
                      (cur_epochs, cur_itrs, opts.total_itrs,lr_now, interval_loss,interval_loss_map/10,interval_loss_emb/10,num_right/(num_right+num_wrong)))
                interval_loss = 0.
                interval_loss_map = 0.
                interval_loss_emb = 0.
                num_right = 0
                num_wrong = 1e-6
        
            if (cur_itrs) % opts.save_interval == 0:
                save_ckpt(os.path.join(opts.save_ckpt_dir,'./latest_%d.pth' %
                          (cur_itrs)))
#                 print("validation...")
#                 model.eval()
#                 val_score, ret_samples = validate(
#                     opts=opts, model=model, loader=val_loader, device=device, metrics=metrics, ret_samples_ids=vis_sample_id)
#                 print(metrics.to_str(val_score))
#                 if val_score['Mean IoU'] > best_score:  # save best model
#                     best_score = val_score['Mean IoU']
#                     save_ckpt('checkpoints/best_%s_%s_os%d.pth' %
#                               (opts.model, opts.dataset,opts.output_stride))

#                 if vis is not None:  # visualize validation score and samples
#                     vis.vis_scalar("[Val] Overall Acc", cur_itrs, val_score['Overall Acc'])
#                     vis.vis_scalar("[Val] Mean IoU", cur_itrs, val_score['Mean IoU'])
#                     vis.vis_table("[Val] Class IoU", val_score['Class IoU'])

#                     for k, (img, target, lbl) in enumerate(ret_samples):
#                         img = (denorm(img) * 255).astype(np.uint8)
#                         target = train_dst.decode_target(target).transpose(2, 0, 1).astype(np.uint8)
#                         lbl = train_dst.decode_target(lbl).transpose(2, 0, 1).astype(np.uint8)
#                         concat_img = np.concatenate((img, target, lbl), axis=2)  # concat along width
#                         vis.vis_image('Sample %d' % k, concat_img)
#                 model.train()
            scheduler.step()  
            
            if cur_itrs >=  opts.total_itrs:
                return
            
        
if __name__ == '__main__':
    main()
