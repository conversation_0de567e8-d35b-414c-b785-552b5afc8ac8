import torch
import torch.nn as nn
import torch.nn.functional as F
 
def euclidean_dist(x,y):
    m,n = x.size(0),y.size(0)
    xx = torch.pow(x,2).sum(1,keepdim=True).expand(m,n)
    yy = torch.pow(y,2).sum(dim=1,keepdim=True).expand(n,m).t()
    dist = xx + yy
    dist.addmm_(1,-2,x,y.t())
    dist = dist.clamp(min=1e-12).sqrt()
    return dist
 
def cosine_dist(x,y):
    bs1, bs2 = x.size(0),y.size(0)
    frac_up = torch.matmul(x,y.transpose(0,1))
    frac_down = (torch.sqrt( torch.pow(x,2).sum(dim=1) ).view(bs1,1).repeat(1,bs2)) * \
                (torch.sqrt( torch.pow(y,2).sum(dim=1).view(1,bs2).repeat(bs1,1) )  )
    cosine = frac_up/frac_down
    cos_d = 1 - cosine
    return cos_d
 
def _batch_hard(mat_distance,mat_similarity,indice=False):
    sorted_mat_distance, positive_indices = torch.sort(mat_distance + (-100000.0)*(1 - mat_similarity),dim=1, descending=True)
    hard_p = sorted_mat_distance[:,0]
    hard_p_indice = positive_indices[:,0]
    sorted_mat_distance, negative_indices = torch.sort( mat_distance + 100000.0 * mat_similarity,dim = 1,descending=False )
    hard_n = sorted_mat_distance[:,0]
    hard_n_indice = negative_indices[:,0]
    if(indice):
        return hard_p, hard_n, hard_p_indice, hard_n_indice
    return hard_p, hard_n
 
class TripletLoss(nn.Module):
    def __init__(self, margin=0.5, normalize_feature = True):
        super(TripletLoss,  self).__init__()
        self.margin = margin
        self.normalize_feature = normalize_feature
        self.margin_loss = nn.MarginRankingLoss(margin = margin)
 
    def forward(self, emb, label):
        if self.normalize_feature:
            emb = F.normalize(emb)
        #print('emb')
        #print(emb)
        mat_dist = euclidean_dist(emb, emb)
        #print('mat_dist')
        assert mat_dist.size(0) == mat_dist.size(1)
        N = mat_dist.size(0)
        mat_sim = label.expand(N,N).eq(label.expand(N,N).t()).float()
        #print(mat_dist)
        #print(mat_sim)
        dist_ap, dist_an = _batch_hard(mat_dist, mat_sim)
        assert dist_an.size(0) == dist_ap.size(0)
        y = torch.ones_like(dist_ap)
        loss = self.margin_loss(dist_an, dist_ap, y)
 
        prec = (dist_an.data > dist_ap.data).sum() * 1.0 / y.size(0)

    
class FocalLoss(nn.Module):
    def __init__(self, alpha=1, gamma=0, size_average=True, ignore_index=255):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.ignore_index = ignore_index
        self.size_average = size_average

    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(
            inputs, targets, reduction='none', ignore_index=self.ignore_index)
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        if self.size_average:
            return focal_loss.mean()
        else:
            return focal_loss.sum()
def tranpose_and_gather_feat(embeddings,pairs,scale=1):
    _,_,h,w = embeddings.size()
    embeddings = embeddings.permute(0, 2, 3, 1).contiguous()
    masks = pairs.sum(2)>0
    pairs = (pairs / scale).long()
    N,num,_ = pairs.size()
    
    embeddings_starts = torch.zeros(N,num,embeddings.size()[-1]).cuda()
    embeddings_ends = torch.zeros(N,num,embeddings.size()[-1]).cuda()
    for i in range(N):
        for j in range(num):
            flag=False
            if pairs[i][j][1]<0 or pairs[i][j][1]>=h:
                flag = True
            if pairs[i][j][0]<0 or pairs[i][j][0]>=w:
                flag = True
            if pairs[i][j][3]<0 or pairs[i][j][3]>=h:
                flag = True
            if pairs[i][j][2]<0 or pairs[i][j][2]>=w:
                flag = True
            if flag:
                masks[i][j] = False
                continue
            embeddings_starts[i,j,:] = embeddings[i,pairs[i][j][1],pairs[i][j][0],:]
            embeddings_ends[i,j,:] = embeddings[i,pairs[i][j][3],pairs[i][j][2],:]
    return embeddings_starts,embeddings_ends,masks
        
def adjust_pairs(pairs,heatmap):
    gap = 25
    N,H,W = heatmap.size()
    pairs_ = torch.zeros_like(pairs)
    for i in range(N):
        heatmap_i = heatmap[i].squeeze()
        pairs_i = pairs[i][pairs[i].sum(dim=1)>0]
        
        for j in range(len(pairs_i)):
            for k in range(2):
                
                pairs_i_j_x = pairs_i[j][2*k]
                pairs_i_j_y = pairs_i[j][2*k+1]
                
                x_left = gap if pairs_i_j_x>gap else pairs_i_j_x
                x_right = gap if W-pairs_i_j_x>gap else W-pairs_i_j_x
                
                y_top = gap if pairs_i_j_y > gap else pairs_i_j_y
                y_bottom = gap if H-pairs_i_j_y>gap else H-pairs_i_j_y
                heatmap_temp = heatmap_i[pairs_i_j_y-y_top:pairs_i_j_y+y_bottom,pairs_i_j_x-x_left:pairs_i_j_x+x_right]
                indice = torch.argmax(heatmap_temp)
                gap_x = indice%(x_left+x_right)-x_left
                gap_y = indice//(x_left+x_right)-y_top
                pairs_[i][j][2*k] = pairs_i[j][2*k] + gap_x.detach().cpu()
                pairs_[i][j][2*k+1] = pairs_i[j][2*k+1] + gap_y.detach().cpu()
    return pairs_
                
            
        
class EmbeddingLoss(nn.Module):
    def __init__(self):
        super(EmbeddingLoss, self).__init__()
        self.margin_loss = nn.MarginRankingLoss(margin = 0.8)

    def forward(self, embeddings,pairs,heatmap=None,scale=1):
        N,C,H,W = embeddings.size()
        embeddings = F.normalize(embeddings)
        embeddings = embeddings.permute(0, 2, 3, 1).contiguous()
        
        embeddings = embeddings.view(N,-1,C)

        loss = 0.
        num = 0
        right = 0
        wrong = 0
        if not heatmap is None:
            pairs = adjust_pairs(pairs,heatmap)
        pairs = ((pairs-scale//2) / scale).long()#pair 由输入图片上的坐标转换成特征图上的坐标 (N，40,4)
        for i in range(N):
            masks_start = []
            masks_end = []
            pairs_i = pairs[i][pairs[i].sum(dim=1)>0]
            for j in range(len(pairs[i])):
                mask_start = torch.zeros(H*W) #当前连线的起始点mask
                mask_end = torch.zeros(H*W)##当前连线的结束点mask
                
                
                ppp = pairs[i][j]
                if ppp.sum()<=0:#去掉加载数据时填充到40长度的0坐标
                    break

                for k in [-2,-1,0,1,2]:
                    for h in [-2,-1,0,1,2]:
                        pp = ppp.clone()
                        pp += torch.tensor([k,h,k,h])
                        flag_start = False#判断顶点加上偏移之后是否有超出特征图边界
                        if pp[1]<0 or pp[1]>=H:
                            flag_start = True
                        if pp[0]<0 or pp[0]>=W:
                            flag_start = True
                            
                        flag_end = False
                        if pp[3]<0 or pp[3]>=H:
                            flag_end = True
                        if pp[2]<0 or pp[2]>=W:
                            flag_end = True
                        
                        if not flag_start:
                            mask_start[pp[0]+pp[1]*W] = 1#起始点mask值赋值为1
                        if not flag_end:
                            mask_end[pp[2]+pp[3]*W] = 1

                if mask_start.sum()==0 or mask_end.sum()==0:
                    continue
                masks_start.append(mask_start)
                masks_end.append(mask_end)
                
            if len(masks_start) <= 1:
                continue
                
            start_points=pairs_i[:,:2].float()
            end_points=pairs_i[:,2:].float()
            dists_start_start = euclidean_dist(start_points,start_points)
            dists_start_end = euclidean_dist(start_points,end_points)
            dists_end_end = euclidean_dist(end_points,end_points)
            dists_end_start = euclidean_dist(end_points,start_points)
            
            for m in range(len(masks_start)):
#                 print(embeddings[m].size())
#                 print(masks_start[m].size())
                
                mask_start_others = torch.zeros_like(masks_start[m])
                mask_end_others = torch.zeros_like(masks_end[m])
                for n in range(len(masks_end)):
                    if n != m:
#                         if dists_start_start[m,n]<3:
#                             masks_start[m]+= masks_start[n]
#                             masks_end[m]+= masks_end[n]
#                         if dists_start_end[m,n]<3:
#                             masks_start[m]+= masks_end[n]
#                             masks_end[m]+= masks_start[n]
#                         if dists_end_end[m,n]<3:
#                             masks_end[m]+= masks_end[n]
#                             masks_start[m]+= masks_start[n]
#                         if dists_end_start[m,n]<3:
#                             masks_end[m]+= masks_start[n]
#                             masks_start[m]+= masks_end[n]
                            
                            
#                         if dists_start_start[m,n]>5 and dists_start_end[m,n]>5:
#                             mask_start_others += masks_start[n]
#                             mask_start_others += masks_end[n]
                            
#                         if dists_end_end[m,n]>5 and dists_end_start[m,n]>5:
#                             mask_end_others += masks_end[n]
#                             mask_end_others += masks_start[n]
                            
                        if dists_start_start[m,n]>5 and dists_start_end[m,n]>5 and dists_end_end[m,n]>5 and dists_end_start[m,n]>5:
                            mask_start_others += masks_start[n]
                            mask_start_others += masks_end[n]

                            mask_end_others += masks_end[n]
                            mask_end_others += masks_start[n]
                
                embedding_start = embeddings[i][torch.where(masks_start[m]>0)]#embeddings[m][masks_start[m].long()]
                embedding_end = embeddings[i][torch.where(masks_end[m]>0)]
                
                dists_pos_start = euclidean_dist(embedding_start,embedding_end)
                dists_pos_start = dists_pos_start.max(1)[0]

                dists_pos_end = euclidean_dist(embedding_end,embedding_start)
                dists_pos_end = dists_pos_end.max(1)[0]
                
                
                embedding_start_others = embeddings[i][torch.where(mask_start_others>0)]
                embedding_end_others = embeddings[i][torch.where(mask_end_others>0)]
                
                if len(embedding_end) == 0 or len(embedding_end_others)==0 or len(embedding_start) == 0 or len(embedding_start_others)==0:
                    return torch.tensor(0.).cuda(),0,0
                    
                dists_neg_start = euclidean_dist(embedding_start,embedding_start_others)
                dists_neg_start = dists_neg_start.min(1)[0]
                right += (dists_pos_start<dists_neg_start).sum().cpu().item()
                wrong += (dists_pos_start>dists_neg_start).sum().cpu().item()
                y = torch.ones_like(dists_pos_start)
                loss_m = self.margin_loss(dists_neg_start, dists_pos_start, y)
                loss += loss_m
                num += 1


                dists_neg_end = euclidean_dist(embedding_end,embedding_end_others)
                dists_neg_end = dists_neg_end.min(1)[0]
                right += (dists_pos_end<dists_neg_end).sum().cpu().item()
                wrong += (dists_pos_end>dists_neg_end).sum().cpu().item()
                y = torch.ones_like(dists_pos_end)
                loss_m = self.margin_loss(dists_neg_end, dists_pos_end, y)
                loss += loss_m
                num += 1

#                 print(loss_m)

        return loss/num,right,wrong
            
class JointsOHKMMSELoss(nn.Module):
    def __init__(self, use_target_weight, topk=8):
        super(JointsOHKMMSELoss, self).__init__()
        self.criterion = nn.MSELoss(reduction='none')
        self.use_target_weight = use_target_weight
        self.topk = topk

    def ohkm(self, loss):
        ohkm_loss = 0.
        for i in range(loss.size()[0]):
            sub_loss = loss[i]
            topk_val, topk_idx = torch.topk(
                sub_loss, k=self.topk, dim=0, sorted=False
            )
            tmp_loss = torch.gather(sub_loss, 0, topk_idx)
            ohkm_loss += torch.sum(tmp_loss) / self.topk
        ohkm_loss /= loss.size()[0]
        return ohkm_loss

    def forward(self, output, target, target_weight):
        batch_size = output.size(0)
        num_joints = output.size(1)
        heatmaps_pred = output.reshape((batch_size, num_joints, -1)).split(1, 1)
        heatmaps_gt = target.reshape((batch_size, num_joints, -1)).split(1, 1)

        loss = []
        for idx in range(num_joints):
            heatmap_pred = heatmaps_pred[idx].squeeze()
            heatmap_gt = heatmaps_gt[idx].squeeze()
            if self.use_target_weight:
                loss.append(0.5 * self.criterion(
                    heatmap_pred.mul(target_weight[:, idx]),
                    heatmap_gt.mul(target_weight[:, idx])
                ))
            else:
                loss.append(
                    0.5 * self.criterion(heatmap_pred, heatmap_gt)
                )

        loss = [l.mean(dim=1).unsqueeze(dim=1) for l in loss]
        loss = torch.cat(loss, dim=1)

        return self.ohkm(loss)