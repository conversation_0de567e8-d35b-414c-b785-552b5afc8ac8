import os
import os.path
import hashlib
import errno
from tqdm import tqdm
import numpy as np
from PIL import Image
import torchvision.transforms.functional as F

def aspect_resize(image,size):
#     print(image.size)
    w,h = image.size
    ratio = min(size[0]/w,size[1]/h)
    image = F.resize(image, (int(h*ratio),int(w*ratio)), Image.BILINEAR)
#     print(image.size,int(w*ratio), int(h*ratio))
    new_image = Image.new('RGB', size, (0,0,0))
    new_image.paste(image, (0, 0, int(w*ratio), int(h*ratio)))
    return new_image,(1/ratio,1/ratio)

def putGaussianMaps(center, accumulate_confid_map, grid_y, grid_x, stride=4,sigma=5):

    stride = 1
    start = stride / 2.0 - 0.5
    y_range = [i for i in range(int(grid_y))]
    x_range = [i for i in range(int(grid_x))]
    xx, yy = np.meshgrid(x_range, y_range)
    xx = xx * stride + start
    yy = yy * stride + start
    d2 = (xx - center[0]) ** 2 + (yy - center[1]) ** 2
    exponent = d2 / 2.0 / sigma / sigma
    mask = exponent <= 4.6052
    cofid_map = np.exp(-exponent)
    # print(cofid_map.max())
    cofid_map = np.multiply(mask, cofid_map)
#     print(cofid_map.shape)
    accumulate_confid_map += cofid_map
    accumulate_confid_map[accumulate_confid_map > 1.0] = 1.0
    
    return accumulate_confid_map

def parse_content(contents,size,stride=1,scales=None):
    #contents:x1,y1,x2,y2,label
    pairs = []
    w,h = size
#     print(size)
    accumulate_confid_map_1 = np.zeros((h//stride,w//stride))
    accumulate_confid_map_2 = np.zeros((h//stride,w//stride))
    accumulate_confid_map_3 = np.zeros((h//stride,w//stride))
    accumulate_confid_map_4 = np.zeros((h//stride,w//stride))#对应最大特征图，高斯半径最小
    for content in contents:
        (x1,y1,x2,y2,label) = (int(ii) for ii in content.split(','))
        if scales:
            x1 = x1/scales[0]
            y1 = y1/scales[1]
            x2 = x2/scales[0]
            y2 = y2/scales[1]

        p1_x = min(w,max(0,x1))
        p1_y = min(h,max(0,y1))
        p2_x = min(w,max(0,x2))
        p2_y = min(h,max(0,y2))
        pair = [p1_x,p1_y,p2_x,p2_y]
        pairs.append(pair)
        accumulate_confid_map_1 = putGaussianMaps((p1_x/stride,p1_y/stride),accumulate_confid_map_1,h//stride,w//stride,stride,sigma=6)
        accumulate_confid_map_1 = putGaussianMaps((p2_x/stride,p2_y/stride),accumulate_confid_map_1,h//stride,w//stride,stride,sigma=6)

        accumulate_confid_map_2 = putGaussianMaps((p1_x/stride,p1_y/stride),accumulate_confid_map_2,h//stride,w//stride,stride,sigma=6)
        accumulate_confid_map_2 = putGaussianMaps((p2_x/stride,p2_y/stride),accumulate_confid_map_2,h//stride,w//stride,stride,sigma=6)

        accumulate_confid_map_3 = putGaussianMaps((p1_x/stride,p1_y/stride),accumulate_confid_map_3,h//stride,w//stride,stride,sigma=6)
        accumulate_confid_map_3 = putGaussianMaps((p2_x/stride,p2_y/stride),accumulate_confid_map_3,h//stride,w//stride,stride,sigma=6)

        accumulate_confid_map_4 = putGaussianMaps((p1_x/stride,p1_y/stride),accumulate_confid_map_4,h//stride,w//stride,stride,sigma=5)
        accumulate_confid_map_4 = putGaussianMaps((p2_x/stride,p2_y/stride),accumulate_confid_map_4,h//stride,w//stride,stride,sigma=5)
            
    accumulate_confid_map_pil_1 = Image.fromarray((accumulate_confid_map_1*255).astype(np.float32))
    accumulate_confid_map_pil_2 = Image.fromarray((accumulate_confid_map_2*255).astype(np.float32))
    accumulate_confid_map_pil_3 = Image.fromarray((accumulate_confid_map_3*255).astype(np.float32))
    accumulate_confid_map_pil_4 = Image.fromarray((accumulate_confid_map_4*255).astype(np.float32))
    return [accumulate_confid_map_pil_1,accumulate_confid_map_pil_2,accumulate_confid_map_pil_3,accumulate_confid_map_pil_4],np.array(pairs)


def gen_bar_updater(pbar):
    def bar_update(count, block_size, total_size):
        if pbar.total is None and total_size:
            pbar.total = total_size
        progress_bytes = count * block_size
        pbar.update(progress_bytes - pbar.n)

    return bar_update


def check_integrity(fpath, md5=None):
    if md5 is None:
        return True
    if not os.path.isfile(fpath):
        return False
    md5o = hashlib.md5()
    with open(fpath, 'rb') as f:
        # read in 1MB chunks
        for chunk in iter(lambda: f.read(1024 * 1024), b''):
            md5o.update(chunk)
    md5c = md5o.hexdigest()
    if md5c != md5:
        return False
    return True


def makedir_exist_ok(dirpath):
    """
    Python2 support for os.makedirs(.., exist_ok=True)
    """
    try:
        os.makedirs(dirpath)
    except OSError as e:
        if e.errno == errno.EEXIST:
            pass
        else:
            raise


def download_url(url, root, filename=None, md5=None):
    """Download a file from a url and place it in root.
    Args:
        url (str): URL to download file from
        root (str): Directory to place downloaded file in
        filename (str): Name to save the file under. If None, use the basename of the URL
        md5 (str): MD5 checksum of the download. If None, do not check
    """
    from six.moves import urllib

    root = os.path.expanduser(root)
    if not filename:
        filename = os.path.basename(url)
    fpath = os.path.join(root, filename)

    makedir_exist_ok(root)

    # downloads file
    if os.path.isfile(fpath) and check_integrity(fpath, md5):
        print('Using downloaded and verified file: ' + fpath)
    else:
        try:
            print('Downloading ' + url + ' to ' + fpath)
            urllib.request.urlretrieve(
                url, fpath,
                reporthook=gen_bar_updater(tqdm(unit='B', unit_scale=True))
            )
        except OSError:
            if url[:5] == 'https':
                url = url.replace('https:', 'http:')
                print('Failed download. Trying https -> http instead.'
                      ' Downloading ' + url + ' to ' + fpath)
                urllib.request.urlretrieve(
                    url, fpath,
                    reporthook=gen_bar_updater(tqdm(unit='B', unit_scale=True))
                )


def list_dir(root, prefix=False):
    """List all directories at a given root
    Args:
        root (str): Path to directory whose folders need to be listed
        prefix (bool, optional): If true, prepends the path to each result, otherwise
            only returns the name of the directories found
    """
    root = os.path.expanduser(root)
    directories = list(
        filter(
            lambda p: os.path.isdir(os.path.join(root, p)),
            os.listdir(root)
        )
    )

    if prefix is True:
        directories = [os.path.join(root, d) for d in directories]

    return directories


def list_files(root, suffix, prefix=False):
    """List all files ending with a suffix at a given root
    Args:
        root (str): Path to directory whose folders need to be listed
        suffix (str or tuple): Suffix of the files to match, e.g. '.png' or ('.jpg', '.png').
            It uses the Python "str.endswith" method and is passed directly
        prefix (bool, optional): If true, prepends the path to each result, otherwise
            only returns the name of the files found
    """
    root = os.path.expanduser(root)
    files = list(
        filter(
            lambda p: os.path.isfile(os.path.join(root, p)) and p.endswith(suffix),
            os.listdir(root)
        )
    )

    if prefix is True:
        files = [os.path.join(root, d) for d in files]

    return files