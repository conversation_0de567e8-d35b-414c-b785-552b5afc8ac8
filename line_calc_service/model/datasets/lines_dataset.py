import json
import os
from collections import namedtuple

import torch
import torch.utils.data as data
from PIL import Image
import numpy as np
from .utils import parse_content,aspect_resize
import torchvision.transforms.functional as F

class Line_datasets(data.Dataset):

    def __init__(self,opts=None, txt='', flag='train',stride=4,transform=None):
        with open(txt,'r') as ff:
            self.lines = ff.readlines()
        self.size = opts.crop_size#h,w
        self.stride = stride
        self.transform = transform

    def __getitem__(self, index):
        """
        Args:
            index (int): Index
        Returns:
            tuple: (image, target) where target is a tuple of all target types if target_type is a list with more
            than one item. Otherwise target is a json object if target_type="polygon", else the image segmentation.
        """
        line = self.lines[index]
        contents = line.strip().split(' ')
        image_path = contents[0]
        image = Image.open(image_path).convert('RGB')
        
        w,h = image.size
        image = F.resize(image, self.size, Image.BILINEAR)
        scales = (w/self.size[0],h/self.size[1])
        
#         image,sacles = aspect_resize(image,self.size)
    
        w,h = image.size 
        if len(contents) == 1:
            target = Image.new('L', (w,h), (0))
            
        targets,pairs = parse_content(contents[1:],image.size,stride=1,scales=scales)
#         print(pairs)
#         print(line,pairs)
        if self.transform:
            image, targets, pairs = self.transform(image, targets, pairs)
        
        pairs = np.array(pairs,dtype=np.int32)
        pairs_ = np.zeros((40,4),dtype=np.int32)
        ll = len(pairs)
        pairs_[:ll] = pairs
        return image, targets,pairs_

    def __len__(self):
        return len(self.lines)
