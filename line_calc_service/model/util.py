import torch
import numpy as np
import cv2
font = cv2.FONT_HERSHEY_SIMPLEX

OUTPUT_STRIDE = 8
NUM_CLASSES = 1
# CKPT = './checkpoints_templess_640_v1/latest_deeplabv3plus_mobilenet_os8_60000.pth'
# CKPT = './checkpoints_templess_448_v3/latest_deeplabv3plus_mobilenet_os8_30000.pth'
# CKPT = './weights/deeplabv3plus_resnet50_448_s8_v4/./latest_deeplabv3plus_resnet50_os8_30000.pth'
# CKPT = './weights/deeplabv3plus_resnet50_640_s8_v10/latest_deeplabv3plus_resnet50_os8_20000.pth'
CKPT = './weights/deeplabv3plus_resnet50_640_s8_v11/./latest_deeplabv3plus_resnet50_os8_20000.pth'
SIZE = (640,640)
DIST_THR= 1.1 #1. #两个特征的欧式举例小于多少时认定为连线成立

POINT_THR = 0.3 #0.2
R = 1
RANGE = 3 #5

MEAN=[0.485, 0.456, 0.406]
STD=[0.229, 0.224, 0.225]

from base_common import LoggerFactory
log = LoggerFactory.get_logger('line_util_test')

def euclidean_dist(x,y):
    m,n = x.size(0),y.size(0)
    xx = torch.pow(x,2).sum(1,keepdim=True).expand(m,n)
    yy = torch.pow(y,2).sum(dim=1,keepdim=True).expand(n,m).t()
    dist = xx + yy
    dist.addmm_(x, y.t(), beta=1, alpha=-2)
    dist = dist.clamp(min=1e-12).sqrt()
    return dist

def get_user_answers(points,features,box_json,image,vis=True):
    top_boxs = box_json['group_top']['boxs']
    top_labels = box_json['group_top']['labels']
    bottom_boxs = box_json['group_bottom']['boxs']
    bottom_labels = box_json['group_bottom']['labels']
    
    top_boxs_mean = np.array(top_boxs).mean(0)
    bottom_boxs_mean = np.array(bottom_boxs).mean(0)
    dist_top_bottom =  (((top_boxs_mean[0]+top_boxs_mean[2])/2-(bottom_boxs_mean[0]+bottom_boxs_mean[2])/2)**2+\
                       ((top_boxs_mean[1]+top_boxs_mean[3])/2-(bottom_boxs_mean[1]+bottom_boxs_mean[3])/2)**2)**.5
#     thr = dist_top_bottom*0.2
    thr = min((top_boxs_mean[2]-top_boxs_mean[0]),(top_boxs_mean[3]-top_boxs_mean[1]))*0.5
    
    boxs = np.concatenate((np.array(top_boxs),np.array(bottom_boxs)))
    labels = np.concatenate((np.array(top_labels),np.array(bottom_labels)))
    
    top_points = []
    top_points_labels = []
    top_points_features = []
    
    bottom_points = []
    bottom_points_labels = []
    bottom_points_features = []
    
    for k,point in enumerate(points):
        boxs_p = boxs.copy()
        boxs_p[:,0::2] -=point[0]
        boxs_p[:,1::2] -=point[1]
        
        x_d = np.zeros(len(boxs_p))
        y_d = np.zeros(len(boxs_p))
        
        x_gap = boxs_p[:,0]*boxs_p[:,2]
        for l in range(len(x_gap)):
            if x_gap[l] > 0:
                x_d[l] = min(abs(boxs_p[l,0]),abs(boxs_p[l,2]))
        y_gap = boxs_p[:,1]*boxs_p[:,3]
        for l in range(len(y_gap)):
            if y_gap[l] > 0:
                y_d[l] = min(abs(boxs_p[l,1]),abs(boxs_p[l,3]))
                
        dist_k = (x_d**2 + y_d**2)**0.5
        
        min_ind = np.argmin(dist_k)

        if dist_k[min_ind] < thr:
            if min_ind < len(top_labels):
                top_points.append(point)
                top_points_labels.append(labels[min_ind])
                top_points_features.append(features[k])
            else:
                bottom_points.append(point)
                bottom_points_labels.append(labels[min_ind])
                bottom_points_features.append(features[k])
    img = cv2.cvtColor(np.asarray(image),cv2.COLOR_RGB2BGR)
    if len(top_points) == 0 or len(bottom_points) == 0:
        return [],img
    dist_top_bottom_features = euclidean_dist(torch.tensor(np.stack(top_points_features)),torch.tensor(np.stack(bottom_points_features)))
#     print(dist_top_bottom_features)
    dist_top_bottom_matches = torch.where(dist_top_bottom_features<DIST_THR)
    
    
    answers = []
    answers_set = []
    for i in range(len(dist_top_bottom_matches[0])):
        answers.append(top_points_labels[dist_top_bottom_matches[0][i].item()] + '-' + bottom_points_labels[dist_top_bottom_matches[1][i].item()])
        if top_points_labels[dist_top_bottom_matches[0][i].item()] + '-' + bottom_points_labels[dist_top_bottom_matches[1][i].item()] in answers_set:
            continue
        answers_set.append(top_points_labels[dist_top_bottom_matches[0][i].item()] + '-' + bottom_points_labels[dist_top_bottom_matches[1][i].item()])
        if vis:
            cv2.line(img, (top_points[dist_top_bottom_matches[0][i]][0],top_points[dist_top_bottom_matches[0][i]][1]),
                     (bottom_points[dist_top_bottom_matches[1][i]][0],bottom_points[dist_top_bottom_matches[1][i]][1]), (0, 0, 255))
            
            for top_box in top_boxs:
#                 print(top_box)
                cv2.rectangle(img, (top_box[0],top_box[1]), (top_box[2],top_box[3]), (0, 0, 255))
            for bottom_box in bottom_boxs:
#                 print(top_box)
                cv2.rectangle(img, (bottom_box[0],bottom_box[1]), (bottom_box[2],bottom_box[3]), (0, 0, 255))
            for m,l in enumerate(top_labels):
                cv2.putText(img,l,(top_boxs[m][0],top_boxs[m][1]),font,1,(0,0,255),2)
            for m,l in enumerate(bottom_labels):
                cv2.putText(img,l,(bottom_boxs[m][0],bottom_boxs[m][1]),font,1,(0,0,255),2)
            
            
    if vis:
        for p in points:
            img = cv2.circle(img,(p[0],p[1]),6,(255,0,0),-1)
            
    return set(answers),img
    
def find_point(img):
    h,w,c = img.shape
    gray = cv2.cvtColor(img,cv2.COLOR_BGR2GRAY) 
    edges = cv2.Canny(gray,50,150,apertureSize = 3)
    lines = cv2.HoughLinesP(edges,2,np.pi/90,100,minLineLength=int(min(h,w)*0.2),maxLineGap=10)
    points = []
    for line in lines:
        for x1,y1,x2,y2 in line:
            points.append([x1,y1])
            points.append([x2,y2])
            cv2.line(img,(x1,y1),(x2,y2),(0,255,0),1)
    cv2.imwrite('1_0.jpg',img)
    return points
    
def get_point(heatmap):
#     print('finding point')
    #heatmap: torch.tensor (1,1,H,W)
    H,W = heatmap.size()

    out_ = heatmap.view(-1)
    value,index = torch.max(out_,0)
    if value < POINT_THR:
        return None,None
    index_x = index%W
    index_y = index//H
    point = [index_x.item(),index_y.item()]
    heatmap[max(0,index_y-RANGE):min(H,index_y+RANGE),max(0,index_x-RANGE):min(W,index_x+RANGE)]=0
    
    return point,heatmap
'''
def get_point(heatmap):
#     print('finding point')
    #heatmap: torch.tensor (1,1,H,W)
    _,_,H,W = heatmap.size()
    thr = POINT_THR
    r = R
    m = torch.nn.ReflectionPad2d(r)
    out = m(heatmap)
#     print(heatmp.size(),out.size())
    conv = torch.nn.AvgPool2d(2*r+1, stride=1, padding=0)
    out = conv(out).squeeze()
#     print(out.size())
    
    
    out_ = out.view(-1)
    value,index = torch.max(out_,0)
    if value < thr:
        return None,None
    index_x = index%W
    index_y = index//H
    point = [index_x.item(),index_y.item()]
    out[max(0,index_y-RANGE*r):min(H,index_y+RANGE*r),max(0,index_x-RANGE*r):min(W,index_x+RANGE*r)]=0
    
    return point,out.unsqueeze(0).unsqueeze(1)
'''

from torchvision.ops import nms
def parse_result(heatmap,embeddings,scales):
    _,_,H,W = heatmap.size()
    thr = POINT_THR
    r = R
    conv = torch.nn.AvgPool2d(2*r+1, stride=1, padding=r)
    out = conv(heatmap).squeeze(0)#H,W
    out = out.detach().cpu().numpy()
    x_nums = np.arange(W)
    y_nums = np.arange(H)
    a = np.meshgrid(x_nums,y_nums)
    b = np.meshgrid(x_nums,y_nums)
    boxs = np.concatenate([a,b])#4,W,H
    
    boxs[:2] -= RANGE*r
    boxs[2:] += RANGE*r
    
    boxs = np.concatenate([boxs,out])#
    boxs = boxs.transpose(1,2,0).reshape((-1,5))
    boxs = boxs[boxs[:,4]>POINT_THR]
    keep = nms(torch.tensor(boxs[:, :4]),torch.tensor(boxs[:, 4]),0.)
    boxs_keep = boxs[keep]
    if len(keep) == 1:
        boxs_keep = [boxs_keep]
    #log.info(boxs_keep)
    if len(boxs_keep) == 0:
        return [],[]
    points = []
    features = []
    for bb in boxs_keep:
        x = int((bb[0]+bb[2])/2)
        y = int((bb[1]+bb[3])/2)
        points.append((int(x*scales[0]*4),int(y*scales[1]*4)))
#             features.append(embeddings[0,y,x,:].detach().cpu().numpy())

        #############################
        #可以尝试不同的周边特征融合方式
        #############################
        N,H,W,C = embeddings.size()
        embs = []
        weights = []
        for k in [-1,0,1]:
            for h in [-1,0,1]:
                pp = [x+k,y+h]
                flag = False
                if pp[1]<0 or pp[1]>=H:
                    flag = True
                if pp[0]<0 or pp[0]>=W:
                    flag = True
                if flag:
                    continue
                emb = embeddings[0,pp[1],pp[0],:]
#                     embs.append(emb.detach().cpu().numpy() )
                embs.append(emb)
        feat = torch.stack(embs).mean(dim=0).detach().cpu().numpy() 
#             feat = torch.tensor(embs).view(-1).detach().cpu().numpy() 
        features.append(feat)
    return points,features
    
    
    
def load_box(an_lists,left_top,size,boxs_keys):
    w,h = size
    x1,y1 = left_top
    box_json = {}
    box_json['group_top'] = {}
    box_json['group_bottom'] = {}
    box_json['group_top']['boxs'] = []
    box_json['group_bottom']['boxs'] = []
    box_json['group_top']['labels'] = []
    box_json['group_bottom']['labels'] = []
    box_json['group_top']['region'] = []
    box_json['group_top']['blockName'] = []
    
    answers = []
    target_boxs = []
    
    for an in an_lists:
        box_json['group_top']['labels'].append(str(an['key']))
        region = an['area'][0]
        box_top = [int(region['x1'] * w), int(region['y1'] * h), int(region['x2'] * w), int(region['y2'] * h)]
        box_json['group_top']['boxs'].append(box_top)
        box_json['group_top']['region'].append(
            [int(region['x1'] * w + x1), int(region['y1'] * h + y1), int(region['x2'] * w + x1),
             int(region['y2'] * h + y1)])
        target_boxs.append([int(region['x1'] * w + x1), int(region['y1'] * h + y1), int(region['x2'] * w + x1),
                            int(region['y2'] * h + y1)])
        blockName_ = ''
        for boxs_key in boxs_keys:
            if boxs_key.startswith(an['blockName']):
                blockName_ = boxs_key
                break
        if len(blockName_) > 0:
            box_json['group_top']['blockName'].append(blockName_)
        else:
            continue

        flag = False
        for bottom in an['answer']:
            region = bottom['sourceAnswer']['ops'][0]['insert']['line']['area']
            if len(region) == 0:
                continue
            box_json['group_bottom']['labels'].append(bottom['value'])
            box_bottom = [int(region['x1'] * w), int(region['y1'] * h), int(region['x2'] * w), int(region['y2'] * h)]
            box_json['group_bottom']['boxs'].append(box_bottom)
            target_boxs.append([int(region['x1'] * w + x1), int(region['y1'] * h + y1), int(region['x2'] * w + x1),
                                int(region['y2'] * h + y1)])
            answers.append(str(an['key']) + '-' + bottom['value'])
            flag = True
        if not flag:
            answers.append(str(an['key']) + '-@')
    target_boxs = np.array(target_boxs)
    x0 = target_boxs[:,0::2].min()
    y0 = target_boxs[:,1::2].min()
    x1 = target_boxs[:,0::2].max()
    y1 = target_boxs[:,1::2].max()
    
    return box_json,answers,(x0,y0,x1,y1)
        
def load_answer(txt):
    with open(txt,'r') as f:
        lines = f.readlines()
        
    answers = {}
    for line in lines:
        img_name = line.strip().split(' ')[0]
        answers[img_name] = line.strip().split(' ')[1:]
    return answers
