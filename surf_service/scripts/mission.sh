#!/bin/bash

export CURRENT_LOG_NAME=surf
WORK_HOME=/usr/servering/correction_runtime
APPLICATION_NAME="server_$CURRENT_LOG_NAME"
APPLICATION_FULL_NAME="$APPLICATION_NAME.py"

start_server() {
  CONF_SCRIPT_PATH=$WORK_HOME/base_common/scripts/get_sys_conf.sh
  chmod +x $CONF_SCRIPT_PATH
  ENVTYPE=$($CONF_SCRIPT_PATH "EnvType")
  CONDA_ENV=$($CONF_SCRIPT_PATH "Conda" $APPLICATION_NAME)
  source ~/anaconda3/etc/profile.d/conda.sh

  conda activate $CONDA_ENV
  export PYTHONPATH=$WORK_HOME
  export EnvType=$ENVTYPE
  export CUDA_VISIBLE_DEVICES=$GPU_NUM
  echo "Starting server..."
  echo "PYTHONPATH: $WORK_HOME"
  echo "ENVIRONMENT: $EnvType"
  echo "CONDA_ENV: $CONDA_ENV"
  echo "CUDA_VISIBLE_DEVICES: $GPU_NUM"
  echo "PORT: $CURRENT_PORT"
  echo "PYTHON_SCRIPT_NAME: $APPLICATION_FULL_NAME"
  nohup python -u $APPLICATION_FULL_NAME $CURRENT_PORT $GPU_NUM $STOP_TIME &> /dev/null &
  echo "Server started."
  sleep 1
  ps -ef|grep $APPLICATION_FULL_NAME
  PID_FILE="$WORK_HOME/pids/$APPLICATION_NAME.pid"
  echo $! > "$PID_FILE"
  echo "Server started with PID(s):"
  cat "$PID_FILE"
}

stop_server() {
  PID=$(lsof -t -i:$CURRENT_PORT)
  if [ -z "$PID" ]; then
      echo "没有找到使用端口 $CURRENT_PORT 的进程."
      exit 0
  fi
  response=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:$CURRENT_PORT/stop)
  echo "停止获取任务响应: $response"
  kill -9 $PID
  if [ $? -eq 0 ]; then
      echo "成功杀死使用端口 $CURRENT_PORT 的进程 (PID: $PID)"
  else
      echo "杀死进程失败."
  fi
}

new_port() {
    local random_port
    while true; do
        random_port=$(( RANDOM % (20000 - 7000 + 1) + 10000 ))
        if ! lsof -i:$random_port > /dev/null; then
            echo $random_port
            return
        fi
    done
}


start_test() {
  CONF_SCRIPT_PATH=$WORK_HOME/base_common/scripts/get_sys_conf.sh
  chmod +x $CONF_SCRIPT_PATH
  ENVTYPE=$($CONF_SCRIPT_PATH "EnvType")
  CONDA_ENV=$($CONF_SCRIPT_PATH "Conda" $APPLICATION_NAME)
  source ~/anaconda3/etc/profile.d/conda.sh

  conda activate $CONDA_ENV
  export PYTHONPATH=$WORK_HOME
  export EnvType=$ENVTYPE
  export CUDA_VISIBLE_DEVICES=$GPU_NUM
  echo "Starting server..."
  echo "PYTHONPATH: $WORK_HOME"
  echo "ENVIRONMENT: $EnvType"
  echo "CONDA_ENV: $CONDA_ENV"
  echo "CUDA_VISIBLE_DEVICES: $GPU_NUM"
  echo "PORT: $CURRENT_PORT"
  echo "PYTHON_SCRIPT_NAME: $APPLICATION_FULL_NAME"
  python -u $APPLICATION_FULL_NAME $CURRENT_PORT $GPU_NUM
  echo "Server started."
  sleep 1
  ps -ef|grep $APPLICATION_FULL_NAME
}

case "$1" in
    test)
        export GPU_NUM=0
        export CURRENT_PORT=$(new_port)
        start_test
      ;;
    start)
        if [ $# -eq 2 ]; then
          export GPU_NUM=$2
          export CURRENT_PORT=$(new_port)
          export STOP_TIME=0
        elif [ $# -eq 3 ]; then
          export GPU_NUM=$2
          export CURRENT_PORT=$3
          export STOP_TIME=0
        elif [ $# -eq 4 ]; then
          export GPU_NUM=$2
          export CURRENT_PORT=$3
          export STOP_TIME=$4
        else
          export GPU_NUM=0
          export CURRENT_PORT=$(new_port)
          export STOP_TIME=0
        fi
        start_server
        ;;
    stop)
        if [ $# -eq 2 ]; then
          export CURRENT_PORT=$2
          stop_server
        else
          #获取当前程序的所有PID
          PIDS=$(pgrep -f $APPLICATION_FULL_NAME)
          if [ -z "$PIDS" ]; then
              echo "没有找到使用脚本 $APPLICATION_FULL_NAME 的进程."
          else
            for PID in $PIDS; do
              PORT=$(lsof -Pan -p $PID -i4 | grep LISTEN | awk '{print $9}' | cut -d: -f2)
              if [ ! -z "$PORT" ]; then
                response=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:$PORT/stop)
                echo "停止获取任务响应: $response"
                kill -9 $PID
                if [ $? -eq 0 ]; then
                    echo "成功杀死相关进程 (PID: $PID)"
                else
                    echo "杀死进程失败."
                fi
              fi
            done
          fi
        fi
        ;;
esac
exit 0