# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime
# @IDE          ：PyCharm 
# <AUTHOR> ya<PERSON><PERSON>
# @Date         ：2024/6/29 9:18 
# @Description  : service_of_surf.py
import copyreg
import json
import time
import traceback
import cv2
import os
import pickle
import random
import numpy as np

from base_common import LoggerFactory, FileUtil, MissionMode, Context
from base_common.service.service_of_base import BaseModelService
log = LoggerFactory.get_logger('AlignService')
def _pickle_keypoint(keypoint):  # : cv2.KeyPoint
    return cv2.KeyPoint, (
        keypoint.pt[0],
        keypoint.pt[1],
        keypoint.size,
        keypoint.angle,
        keypoint.response,
        keypoint.octave,
        keypoint.class_id,
    )

copyreg.pickle(cv2.KeyPoint().__class__, _pickle_keypoint)

class SurfService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.SURF_MISSION
        self.SURF_100 = cv2.xfeatures2d.SURF_create()
        self.SURF_300 = cv2.xfeatures2d.SURF_create(300)
        self.SURF_500 = cv2.xfeatures2d.SURF_create(500)
        self.BF = cv2.BFMatcher()
        self.empty_resp = json.dumps({'img_key': None, 'img_type': None, 'crop_box': [], 'surf_mat': 0})
        try:
            self.GPU_SURF_100 = cv2.cuda.SURF_CUDA_create(_hessianThreshold=100, _nOctaves=4, _nOctaveLayers=3)
            self.GPU_BF = cv2.cuda.DescriptorMatcher_createBFMatcher(cv2.NORM_L2)
            self._GPU_ENABLE = True
        except:
            self._GPU_ENABLE = False
        self.prehot()
    def prehot(self):
        t0 = time.time()
        file_path = f'/mnt/preheat_missions/surf/request.json'
        json_data = []
        if os.path.exists(file_path):
            with open(file_path, 'r') as reader:
                content = reader.read()
                json_data = json.loads(content)
        if len(json_data) > 0:
            count = 0
            for i in range(len(json_data)):
                try:
                    data_json = json_data[i]
                    width = data_json.get('width', None)
                    height = data_json.get('height', None)
                    user_image = cv2.imread(data_json['user_image'])
                    pdf_image = cv2.imread(data_json['pdf_path'])
                    crop_rect = data_json['crop_rect']
                    base_path = data_json['base_path']
                    page_id = data_json['page_id']
                    col_index = data_json['col_index']
                    if col_index != "default" and col_index is not None:
                        crop_x_min, crop_y_min, crop_x_max, crop_y_max = crop_rect
                        pdf_image = pdf_image[crop_y_min:crop_y_max, crop_x_min:crop_x_max, :]
                    res_img, crop_box, surf_mat = self.predict(width, height, user_image, pdf_image, base_path, page_id,
                                                               col_index)
                    break
                except:
                    log.error(f"preheat error {traceback.format_exc()}")
                if count >= 2:
                    break
            log.info(f'SURF预热结束，预热耗时：{time.time() - t0:.4f} sec')

    def do_post(self, data_json):
        t0 = time.time()
        t_surf = None
        try:
            try:
                t1 = time.time()
                width = data_json.get('width', None)
                height = data_json.get('height', None)
                user_image = self.get_image({'img_type': data_json.get('img_type', None), 'img_key': data_json['user_img_key']})
                pdf_image = cv2.imread(data_json['pdf_path'])
                crop_rect = data_json['crop_rect']
                base_path = data_json['base_path']
                page_id = data_json['page_id']
                col_index = data_json['col_index']

                if col_index != "default" and col_index is not None:
                    crop_x_min, crop_y_min, crop_x_max, crop_y_max = crop_rect
                    pdf_image = pdf_image[crop_y_min:crop_y_max, crop_x_min:crop_x_max, :]
                t2 = time.time()
                log.debug(f'解析参数耗时： {(t2-t1):.4f}')
            except:
                log.error(f"get img error {traceback.format_exc()}")
                return self.empty_resp
            ts = time.time()
            res_img, crop_box, surf_mat = self.predict(width, height, user_image, pdf_image, base_path, page_id, col_index)
            t_surf = time.time() - ts
            if res_img is None:
                log.error("SURF 执行失败")
                return self.empty_resp
            img_key, img_type = self.set_image(res_img, img_fmt='.jpg')
            return {
                'img_key': img_key,
                'img_type': img_type,
                'crop_box': crop_box,
                'surf_mat': surf_mat
            }
        except:
            log.error(f'{traceback.format_exc()}')
            return self.empty_resp
        finally:
            t1 = time.time() - t0
            log.debug(f"模型耗时：{t1:.4f} sec")
            Context.print_cost(self.mission_mode, t_surf, t1)

    def predict(self, w, h, user_img, pdf_img, base_path_, page_id, column_id, use_max=True):
        # 图像特征提取及匹配矫正
        # img1:standerd img
        # img2:user img
        MIN_MATCH_COUNT = 120
        MAX_HIGHT = 1800
        h_, w_, _ = user_img.shape

        img2_ori = cv2.resize(user_img, (int(h / h_ * w_), h), cv2.INTER_AREA)
        mean = self._get_vale(user_img).tolist()
        # 图像高度不超过max_height，超过则resize
        if not (use_max and h > MAX_HIGHT):
            MAX_HIGHT = h

        img2_ = cv2.resize(user_img, (int(MAX_HIGHT / h_ * w_), MAX_HIGHT), cv2.INTER_AREA)

        if self._GPU_ENABLE:
            kp1, kp2, matches = self._surf_algo_gpu(pdf_img, img2_)
            good = []
            for m, n in matches:
                if m.distance < 0.75 * n.distance:
                    good.append(m)
        else:            
            kp1, des1 = self._get_or_update_kp(base_path_, page_id, column_id, None)
            if kp1 is None or des1 is None:
                return None, None, 0
            kp1, kp2, matches = self._surf_algo(kp1, des1, img2_)
            good = []
            good_075 = []
            good_08 = []
            for m, n in matches:
                if m.distance > 0.45:
                    continue
                if m.distance < 0.7 * n.distance:
                    good.append(m)
                    continue
                if m.distance < 0.75 * n.distance:
                    good_075.append(m)
                    continue
                if m.distance < 0.8 * n.distance:
                    good_08.append(m)
            if len(good) <= MIN_MATCH_COUNT:
                good.extend(good_075)
            if len(good) <= MIN_MATCH_COUNT:
                good.extend(good_08)

        log.info(f'对齐图像时像素匹配点数： {len(good)}')
        if len(good) >= MIN_MATCH_COUNT:
            src_pts = np.float32([kp1[m.queryIdx].pt for m in good]).reshape(-1, 1, 2) * h / MAX_HIGHT
            dst_pts = np.float32([kp2[m.trainIdx].pt for m in good]).reshape(-1, 1, 2) * h / MAX_HIGHT
            mat, mask = cv2.findHomography(dst_pts, src_pts, cv2.RANSAC, 5.0)
            im1_reg = cv2.warpPerspective(img2_ori, mat, (w, h), borderValue=mean, flags=cv2.INTER_CUBIC)

            # 拉飞
            variance_threshold = 0.3
            mat_v = self._is_image_distorted(mat, dst_pts)
            if mat_v > variance_threshold:
                return None, None, 0

            # 去掉图像周围的白边
            nnz_inds = np.where(im1_reg[:, :, 0] != mean[0])
            if len(nnz_inds[0]) != 0:
                y_min = int(np.min(nnz_inds[0]))
                y_max = int(np.max(nnz_inds[0]))
                x_min = int(np.min(nnz_inds[1]))
                x_max = int(np.max(nnz_inds[1]))
                old_im = im1_reg[y_min:y_max + 1, x_min:x_max + 1]
                return old_im, [x_min, y_min, x_max, y_max], mat_v
            else:
                return im1_reg, [], mat_v
        else:
            return None, None, 0

    def _apply_homography(self, _mat, _points):
        transformed_points = []
        for _point in _points:
            x, y = _point
            point_h = np.array([x, y, 1], dtype=float)
            transformed_point_h = np.dot(_mat, point_h)
            transformed_point_h /= transformed_point_h[2]
            transformed_points.append((transformed_point_h[0], transformed_point_h[1]))
        return transformed_points

    def _distance(self, p1, p2):
        return np.sqrt((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2)

    def _is_image_distorted(self, mat, des_points):
        des_points = des_points.squeeze()
        num_points = 50
        des_l = des_points.shape[0]
        step = des_l // num_points
        ratios = []
        for i in range(num_points):
            index = i * step
            if index < des_l:
                point = des_points[index]
                x = point[0]
                y = point[1]
                neighbors = [(x, y - 50), (x + 50, y)]
                all_points = [point] + neighbors
                transformed_all_points = self._apply_homography(mat, all_points)
                transformed_point = transformed_all_points[0]
                transformed_neighbors = transformed_all_points[1:]
                for transformed_neighbor in transformed_neighbors:
                    dist = self._distance(transformed_point, transformed_neighbor)
                    ratio = dist / 50.0
                    ratios.append(ratio)
        ratios = np.array(ratios)
        variance = np.var(ratios)
        log.info(f"矫正方差为: {variance}")
        return variance
    def _surf_algo_gpu(self, pdf_img, user_img):
        t0 = time.time()
        MAX_HIGHT = 1800
        h, w, _ = pdf_img.shape
        if h > MAX_HIGHT:
            pdf_img = cv2.resize(pdf_img, (int(MAX_HIGHT / h * w), MAX_HIGHT), cv2.INTER_AREA)

        user_gray_img = cv2.cvtColor(user_img, cv2.COLOR_BGR2GRAY)
        user_gpu_img = cv2.cuda.GpuMat()
        user_gpu_img.upload(user_gray_img)

        pdf_gray_img = cv2.cvtColor(pdf_img, cv2.COLOR_BGR2GRAY)
        pdf_gpu_img = cv2.cuda.GpuMat()
        pdf_gpu_img.upload(pdf_gray_img)

        kp1_gpu, des1_gpu = self.GPU_SURF_100.detectWithDescriptors(pdf_gpu_img, None)
        kp2_gpu, des2_gpu = self.GPU_SURF_100.detectWithDescriptors(user_gpu_img, None)

        matches = self.GPU_BF.knnMatch(des1_gpu, des2_gpu, 2)

        # 下载结果到CPU
        kp1 = self.GPU_SURF_100.downloadKeypoints(kp1_gpu)
        kp2 = self.GPU_SURF_100.downloadKeypoints(kp2_gpu)
        kp1_gpu.release()
        des1_gpu.release()

        kp2_gpu.release()
        des2_gpu.release()

        user_gpu_img.release()
        pdf_gpu_img.release()

        log.debug(f'SURF algo end {time.time() - t0:.4f} sec')
        return kp1, kp2, matches
    def _surf_algo(self, kp1, des1, img2_):
        """
        log.info(f"_surf_algo: len(kp1): {len(kp1)}")
        kp2, des2 = self.SURF_100.detectAndCompute(img2_, None)
        matches = self.BF.knnMatch(des1, des2, k=2)
        return kp1, kp2, matches
        """
        if len(kp1) < 10000:
            kp2, des2 = self.SURF_100.detectAndCompute(img2_, None)
        elif len(kp1) < 20000:
            kp2, des2 = self.SURF_300.detectAndCompute(img2_, None)
        else:
            kp2, des2 = self.SURF_500.detectAndCompute(img2_, None)
        num = 5000
        #log.info(f"_surf_algo: len(kp1): {len(kp1)}")
        if len(kp1) > num:
            num = int((len(kp1)-num)*0.3) + num
            inds = list(range(len(kp1)))
            random.shuffle(inds)
            des1 = des1[inds[:num]]
            kp1_ = [kp1[ind] for ind in inds[:num]]
            kp1 = kp1_
        matches = self.BF.knnMatch(des1, des2, k=2)
        return kp1, kp2, matches

    def _get_vale(self, x):
        return (np.mean(x[:, 0], axis=0, dtype=np.int) + np.mean(x[:, -1], axis=0, dtype=np.int)) // 2

    def _get_or_update_kp(self, base_path_, page_id, column_id, img_ref):
        col_path = "default" if column_id is None else str(column_id)
        key_pt_storage_path = os.path.join(base_path_, f"xfeatures2d/{page_id}_{col_path}.pkl")

        if os.path.exists(key_pt_storage_path):
            content = FileUtil.read_file(key_pt_storage_path, "rb")
            kp1, des1 = pickle.loads(content)
            return kp1, des1
        if img_ref is None:
            log.error(f"{key_pt_storage_path} surf pkl文件不存在！")
            return None, None
        kp1, des1 = self.SURF_100.detectAndCompute(img_ref, None)
        return kp1, des1
